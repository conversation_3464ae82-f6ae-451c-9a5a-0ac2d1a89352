# Instruções para Implementação da Funcionalidade de Upload de Fichas

## Resumo da Funcionalidade

Foi implementada uma nova funcionalidade que permite aos clubes fazer upload de fichas já prontas (PDF, Word, Excel, etc.) ao invés de criar apenas fichas HTML no sistema. Isso permite que clubes usem suas fichas padronizadas existentes.

## Passos para Implementação Manual

### 1. Executar Migração do Banco de Dados

Execute o seguinte SQL no Supabase para adicionar o campo `file_url` à tabela `club_form_templates`:

```sql
-- Adicionar campo file_url à tabela club_form_templates
ALTER TABLE club_form_templates 
ADD COLUMN file_url TEXT;

-- Adicionar comentário para documentar o campo
COMMENT ON COLUMN club_form_templates.file_url IS 'URL do arquivo uploadado (PDF, Word, Excel, etc.) quando o template é baseado em arquivo ao invés de conteúdo HTML';

-- Criar índice para melhorar performance
CREATE INDEX idx_club_form_templates_file_url ON club_form_templates(file_url) WHERE file_url IS NOT NULL;
```

### 2. Verificar Bucket de Storage

O sistema usa o bucket `playerdocuments` que já existe no projeto. Não é necessário criar novos buckets.

### 3. Arquivos Criados/Modificados

Os seguintes arquivos foram criados ou modificados:

#### Novos Arquivos:
- `src/api/formTemplateStorage.ts` - API para upload de arquivos
- `src/components/forms/FormTemplateUploadDialog.tsx` - Modal de upload
- `supabase/migrations/20250127000000_add_file_url_to_club_form_templates.sql` - Migração
- `sql/add_file_url_to_club_form_templates.sql` - Script SQL
- `docs/upload-fichas-funcionalidade.md` - Documentação

#### Arquivos Modificados:
- `src/api/clubFormTemplates.ts` - Adicionado suporte ao campo file_url
- `src/components/forms/FormTemplatesManager.tsx` - Adicionado botão de upload
- `src/components/forms/FormTemplateDownloadLinks.tsx` - Suporte a arquivos uploadados
- `src/components/forms/FormTemplatePreviewDialogNew.tsx` - Preview de arquivos

### 4. Funcionalidades Implementadas

#### Para o Clube:
1. **Botão "Upload de Ficha"** no gerenciador de fichas
2. **Modal de upload** com validação de tipos de arquivo
3. **Suporte a múltiplos formatos**: PDF, Word, Excel, TXT
4. **Visualização diferenciada** de fichas com arquivo (ícone especial)
5. **Preview direto** do arquivo no navegador
6. **Download direto** do arquivo original

#### Para o Atleta:
1. **Download automático** de fichas uploadadas durante pré-cadastro
2. **Compatibilidade** com sistema existente de fichas HTML
3. **Interface unificada** para todos os tipos de ficha

### 5. Validações e Segurança

- **Tipos de arquivo permitidos**: PDF, DOC, DOCX, XLS, XLSX, TXT
- **Tamanho máximo**: 10MB por arquivo
- **Sanitização**: Nomes de arquivo são sanitizados
- **Permissões**: Apenas usuários autorizados podem fazer upload
- **Armazenamento seguro**: Arquivos ficam no Supabase Storage

### 6. Como Testar

1. **Acesse o menu "Gerenciar Fichas"**
2. **Clique em "Upload de Ficha"**
3. **Preencha os dados e faça upload de um arquivo PDF**
4. **Verifique se a ficha aparece na lista com ícone de arquivo**
5. **Teste o preview e download**
6. **Acesse o pré-cadastro e verifique se a ficha está disponível**

### 7. Compatibilidade

- ✅ **Backward Compatible**: Fichas HTML existentes continuam funcionando
- ✅ **Coexistência**: Clubes podem ter fichas HTML e uploadadas
- ✅ **Sem migração necessária**: Dados existentes não são afetados

### 8. Monitoramento

Monitore os seguintes pontos após a implementação:

- **Uploads bem-sucedidos** no Supabase Storage
- **Performance** das consultas com o novo índice
- **Uso de storage** pelos clubes
- **Feedback dos usuários** sobre a nova funcionalidade

### 9. Rollback (se necessário)

Para reverter as mudanças:

```sql
-- Remover o campo file_url (CUIDADO: isso apagará dados!)
ALTER TABLE club_form_templates DROP COLUMN file_url;

-- Remover o índice
DROP INDEX idx_club_form_templates_file_url;
```

### 10. Próximos Passos

Após a implementação, considere:

1. **Documentação para usuários finais**
2. **Treinamento da equipe de suporte**
3. **Monitoramento de uso e feedback**
4. **Possíveis melhorias baseadas no uso real**

## Contato

Para dúvidas sobre a implementação, consulte a documentação em `docs/upload-fichas-funcionalidade.md` ou entre em contato com a equipe de desenvolvimento.