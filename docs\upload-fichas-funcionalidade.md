# Funcionalidade de Upload de Fichas

## Visão Geral

A funcionalidade de upload de fichas permite que os clubes façam upload de documentos já prontos (PDF, Word, Excel, etc.) ao invés de criar fichas do zero no sistema. Isso garante uniformidade e praticidade, já que muitos clubes já têm suas fichas padronizadas.

## Como Funciona

### 1. Upload de Fichas

No menu "Gerenciar Fichas", além do botão "Nova Ficha" (para criar fichas HTML), agora existe o botão "Upload de Ficha":

- **Formatos Aceitos**: PDF, Word (.doc, .docx), Excel (.xls, .xlsx), TXT
- **<PERSON><PERSON><PERSON>**: 10MB por arquivo
- **Tipos de Ficha**: Pré-cadastro, Moradia, Termos de Responsabilidade, Personalizado

### 2. Processo de Upload

1. Clique em "Upload de Ficha"
2. Preencha o nome da ficha
3. Adicione uma descrição (opcional)
4. Selecione o tipo de ficha
5. Faça upload do arquivo
6. Clique em "Criar Ficha"

### 3. Visualização e Download

- **Fichas com arquivo**: Mostram um ícone de arquivo ao lado do nome
- **Preview**: Abre o arquivo diretamente no navegador
- **Download/Impressão**: 
  - **PDF**: Abre automaticamente para impressão
  - **Word/Excel/TXT**: Faz download para impressão local
- **Edição**: Permite substituir, remover ou manter o arquivo atual

### 4. Disponibilidade para Atletas

Quando um atleta acessa o pré-cadastro:
- As fichas uploadadas ficam disponíveis para download
- O atleta pode baixar, preencher e enviar de volta
- Mantém a uniformidade do documento original do clube

## Implementação Técnica

### Banco de Dados

```sql
-- Novo campo adicionado à tabela club_form_templates
ALTER TABLE club_form_templates 
ADD COLUMN file_url TEXT;
```

### Armazenamento

- **Bucket**: `playerdocuments` no Supabase Storage (usando bucket existente)
- **Estrutura**: `form-templates/club-{id}/{nome-arquivo}-{uuid}.{extensao}`
- **Segurança**: URLs públicas para acesso direto

### Componentes Criados

1. **FormTemplateUploadDialog**: Modal para upload de fichas
2. **FormTemplateEditDialog**: Modal para edição de fichas com gerenciamento de arquivos
3. **formTemplateStorage.ts**: API para upload/delete de arquivos
4. **printUtils.ts**: Utilitários para impressão automática
5. **Atualizações nos componentes existentes**: Preview, download, listagem

## Benefícios

1. **Uniformidade**: Clubes podem usar suas fichas padronizadas
2. **Praticidade**: Não precisa recriar fichas no sistema
3. **Flexibilidade**: Suporta múltiplos formatos de arquivo
4. **Compatibilidade**: Funciona junto com o sistema existente de fichas HTML

## Fluxo do Usuário

### Para o Clube:
1. Acessa "Gerenciar Fichas"
2. Clica em "Upload de Ficha"
3. Faz upload do documento
4. Ficha fica disponível para atletas

### Para o Atleta:
1. Acessa link de pré-cadastro
2. Vê fichas disponíveis para download
3. Baixa, preenche e envia documentos
4. Processo normal de pré-cadastro continua

## Considerações de Segurança

- **Validação de tipos**: Apenas formatos permitidos
- **Limite de tamanho**: 10MB máximo
- **Sanitização**: Nomes de arquivo são sanitizados
- **Permissões**: Apenas usuários autorizados podem fazer upload

## Migração e Compatibilidade

- **Backward Compatible**: Fichas HTML existentes continuam funcionando
- **Migração**: Não é necessária migração de dados existentes
- **Coexistência**: Clubes podem ter fichas HTML e uploadadas simultaneamente
## 
Funcionalidades de Edição

### Gerenciamento de Arquivos na Edição

Ao editar uma ficha com arquivo, o usuário pode:

1. **Manter arquivo atual**: Não altera o arquivo anexado
2. **Substituir arquivo**: Remove o arquivo atual e faz upload de um novo
3. **Remover arquivo**: Remove o arquivo e converte para ficha HTML editável

### Impressão Automática

O sistema detecta automaticamente o tipo de arquivo e:

- **PDF**: Abre automaticamente para impressão no navegador
- **Word (.doc, .docx)**: Faz download para impressão local
- **Excel (.xls, .xlsx)**: Faz download para impressão local  
- **TXT**: Abre para impressão no navegador

### Interface Intuitiva

- **Ícones diferenciados**: Printer para PDFs, Download para outros formatos
- **Feedback claro**: Mensagens específicas para cada tipo de ação
- **Fallbacks**: Se a impressão automática falhar, faz download automaticamente