import React from 'react';
import { StatusBar } from 'react-native';
import { PaperProvider } from 'react-native-paper';
import { QueryClientProvider } from '@tanstack/react-query';
import { AuthProvider } from '@/contexts/AuthContext';
import AppNavigator from '@/navigation/AppNavigator';
import { lightTheme } from '@/theme';
import { queryClient } from '@/services/queryClient';

// Configurar React Query
import { QueryClient } from '@tanstack/react-query';

const queryClientInstance = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes (anteriormente cacheTime)
      retry: 3,
      refetchOnWindowFocus: false,
    },
  },
});

export default function App() {
  return (
    <QueryClientProvider client={queryClientInstance}>
      <PaperProvider theme={lightTheme}>
        <AuthProvider>
          <StatusBar 
            barStyle="light-content" 
            backgroundColor="#1976d2" 
            translucent={false}
          />
          <AppNavigator />
        </AuthProvider>
      </PaperProvider>
    </QueryClientProvider>
  );
}
