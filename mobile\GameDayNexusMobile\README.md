# 📱 Game Day Nexus Mobile

Aplicativo mobile React Native do sistema ERP esportivo Game Day Nexus Platform.

## 🚀 Início R<PERSON>pido

### Pré-requisitos
- Node.js 18+
- React Native CLI
- Android Studio (para Android)
- Xcode (para iOS)
- Supabase account

### Instalação

1. **Instalar dependências**:
```bash
npm install
```

2. **Configurar variáveis de ambiente**:
```bash
cp .env.example .env
# Editar .env com suas credenciais do Supabase
```

3. **Instalar dependências iOS** (apenas para iOS):
```bash
cd ios && pod install && cd ..
```

4. **Executar o aplicativo**:

**Android**:
```bash
npm run android
```

**iOS**:
```bash
npm run ios
```

## 📁 Estrutura do Projeto

```
src/
├── components/          # Componentes reutilizáveis
│   ├── common/         # Componentes comuns
│   ├── forms/          # Componentes de formulário
│   ├── charts/         # Gráficos e visualizações
│   └── ui/             # Componentes de interface
├── screens/            # Telas do aplicativo
│   ├── auth/           # Telas de autenticação
│   ├── dashboard/      # Dashboard principal
│   ├── athletes/       # Módulo de atletas
│   ├── matches/        # Módulo de partidas
│   ├── training/       # Módulo de treinamentos
│   ├── medical/        # Módulo médico
│   ├── financial/      # Módulo financeiro
│   ├── administrative/ # Módulo administrativo
│   ├── inventory/      # Módulo de estoque
│   └── nutrition/      # Módulo de alimentação
├── navigation/         # Configuração de navegação
├── services/           # Serviços e APIs
│   ├── api/           # Serviços de API
│   ├── auth/          # Serviços de autenticação
│   ├── storage/       # Armazenamento local
│   └── notifications/ # Serviços de notificação
├── contexts/           # Contexts React
├── hooks/              # Custom hooks
├── utils/              # Utilitários
├── types/              # Tipos TypeScript
├── theme/              # Configuração de tema
└── assets/             # Imagens, ícones, etc.
```

## 🛠️ Tecnologias Utilizadas

- **React Native** - Framework principal
- **TypeScript** - Tipagem estática
- **React Navigation 6** - Navegação
- **React Native Paper** - UI Components
- **Supabase** - Backend e autenticação
- **React Query** - Gerenciamento de estado servidor
- **React Hook Form** - Formulários
- **Zod** - Validação de esquemas

## 🎨 Funcionalidades Implementadas

### ✅ Fase 1 - Configuração Inicial
- [x] Estrutura do projeto
- [x] Configuração TypeScript
- [x] Dependências principais
- [x] Configuração Supabase
- [x] Sistema de tema
- [x] Navegação básica

### ✅ Sistema de Autenticação
- [x] Tela de Login
- [x] Tela de Recuperação de Senha
- [x] Context de Autenticação
- [x] Persistência de Sessão
- [x] Proteção de Rotas

### ✅ Dashboard
- [x] Dashboard básico
- [x] Estatísticas rápidas
- [x] Ações rápidas
- [x] Navegação drawer

### 🚧 Em Desenvolvimento
- [ ] Módulo de Atletas
- [ ] Módulo de Partidas
- [ ] Módulo de Treinamentos
- [ ] Módulo Médico
- [ ] Módulo Financeiro

## 🔧 Scripts Disponíveis

```bash
# Desenvolvimento
npm run android          # Executar no Android
npm run ios             # Executar no iOS
npm start               # Iniciar Metro bundler

# Qualidade de Código
npm run lint            # Verificar código
npm run type-check      # Verificar tipos TypeScript
npm test                # Executar testes

# Limpeza
npm run clean           # Limpar cache
npm run reset-cache     # Reset Metro cache
```

## 🔐 Configuração de Ambiente

### Supabase
1. Criar projeto no [Supabase](https://supabase.com)
2. Copiar URL e Anon Key
3. Configurar no arquivo `.env`

### Firebase (Notificações Push)
1. Criar projeto no [Firebase Console](https://console.firebase.google.com)
2. Configurar para Android e iOS
3. Baixar arquivos de configuração
4. Configurar no arquivo `.env`

## 📱 Telas Implementadas

### Autenticação
- **LoginScreen** - Login com email/senha
- **ForgotPasswordScreen** - Recuperação de senha

### Principal
- **DashboardScreen** - Dashboard principal
- **LoadingScreen** - Tela de carregamento

### Navegação
- **AuthNavigator** - Navegação de autenticação
- **MainNavigator** - Navegação principal com drawer

## 🎯 Próximos Passos

1. **Implementar Módulo de Atletas**
   - Lista de atletas
   - Perfil do atleta
   - Cadastro/edição

2. **Implementar Módulo de Partidas**
   - Lista de partidas
   - Escalação tática
   - Eventos em tempo real

3. **Adicionar Notificações Push**
   - Configurar Firebase
   - Implementar notificações

4. **Implementar Modo Offline**
   - Cache local
   - Sincronização

## 🐛 Problemas Conhecidos

- Nenhum problema conhecido no momento

## 📞 Suporte

Para suporte técnico, entre em contato com a equipe de desenvolvimento.

## 📄 Licença

Este projeto é propriedade da Game Day Nexus Platform.

---

**Status**: 🟡 Em desenvolvimento  
**Versão**: 1.0.0  
**Última atualização**: Agosto 2025
