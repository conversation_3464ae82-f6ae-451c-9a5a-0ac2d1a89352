import React from 'react';
import { View, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { Card, Text, Chip, useTheme, Avatar } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { spacing, statusColors, positionColors } from '@/theme';
import { Athlete } from '@/types/athletes';

interface AthleteCardProps {
  athlete: Athlete;
  onPress: () => void;
  showDetails?: boolean;
}

export default function AthleteCard({ 
  athlete, 
  onPress, 
  showDetails = true 
}: AthleteCardProps) {
  const theme = useTheme();

  const getStatusColor = () => {
    return statusColors[athlete.status] || theme.colors.outline;
  };

  const getPositionColor = () => {
    if (!athlete.position) return theme.colors.primary;
    return positionColors[athlete.position.type] || theme.colors.primary;
  };

  const getStatusLabel = () => {
    switch (athlete.status) {
      case 'active':
        return 'Ativo';
      case 'inactive':
        return 'Inativo';
      case 'suspended':
        return 'Suspenso';
      case 'transferred':
        return 'Transferido';
      case 'loaned':
        return 'Emprestado';
      default:
        return athlete.status;
    }
  };

  const calculateAge = () => {
    const birthDate = new Date(athlete.birth_date);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    return age;
  };

  const renderAvatar = () => {
    if (athlete.photo_url) {
      return (
        <Image
          source={{ uri: athlete.photo_url }}
          style={styles.avatar}
        />
      );
    }

    return (
      <Avatar.Text
        size={60}
        label={athlete.name.split(' ').map(n => n[0]).join('').substring(0, 2)}
        style={[styles.avatarText, { backgroundColor: getPositionColor() }]}
      />
    );
  };

  return (
    <TouchableOpacity onPress={onPress} style={styles.container}>
      <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
        <Card.Content style={styles.content}>
          <View style={styles.header}>
            <View style={styles.avatarContainer}>
              {renderAvatar()}
              {athlete.jersey_number && (
                <View style={[styles.jerseyBadge, { backgroundColor: getPositionColor() }]}>
                  <Text variant="labelSmall" style={styles.jerseyNumber}>
                    {athlete.jersey_number}
                  </Text>
                </View>
              )}
            </View>

            <View style={styles.info}>
              <Text variant="titleMedium" style={styles.name} numberOfLines={1}>
                {athlete.name}
              </Text>
              
              <View style={styles.details}>
                {athlete.position && (
                  <Chip
                    style={[styles.positionChip, { backgroundColor: `${getPositionColor()}20` }]}
                    textStyle={[styles.chipText, { color: getPositionColor() }]}
                    compact
                  >
                    {athlete.position.abbreviation}
                  </Chip>
                )}
                
                <Chip
                  style={[styles.statusChip, { backgroundColor: `${getStatusColor()}20` }]}
                  textStyle={[styles.chipText, { color: getStatusColor() }]}
                  compact
                >
                  {getStatusLabel()}
                </Chip>
              </View>

              {showDetails && (
                <View style={styles.additionalInfo}>
                  <View style={styles.infoItem}>
                    <Icon name="cake" size={14} color={theme.colors.outline} />
                    <Text variant="bodySmall" style={styles.infoText}>
                      {calculateAge()} anos
                    </Text>
                  </View>
                  
                  {athlete.category && (
                    <View style={styles.infoItem}>
                      <Icon name="group" size={14} color={theme.colors.outline} />
                      <Text variant="bodySmall" style={styles.infoText}>
                        {athlete.category.name}
                      </Text>
                    </View>
                  )}
                </View>
              )}
            </View>

            <View style={styles.actions}>
              <Icon name="chevron-right" size={24} color={theme.colors.outline} />
            </View>
          </View>
        </Card.Content>
      </Card>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    marginHorizontal: spacing.md,
    marginBottom: spacing.sm,
  },
  card: {
    elevation: 2,
  },
  content: {
    padding: spacing.md,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatarContainer: {
    position: 'relative',
    marginRight: spacing.md,
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
  },
  avatarText: {
    backgroundColor: '#1976d2',
  },
  jerseyBadge: {
    position: 'absolute',
    bottom: -4,
    right: -4,
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#ffffff',
  },
  jerseyNumber: {
    color: '#ffffff',
    fontWeight: 'bold',
    fontSize: 10,
  },
  info: {
    flex: 1,
  },
  name: {
    fontWeight: '600',
    marginBottom: spacing.xs,
  },
  details: {
    flexDirection: 'row',
    marginBottom: spacing.sm,
  },
  positionChip: {
    height: 24,
    marginRight: spacing.xs,
  },
  statusChip: {
    height: 24,
  },
  chipText: {
    fontSize: 10,
    fontWeight: '600',
  },
  additionalInfo: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: spacing.md,
    marginBottom: spacing.xs,
  },
  infoText: {
    marginLeft: spacing.xs,
    opacity: 0.7,
  },
  actions: {
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: spacing.sm,
  },
});
