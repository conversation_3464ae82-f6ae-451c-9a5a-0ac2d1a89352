import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import {
  Modal,
  Portal,
  Text,
  Button,
  Chip,
  Divider,
  useTheme,
} from 'react-native-paper';
import { spacing } from '@/theme';
import { AthleteFilters } from '@/types/athletes';

interface FilterBottomSheetProps {
  visible: boolean;
  onDismiss: () => void;
  onApply: (filters: AthleteFilters) => void;
  currentFilters: AthleteFilters;
}

export default function FilterBottomSheet({
  visible,
  onDismiss,
  onApply,
  currentFilters,
}: FilterBottomSheetProps) {
  const theme = useTheme();
  const [filters, setFilters] = useState<AthleteFilters>(currentFilters);

  useEffect(() => {
    setFilters(currentFilters);
  }, [currentFilters, visible]);

  const categories = [
    { id: 'cat1', name: 'Sub-20' },
    { id: 'cat2', name: 'Sub-17' },
    { id: 'cat3', name: 'Sub-15' },
    { id: 'cat4', name: 'Profissional' },
  ];

  const positions = [
    { id: 'pos1', name: '<PERSON><PERSON><PERSON>', type: 'goalkeeper' },
    { id: 'pos2', name: 'Zagueiro', type: 'defender' },
    { id: 'pos3', name: 'Lateral', type: 'lateral' },
    { id: 'pos4', name: 'Meio-campo', type: 'midfielder' },
    { id: 'pos5', name: 'Atacante', type: 'forward' },
  ];

  const statuses = [
    { id: 'active', name: 'Ativo' },
    { id: 'inactive', name: 'Inativo' },
    { id: 'suspended', name: 'Suspenso' },
    { id: 'transferred', name: 'Transferido' },
    { id: 'loaned', name: 'Emprestado' },
  ];

  const handleCategorySelect = (categoryId: string) => {
    setFilters(prev => ({
      ...prev,
      category_id: prev.category_id === categoryId ? undefined : categoryId,
    }));
  };

  const handlePositionSelect = (positionId: string) => {
    setFilters(prev => ({
      ...prev,
      position_id: prev.position_id === positionId ? undefined : positionId,
    }));
  };

  const handleStatusSelect = (status: string) => {
    setFilters(prev => ({
      ...prev,
      status: prev.status === status ? undefined : status as any,
    }));
  };

  const handleClearFilters = () => {
    setFilters({});
  };

  const handleApply = () => {
    onApply(filters);
  };

  const getActiveFiltersCount = () => {
    return Object.values(filters).filter(value => value).length;
  };

  return (
    <Portal>
      <Modal
        visible={visible}
        onDismiss={onDismiss}
        contentContainerStyle={[
          styles.container,
          { backgroundColor: theme.colors.surface }
        ]}
      >
        <View style={styles.header}>
          <Text variant="titleLarge" style={styles.title}>
            Filtros
          </Text>
          <Button
            mode="text"
            onPress={handleClearFilters}
            disabled={getActiveFiltersCount() === 0}
          >
            Limpar
          </Button>
        </View>

        <Divider />

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Categoria */}
          <View style={styles.section}>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Categoria
            </Text>
            <View style={styles.chipsContainer}>
              {categories.map((category) => (
                <Chip
                  key={category.id}
                  selected={filters.category_id === category.id}
                  onPress={() => handleCategorySelect(category.id)}
                  style={styles.chip}
                  showSelectedOverlay
                >
                  {category.name}
                </Chip>
              ))}
            </View>
          </View>

          <Divider style={styles.divider} />

          {/* Posição */}
          <View style={styles.section}>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Posição
            </Text>
            <View style={styles.chipsContainer}>
              {positions.map((position) => (
                <Chip
                  key={position.id}
                  selected={filters.position_id === position.id}
                  onPress={() => handlePositionSelect(position.id)}
                  style={styles.chip}
                  showSelectedOverlay
                >
                  {position.name}
                </Chip>
              ))}
            </View>
          </View>

          <Divider style={styles.divider} />

          {/* Status */}
          <View style={styles.section}>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Status
            </Text>
            <View style={styles.chipsContainer}>
              {statuses.map((status) => (
                <Chip
                  key={status.id}
                  selected={filters.status === status.id}
                  onPress={() => handleStatusSelect(status.id)}
                  style={styles.chip}
                  showSelectedOverlay
                >
                  {status.name}
                </Chip>
              ))}
            </View>
          </View>
        </ScrollView>

        <Divider />

        <View style={styles.footer}>
          <Button
            mode="outlined"
            onPress={onDismiss}
            style={styles.footerButton}
          >
            Cancelar
          </Button>
          <Button
            mode="contained"
            onPress={handleApply}
            style={styles.footerButton}
          >
            Aplicar {getActiveFiltersCount() > 0 && `(${getActiveFiltersCount()})`}
          </Button>
        </View>
      </Modal>
    </Portal>
  );
}

const styles = StyleSheet.create({
  container: {
    margin: spacing.md,
    borderRadius: 12,
    maxHeight: '80%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.lg,
    paddingBottom: spacing.md,
  },
  title: {
    fontWeight: '600',
  },
  content: {
    maxHeight: 400,
  },
  section: {
    padding: spacing.lg,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: spacing.md,
  },
  chipsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  chip: {
    marginBottom: spacing.sm,
  },
  divider: {
    marginHorizontal: spacing.lg,
  },
  footer: {
    flexDirection: 'row',
    padding: spacing.lg,
    paddingTop: spacing.md,
    gap: spacing.md,
  },
  footerButton: {
    flex: 1,
  },
});
