import React from 'react';
import { StyleSheet, TouchableOpacity } from 'react-native';
import { Card, Text, useTheme } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { spacing } from '@/theme';

interface QuickActionButtonProps {
  title: string;
  icon: string;
  color?: string;
  onPress: () => void;
  disabled?: boolean;
}

export default function QuickActionButton({
  title,
  icon,
  color = '#1976d2',
  onPress,
  disabled = false,
}: QuickActionButtonProps) {
  const theme = useTheme();

  return (
    <TouchableOpacity 
      onPress={onPress} 
      disabled={disabled}
      style={[styles.container, disabled && styles.disabled]}
    >
      <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
        <Card.Content style={styles.content}>
          <Icon 
            name={icon} 
            size={32} 
            color={disabled ? theme.colors.outline : color} 
          />
          <Text 
            variant="titleSmall" 
            style={[
              styles.title, 
              { color: disabled ? theme.colors.outline : theme.colors.onSurface }
            ]}
          >
            {title}
          </Text>
        </Card.Content>
      </Card>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    margin: spacing.xs,
  },
  disabled: {
    opacity: 0.6,
  },
  card: {
    elevation: 1,
  },
  content: {
    alignItems: 'center',
    padding: spacing.md,
    minHeight: 80,
    justifyContent: 'center',
  },
  title: {
    marginTop: spacing.sm,
    textAlign: 'center',
    fontWeight: '500',
  },
});
