import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { Card, Text, useTheme } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { spacing } from '@/theme';

interface StatWidgetProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: string;
  color?: string;
  onPress?: () => void;
  loading?: boolean;
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

export default function StatWidget({
  title,
  value,
  subtitle,
  icon,
  color = '#1976d2',
  onPress,
  loading = false,
  trend,
}: StatWidgetProps) {
  const theme = useTheme();

  const formatValue = (val: string | number) => {
    if (typeof val === 'number') {
      return val.toLocaleString();
    }
    return val;
  };

  const getTrendColor = () => {
    if (!trend) return theme.colors.onSurface;
    return trend.isPositive ? '#4caf50' : '#f44336';
  };

  const getTrendIcon = () => {
    if (!trend) return null;
    return trend.isPositive ? 'trending-up' : 'trending-down';
  };

  const CardComponent = onPress ? TouchableOpacity : View;

  return (
    <CardComponent onPress={onPress} style={styles.container}>
      <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
        <Card.Content style={styles.content}>
          <View style={styles.header}>
            <View style={[styles.iconContainer, { backgroundColor: `${color}20` }]}>
              <Icon name={icon} size={24} color={color} />
            </View>
            {trend && (
              <View style={styles.trendContainer}>
                <Icon 
                  name={getTrendIcon()!} 
                  size={16} 
                  color={getTrendColor()} 
                />
                <Text 
                  variant="bodySmall" 
                  style={[styles.trendText, { color: getTrendColor() }]}
                >
                  {Math.abs(trend.value)}%
                </Text>
              </View>
            )}
          </View>

          <View style={styles.body}>
            <Text variant="headlineMedium" style={[styles.value, { color }]}>
              {loading ? '...' : formatValue(value)}
            </Text>
            <Text variant="titleMedium" style={styles.title}>
              {title}
            </Text>
            {subtitle && (
              <Text variant="bodySmall" style={styles.subtitle}>
                {subtitle}
              </Text>
            )}
          </View>
        </Card.Content>
      </Card>
    </CardComponent>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    margin: spacing.xs,
  },
  card: {
    elevation: 2,
  },
  content: {
    padding: spacing.md,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  trendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  trendText: {
    marginLeft: spacing.xs,
    fontWeight: '600',
  },
  body: {
    alignItems: 'flex-start',
  },
  value: {
    fontWeight: 'bold',
    marginBottom: spacing.xs,
  },
  title: {
    fontWeight: '600',
    marginBottom: spacing.xs,
  },
  subtitle: {
    opacity: 0.7,
  },
});
