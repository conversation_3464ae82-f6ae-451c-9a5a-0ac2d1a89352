import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { Card, Text, Chip, useTheme } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { spacing } from '@/theme';

interface UpcomingEvent {
  id: string;
  title: string;
  type: 'match' | 'training' | 'medical' | 'meeting';
  date: string;
  time?: string;
  location?: string;
  description?: string;
  priority?: 'low' | 'medium' | 'high';
}

interface UpcomingEventCardProps {
  event: UpcomingEvent;
  onPress?: () => void;
}

export default function UpcomingEventCard({ event, onPress }: UpcomingEventCardProps) {
  const theme = useTheme();

  const getEventIcon = () => {
    switch (event.type) {
      case 'match':
        return 'sports-soccer';
      case 'training':
        return 'fitness-center';
      case 'medical':
        return 'local-hospital';
      case 'meeting':
        return 'meeting-room';
      default:
        return 'event';
    }
  };

  const getEventColor = () => {
    switch (event.type) {
      case 'match':
        return '#4caf50';
      case 'training':
        return '#2196f3';
      case 'medical':
        return '#f44336';
      case 'meeting':
        return '#ff9800';
      default:
        return theme.colors.primary;
    }
  };

  const getPriorityColor = () => {
    switch (event.priority) {
      case 'high':
        return '#f44336';
      case 'medium':
        return '#ff9800';
      case 'low':
        return '#4caf50';
      default:
        return theme.colors.outline;
    }
  };

  const formatEventDate = () => {
    try {
      const eventDate = new Date(event.date);
      return format(eventDate, "dd 'de' MMM", { locale: ptBR });
    } catch {
      return event.date;
    }
  };

  const CardComponent = onPress ? TouchableOpacity : View;

  return (
    <CardComponent onPress={onPress} style={styles.container}>
      <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
        <Card.Content style={styles.content}>
          <View style={styles.header}>
            <View style={styles.iconContainer}>
              <Icon 
                name={getEventIcon()} 
                size={24} 
                color={getEventColor()} 
              />
            </View>
            <View style={styles.headerText}>
              <Text variant="titleMedium" style={styles.title} numberOfLines={1}>
                {event.title}
              </Text>
              <View style={styles.dateTimeContainer}>
                <Text variant="bodySmall" style={styles.date}>
                  {formatEventDate()}
                </Text>
                {event.time && (
                  <Text variant="bodySmall" style={styles.time}>
                    • {event.time}
                  </Text>
                )}
              </View>
            </View>
            {event.priority && (
              <Chip 
                style={[styles.priorityChip, { backgroundColor: `${getPriorityColor()}20` }]}
                textStyle={[styles.priorityText, { color: getPriorityColor() }]}
                compact
              >
                {event.priority}
              </Chip>
            )}
          </View>

          {event.location && (
            <View style={styles.locationContainer}>
              <Icon name="location-on" size={16} color={theme.colors.outline} />
              <Text variant="bodySmall" style={styles.location}>
                {event.location}
              </Text>
            </View>
          )}

          {event.description && (
            <Text variant="bodySmall" style={styles.description} numberOfLines={2}>
              {event.description}
            </Text>
          )}
        </Card.Content>
      </Card>
    </CardComponent>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: spacing.sm,
  },
  card: {
    elevation: 1,
  },
  content: {
    padding: spacing.md,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },
  iconContainer: {
    marginRight: spacing.md,
    marginTop: spacing.xs,
  },
  headerText: {
    flex: 1,
  },
  title: {
    fontWeight: '600',
    marginBottom: spacing.xs,
  },
  dateTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  date: {
    opacity: 0.7,
  },
  time: {
    opacity: 0.7,
    marginLeft: spacing.xs,
  },
  priorityChip: {
    height: 24,
    marginLeft: spacing.sm,
  },
  priorityText: {
    fontSize: 10,
    fontWeight: '600',
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  location: {
    marginLeft: spacing.xs,
    opacity: 0.7,
  },
  description: {
    opacity: 0.8,
    lineHeight: 18,
  },
});
