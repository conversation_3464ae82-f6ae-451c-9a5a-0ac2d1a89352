import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { Card, Text, Chip, useTheme } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { spacing } from '@/theme';
import { 
  FinancialTransaction, 
  getTransactionTypeColor,
  getTransactionStatusLabel,
  getTransactionStatusColor,
  getPaymentMethodLabel,
  getPaymentMethodIcon,
  formatCurrency,
  isOverdue
} from '@/types/financial';

interface FinancialCardProps {
  transaction: FinancialTransaction;
  onPress: () => void;
  showDetails?: boolean;
}

export default function FinancialCard({ 
  transaction, 
  onPress, 
  showDetails = true 
}: FinancialCardProps) {
  const theme = useTheme();

  const formatTransactionDate = () => {
    try {
      const transactionDate = new Date(transaction.date);
      return format(transactionDate, "dd 'de' MMM", { locale: ptBR });
    } catch {
      return transaction.date;
    }
  };

  const getTypeColor = () => {
    return getTransactionTypeColor(transaction.type);
  };

  const getStatusColor = () => {
    return getTransactionStatusColor(transaction.status);
  };

  const isIncomeTransaction = () => {
    return transaction.type === 'income';
  };

  const isOverdueTransaction = () => {
    return transaction.due_date && isOverdue(transaction.due_date, transaction.status);
  };

  const getAmountColor = () => {
    if (isOverdueTransaction()) return '#f44336';
    return getTypeColor();
  };

  const getTypeIcon = () => {
    return isIncomeTransaction() ? 'trending-up' : 'trending-down';
  };

  const renderAmount = () => {
    const amount = formatCurrency(transaction.amount);
    const prefix = isIncomeTransaction() ? '+' : '-';
    
    return (
      <Text variant="titleMedium" style={[styles.amount, { color: getAmountColor() }]}>
        {prefix} {amount}
      </Text>
    );
  };

  const renderDueDate = () => {
    if (!transaction.due_date) return null;
    
    const isOverdueItem = isOverdueTransaction();
    const dueDate = format(new Date(transaction.due_date), "dd/MM/yyyy");
    
    return (
      <View style={styles.dueDateContainer}>
        <Icon 
          name={isOverdueItem ? 'warning' : 'schedule'} 
          size={14} 
          color={isOverdueItem ? '#f44336' : theme.colors.outline} 
        />
        <Text variant="bodySmall" style={[
          styles.dueDate,
          { color: isOverdueItem ? '#f44336' : theme.colors.outline }
        ]}>
          {isOverdueItem ? 'Venceu em' : 'Vence em'} {dueDate}
        </Text>
      </View>
    );
  };

  return (
    <TouchableOpacity onPress={onPress} style={styles.container}>
      <Card style={[
        styles.card, 
        { backgroundColor: theme.colors.surface },
        isOverdueTransaction() && styles.overdueCard
      ]}>
        <Card.Content style={styles.content}>
          <View style={styles.header}>
            <View style={styles.dateContainer}>
              <Text variant="bodyMedium" style={styles.date}>
                {formatTransactionDate()}
              </Text>
              <Chip
                style={[styles.statusChip, { backgroundColor: `${getStatusColor()}20` }]}
                textStyle={[styles.statusText, { color: getStatusColor() }]}
                compact
              >
                {getTransactionStatusLabel(transaction.status)}
              </Chip>
            </View>
            
            <View style={styles.amountContainer}>
              {renderAmount()}
            </View>
          </View>

          <View style={styles.transactionInfo}>
            <View style={styles.titleContainer}>
              <View style={[styles.typeIcon, { backgroundColor: `${getTypeColor()}20` }]}>
                <Icon 
                  name={getTypeIcon()} 
                  size={20} 
                  color={getTypeColor()} 
                />
              </View>
              
              <View style={styles.titleText}>
                <Text variant="titleMedium" style={styles.title} numberOfLines={1}>
                  {transaction.title}
                </Text>
                
                <View style={styles.categoryContainer}>
                  <View style={[styles.categoryDot, { backgroundColor: transaction.category.color }]} />
                  <Text variant="bodySmall" style={styles.categoryName}>
                    {transaction.category.name}
                  </Text>
                </View>
              </View>
            </View>

            {transaction.description && (
              <Text variant="bodySmall" style={styles.description} numberOfLines={2}>
                {transaction.description}
              </Text>
            )}
          </View>

          {showDetails && (
            <View style={styles.details}>
              {transaction.payment_method && (
                <View style={styles.detailItem}>
                  <Icon 
                    name={getPaymentMethodIcon(transaction.payment_method)} 
                    size={16} 
                    color={theme.colors.outline} 
                  />
                  <Text variant="bodySmall" style={styles.detailText}>
                    {getPaymentMethodLabel(transaction.payment_method)}
                  </Text>
                </View>
              )}
              
              {transaction.reference_number && (
                <View style={styles.detailItem}>
                  <Icon name="confirmation-number" size={16} color={theme.colors.outline} />
                  <Text variant="bodySmall" style={styles.detailText}>
                    {transaction.reference_number}
                  </Text>
                </View>
              )}
              
              {renderDueDate()}
            </View>
          )}

          {transaction.tags && transaction.tags.length > 0 && (
            <View style={styles.tagsContainer}>
              {transaction.tags.slice(0, 3).map((tag, index) => (
                <Chip
                  key={index}
                  style={styles.tagChip}
                  textStyle={styles.tagText}
                  compact
                >
                  {tag}
                </Chip>
              ))}
              {transaction.tags.length > 3 && (
                <Text variant="bodySmall" style={styles.moreTags}>
                  +{transaction.tags.length - 3} mais
                </Text>
              )}
            </View>
          )}

          {transaction.recurring && (
            <View style={styles.recurringContainer}>
              <Icon name="repeat" size={14} color={theme.colors.outline} />
              <Text variant="bodySmall" style={styles.recurringText}>
                Transação recorrente
              </Text>
            </View>
          )}
        </Card.Content>
      </Card>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    marginHorizontal: spacing.md,
    marginBottom: spacing.sm,
  },
  card: {
    elevation: 2,
  },
  overdueCard: {
    borderLeftWidth: 4,
    borderLeftColor: '#f44336',
  },
  content: {
    padding: spacing.md,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  date: {
    fontWeight: '600',
    marginRight: spacing.sm,
  },
  statusChip: {
    height: 24,
  },
  statusText: {
    fontSize: 10,
    fontWeight: '600',
  },
  amountContainer: {
    alignItems: 'flex-end',
  },
  amount: {
    fontWeight: 'bold',
  },
  transactionInfo: {
    marginBottom: spacing.md,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  typeIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  titleText: {
    flex: 1,
  },
  title: {
    fontWeight: '600',
    marginBottom: spacing.xs,
  },
  categoryContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: spacing.xs,
  },
  categoryName: {
    opacity: 0.7,
    fontSize: 12,
  },
  description: {
    opacity: 0.8,
    lineHeight: 18,
    marginLeft: 52, // Alinhado com o título
  },
  details: {
    marginBottom: spacing.sm,
    gap: spacing.xs,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  detailText: {
    marginLeft: spacing.xs,
    opacity: 0.7,
  },
  dueDateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  dueDate: {
    marginLeft: spacing.xs,
    fontSize: 12,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
    marginBottom: spacing.sm,
    gap: spacing.xs,
  },
  tagChip: {
    height: 20,
    backgroundColor: '#f5f5f5',
  },
  tagText: {
    fontSize: 9,
    opacity: 0.8,
  },
  moreTags: {
    opacity: 0.6,
    fontStyle: 'italic',
    marginLeft: spacing.xs,
  },
  recurringContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: spacing.sm,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  recurringText: {
    marginLeft: spacing.xs,
    opacity: 0.7,
    fontStyle: 'italic',
  },
});
