import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import {
  Modal,
  Portal,
  Text,
  Button,
  Chip,
  Divider,
  useTheme,
} from 'react-native-paper';
import { spacing } from '@/theme';
import { FinancialFilters, TransactionType, TransactionStatus, PaymentMethod } from '@/types/financial';

interface FinancialFilterBottomSheetProps {
  visible: boolean;
  onDismiss: () => void;
  onApply: (filters: FinancialFilters) => void;
  currentFilters: FinancialFilters;
}

export default function FinancialFilterBottomSheet({
  visible,
  onDismiss,
  onApply,
  currentFilters,
}: FinancialFilterBottomSheetProps) {
  const theme = useTheme();
  const [filters, setFilters] = useState<FinancialFilters>(currentFilters);

  useEffect(() => {
    setFilters(currentFilters);
  }, [currentFilters, visible]);

  const categories = [
    { id: 'cat1', name: 'Mensalidades', type: 'income' },
    { id: 'cat2', name: 'Material Esportivo', type: 'expense' },
    { id: 'cat3', name: 'Manutenção', type: 'expense' },
    { id: 'cat4', name: 'Patrocínio', type: 'income' },
    { id: 'cat5', name: 'Salários', type: 'expense' },
    { id: 'cat6', name: 'Eventos', type: 'income' },
  ];

  const types: { id: TransactionType; name: string }[] = [
    { id: 'income', name: 'Receita' },
    { id: 'expense', name: 'Despesa' },
  ];

  const statuses: { id: TransactionStatus; name: string }[] = [
    { id: 'pending', name: 'Pendente' },
    { id: 'paid', name: 'Pago' },
    { id: 'overdue', name: 'Vencido' },
    { id: 'cancelled', name: 'Cancelado' },
    { id: 'partial', name: 'Parcial' },
  ];

  const paymentMethods: { id: PaymentMethod; name: string }[] = [
    { id: 'cash', name: 'Dinheiro' },
    { id: 'bank_transfer', name: 'Transferência' },
    { id: 'credit_card', name: 'Cartão de Crédito' },
    { id: 'debit_card', name: 'Cartão de Débito' },
    { id: 'pix', name: 'PIX' },
    { id: 'check', name: 'Cheque' },
    { id: 'boleto', name: 'Boleto' },
  ];

  const handleTypeSelect = (type: TransactionType) => {
    setFilters(prev => ({
      ...prev,
      type: prev.type === type ? undefined : type,
    }));
  };

  const handleCategorySelect = (categoryId: string) => {
    setFilters(prev => ({
      ...prev,
      category_id: prev.category_id === categoryId ? undefined : categoryId,
    }));
  };

  const handleStatusSelect = (status: TransactionStatus) => {
    setFilters(prev => ({
      ...prev,
      status: prev.status === status ? undefined : status,
    }));
  };

  const handlePaymentMethodSelect = (method: PaymentMethod) => {
    setFilters(prev => ({
      ...prev,
      payment_method: prev.payment_method === method ? undefined : method,
    }));
  };

  const handleClearFilters = () => {
    setFilters({});
  };

  const handleApply = () => {
    onApply(filters);
  };

  const getActiveFiltersCount = () => {
    return Object.values(filters).filter(value => value !== undefined && value !== '').length;
  };

  return (
    <Portal>
      <Modal
        visible={visible}
        onDismiss={onDismiss}
        contentContainerStyle={[
          styles.container,
          { backgroundColor: theme.colors.surface }
        ]}
      >
        <View style={styles.header}>
          <Text variant="titleLarge" style={styles.title}>
            Filtros Financeiros
          </Text>
          <Button
            mode="text"
            onPress={handleClearFilters}
            disabled={getActiveFiltersCount() === 0}
          >
            Limpar
          </Button>
        </View>

        <Divider />

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Tipo */}
          <View style={styles.section}>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Tipo
            </Text>
            <View style={styles.chipsContainer}>
              {types.map((type) => (
                <Chip
                  key={type.id}
                  selected={filters.type === type.id}
                  onPress={() => handleTypeSelect(type.id)}
                  style={styles.chip}
                  showSelectedOverlay
                >
                  {type.name}
                </Chip>
              ))}
            </View>
          </View>

          <Divider style={styles.divider} />

          {/* Categoria */}
          <View style={styles.section}>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Categoria
            </Text>
            <View style={styles.chipsContainer}>
              {categories.map((category) => (
                <Chip
                  key={category.id}
                  selected={filters.category_id === category.id}
                  onPress={() => handleCategorySelect(category.id)}
                  style={styles.chip}
                  showSelectedOverlay
                >
                  {category.name}
                </Chip>
              ))}
            </View>
          </View>

          <Divider style={styles.divider} />

          {/* Status */}
          <View style={styles.section}>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Status
            </Text>
            <View style={styles.chipsContainer}>
              {statuses.map((status) => (
                <Chip
                  key={status.id}
                  selected={filters.status === status.id}
                  onPress={() => handleStatusSelect(status.id)}
                  style={styles.chip}
                  showSelectedOverlay
                >
                  {status.name}
                </Chip>
              ))}
            </View>
          </View>

          <Divider style={styles.divider} />

          {/* Método de Pagamento */}
          <View style={styles.section}>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Método de Pagamento
            </Text>
            <View style={styles.chipsContainer}>
              {paymentMethods.map((method) => (
                <Chip
                  key={method.id}
                  selected={filters.payment_method === method.id}
                  onPress={() => handlePaymentMethodSelect(method.id)}
                  style={styles.chip}
                  showSelectedOverlay
                >
                  {method.name}
                </Chip>
              ))}
            </View>
          </View>

          <Divider style={styles.divider} />

          {/* Valor */}
          <View style={styles.section}>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Valor
            </Text>
            <Text variant="bodySmall" style={styles.sectionSubtitle}>
              Em breve: filtros por faixa de valor
            </Text>
            <View style={styles.chipsContainer}>
              <Chip
                style={[styles.chip, styles.disabledChip]}
                textStyle={styles.disabledChipText}
                disabled
              >
                Até R$ 100
              </Chip>
              <Chip
                style={[styles.chip, styles.disabledChip]}
                textStyle={styles.disabledChipText}
                disabled
              >
                R$ 100 - R$ 500
              </Chip>
              <Chip
                style={[styles.chip, styles.disabledChip]}
                textStyle={styles.disabledChipText}
                disabled
              >
                Acima de R$ 500
              </Chip>
            </View>
          </View>

          <Divider style={styles.divider} />

          {/* Período */}
          <View style={styles.section}>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Período
            </Text>
            <Text variant="bodySmall" style={styles.sectionSubtitle}>
              Em breve: filtros por data específica
            </Text>
            <View style={styles.chipsContainer}>
              <Chip
                style={[styles.chip, styles.disabledChip]}
                textStyle={styles.disabledChipText}
                disabled
              >
                Esta Semana
              </Chip>
              <Chip
                style={[styles.chip, styles.disabledChip]}
                textStyle={styles.disabledChipText}
                disabled
              >
                Este Mês
              </Chip>
              <Chip
                style={[styles.chip, styles.disabledChip]}
                textStyle={styles.disabledChipText}
                disabled
              >
                Personalizado
              </Chip>
            </View>
          </View>
        </ScrollView>

        <Divider />

        <View style={styles.footer}>
          <Button
            mode="outlined"
            onPress={onDismiss}
            style={styles.footerButton}
          >
            Cancelar
          </Button>
          <Button
            mode="contained"
            onPress={handleApply}
            style={styles.footerButton}
          >
            Aplicar {getActiveFiltersCount() > 0 && `(${getActiveFiltersCount()})`}
          </Button>
        </View>
      </Modal>
    </Portal>
  );
}

const styles = StyleSheet.create({
  container: {
    margin: spacing.md,
    borderRadius: 12,
    maxHeight: '80%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.lg,
    paddingBottom: spacing.md,
  },
  title: {
    fontWeight: '600',
  },
  content: {
    maxHeight: 400,
  },
  section: {
    padding: spacing.lg,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: spacing.sm,
  },
  sectionSubtitle: {
    opacity: 0.7,
    marginBottom: spacing.md,
    fontStyle: 'italic',
  },
  chipsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  chip: {
    marginBottom: spacing.sm,
  },
  disabledChip: {
    opacity: 0.5,
  },
  disabledChipText: {
    opacity: 0.5,
  },
  divider: {
    marginHorizontal: spacing.lg,
  },
  footer: {
    flexDirection: 'row',
    padding: spacing.lg,
    paddingTop: spacing.md,
    gap: spacing.md,
  },
  footerButton: {
    flex: 1,
  },
});
