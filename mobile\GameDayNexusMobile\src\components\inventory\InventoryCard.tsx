import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { Card, Text, Chip, useTheme, ProgressBar } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { spacing } from '@/theme';
import { 
  InventoryItem, 
  getItemConditionLabel, 
  getItemConditionColor,
  getItemStatusLabel,
  getItemStatusColor,
  getStockUnitLabel,
  formatCurrency,
  isLowStock,
  isOutOfStock,
  calculateStockPercentage
} from '@/types/inventory';

interface InventoryCardProps {
  item: InventoryItem;
  onPress: () => void;
  showDetails?: boolean;
}

export default function InventoryCard({ 
  item, 
  onPress, 
  showDetails = true 
}: InventoryCardProps) {
  const theme = useTheme();

  const getStockStatusColor = () => {
    if (isOutOfStock(item)) return '#f44336';
    if (isLowStock(item)) return '#ff9800';
    return '#4caf50';
  };

  const getStockStatusLabel = () => {
    if (isOutOfStock(item)) return 'Sem Estoque';
    if (isLowStock(item)) return 'Estoque Baixo';
    return 'Estoque OK';
  };

  const getStockPercentage = () => {
    if (!item.max_stock) return 0;
    return calculateStockPercentage(item.current_stock, item.max_stock);
  };

  const getCategoryIcon = () => {
    return item.category.icon || 'inventory';
  };

  const renderStockInfo = () => {
    const stockPercentage = getStockPercentage();
    
    return (
      <View style={styles.stockContainer}>
        <View style={styles.stockHeader}>
          <Text variant="bodySmall" style={styles.stockLabel}>
            Estoque: {item.current_stock} {getStockUnitLabel(item.unit)}
          </Text>
          <Chip
            style={[styles.stockChip, { backgroundColor: `${getStockStatusColor()}20` }]}
            textStyle={[styles.stockText, { color: getStockStatusColor() }]}
            compact
          >
            {getStockStatusLabel()}
          </Chip>
        </View>
        
        {item.max_stock && (
          <View style={styles.progressContainer}>
            <ProgressBar
              progress={stockPercentage / 100}
              color={getStockStatusColor()}
              style={styles.progressBar}
            />
            <Text variant="bodySmall" style={styles.progressText}>
              {stockPercentage.toFixed(0)}% da capacidade
            </Text>
          </View>
        )}
        
        <View style={styles.stockDetails}>
          <Text variant="bodySmall" style={styles.stockDetail}>
            Mín: {item.min_stock}
          </Text>
          {item.max_stock && (
            <Text variant="bodySmall" style={styles.stockDetail}>
              Máx: {item.max_stock}
            </Text>
          )}
        </View>
      </View>
    );
  };

  return (
    <TouchableOpacity onPress={onPress} style={styles.container}>
      <Card style={[
        styles.card, 
        { backgroundColor: theme.colors.surface },
        (isOutOfStock(item) || isLowStock(item)) && styles.alertCard
      ]}>
        <Card.Content style={styles.content}>
          <View style={styles.header}>
            <View style={styles.titleContainer}>
              <View style={[styles.categoryIcon, { backgroundColor: `${item.category.color}20` }]}>
                <Icon 
                  name={getCategoryIcon()} 
                  size={20} 
                  color={item.category.color} 
                />
              </View>
              
              <View style={styles.titleText}>
                <Text variant="titleMedium" style={styles.title} numberOfLines={1}>
                  {item.name}
                </Text>
                
                <View style={styles.categoryContainer}>
                  <View style={[styles.categoryDot, { backgroundColor: item.category.color }]} />
                  <Text variant="bodySmall" style={styles.categoryName}>
                    {item.category.name}
                  </Text>
                </View>
              </View>
            </View>
            
            <View style={styles.valueContainer}>
              <Text variant="titleMedium" style={styles.value}>
                {formatCurrency(item.total_value)}
              </Text>
              {item.unit_cost && (
                <Text variant="bodySmall" style={styles.unitCost}>
                  {formatCurrency(item.unit_cost)}/{getStockUnitLabel(item.unit)}
                </Text>
              )}
            </View>
          </View>

          {item.description && (
            <Text variant="bodySmall" style={styles.description} numberOfLines={2}>
              {item.description}
            </Text>
          )}

          {renderStockInfo()}

          {showDetails && (
            <View style={styles.details}>
              <View style={styles.detailRow}>
                <View style={styles.detailItem}>
                  <Icon name="business" size={16} color={theme.colors.outline} />
                  <Text variant="bodySmall" style={styles.detailText}>
                    {item.brand || 'Sem marca'}
                  </Text>
                </View>
                
                {item.location && (
                  <View style={styles.detailItem}>
                    <Icon name="location-on" size={16} color={theme.colors.outline} />
                    <Text variant="bodySmall" style={styles.detailText}>
                      {item.location}
                    </Text>
                  </View>
                )}
              </View>
              
              <View style={styles.statusRow}>
                <Chip
                  style={[styles.conditionChip, { backgroundColor: `${getItemConditionColor(item.condition)}20` }]}
                  textStyle={[styles.conditionText, { color: getItemConditionColor(item.condition) }]}
                  compact
                >
                  {getItemConditionLabel(item.condition)}
                </Chip>
                
                <Chip
                  style={[styles.statusChip, { backgroundColor: `${getItemStatusColor(item.status)}20` }]}
                  textStyle={[styles.statusText, { color: getItemStatusColor(item.status) }]}
                  compact
                >
                  {getItemStatusLabel(item.status)}
                </Chip>
              </View>
            </View>
          )}

          {item.tags && item.tags.length > 0 && (
            <View style={styles.tagsContainer}>
              {item.tags.slice(0, 3).map((tag, index) => (
                <Chip
                  key={index}
                  style={styles.tagChip}
                  textStyle={styles.tagText}
                  compact
                >
                  {tag}
                </Chip>
              ))}
              {item.tags.length > 3 && (
                <Text variant="bodySmall" style={styles.moreTags}>
                  +{item.tags.length - 3} mais
                </Text>
              )}
            </View>
          )}

          {(isOutOfStock(item) || isLowStock(item)) && (
            <View style={styles.alertContainer}>
              <Icon 
                name={isOutOfStock(item) ? 'error' : 'warning'} 
                size={16} 
                color={getStockStatusColor()} 
              />
              <Text variant="bodySmall" style={[styles.alertText, { color: getStockStatusColor() }]}>
                {isOutOfStock(item) 
                  ? 'Item sem estoque - reposição necessária'
                  : 'Estoque baixo - considere repor'
                }
              </Text>
            </View>
          )}
        </Card.Content>
      </Card>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    marginHorizontal: spacing.md,
    marginBottom: spacing.sm,
  },
  card: {
    elevation: 2,
  },
  alertCard: {
    borderLeftWidth: 4,
    borderLeftColor: '#ff9800',
  },
  content: {
    padding: spacing.md,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.md,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginRight: spacing.md,
  },
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  titleText: {
    flex: 1,
  },
  title: {
    fontWeight: '600',
    marginBottom: spacing.xs,
  },
  categoryContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: spacing.xs,
  },
  categoryName: {
    opacity: 0.7,
    fontSize: 12,
  },
  valueContainer: {
    alignItems: 'flex-end',
  },
  value: {
    fontWeight: 'bold',
    color: '#4caf50',
  },
  unitCost: {
    opacity: 0.7,
    marginTop: spacing.xs,
  },
  description: {
    opacity: 0.8,
    lineHeight: 18,
    marginBottom: spacing.md,
  },
  stockContainer: {
    marginBottom: spacing.md,
    padding: spacing.sm,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
  },
  stockHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  stockLabel: {
    fontWeight: '500',
  },
  stockChip: {
    height: 20,
  },
  stockText: {
    fontSize: 9,
    fontWeight: '600',
  },
  progressContainer: {
    marginBottom: spacing.sm,
  },
  progressBar: {
    height: 6,
    borderRadius: 3,
    marginBottom: spacing.xs,
  },
  progressText: {
    opacity: 0.7,
    fontSize: 10,
  },
  stockDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  stockDetail: {
    opacity: 0.7,
    fontSize: 11,
  },
  details: {
    marginBottom: spacing.sm,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.sm,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  detailText: {
    marginLeft: spacing.xs,
    opacity: 0.7,
    flex: 1,
  },
  statusRow: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  conditionChip: {
    height: 24,
  },
  conditionText: {
    fontSize: 10,
    fontWeight: '600',
  },
  statusChip: {
    height: 24,
  },
  statusText: {
    fontSize: 10,
    fontWeight: '600',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
    marginBottom: spacing.sm,
    gap: spacing.xs,
  },
  tagChip: {
    height: 20,
    backgroundColor: '#e3f2fd',
  },
  tagText: {
    fontSize: 9,
    opacity: 0.8,
  },
  moreTags: {
    opacity: 0.6,
    fontStyle: 'italic',
    marginLeft: spacing.xs,
  },
  alertContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: spacing.sm,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  alertText: {
    marginLeft: spacing.xs,
    fontSize: 11,
    fontWeight: '500',
    flex: 1,
  },
});
