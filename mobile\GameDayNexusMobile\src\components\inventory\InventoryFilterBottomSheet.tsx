import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import {
  Modal,
  Portal,
  Text,
  Button,
  Chip,
  Divider,
  useTheme,
  Switch,
} from 'react-native-paper';
import { spacing } from '@/theme';
import { InventoryFilters, ItemCondition, ItemStatus } from '@/types/inventory';

interface InventoryFilterBottomSheetProps {
  visible: boolean;
  onDismiss: () => void;
  onApply: (filters: InventoryFilters) => void;
  currentFilters: InventoryFilters;
}

export default function InventoryFilterBottomSheet({
  visible,
  onDismiss,
  onApply,
  currentFilters,
}: InventoryFilterBottomSheetProps) {
  const theme = useTheme();
  const [filters, setFilters] = useState<InventoryFilters>(currentFilters);

  useEffect(() => {
    setFilters(currentFilters);
  }, [currentFilters, visible]);

  const categories = [
    { id: 'cat1', name: 'Material Esportivo' },
    { id: 'cat2', name: 'Uniformes' },
    { id: 'cat3', name: '<PERSON><PERSON><PERSON><PERSON>' },
    { id: 'cat4', name: '<PERSON><PERSON>n<PERSON>' },
    { id: 'cat5', name: 'Escritório' },
    { id: 'cat6', name: 'Limpeza' },
  ];

  const conditions: { id: ItemCondition; name: string }[] = [
    { id: 'new', name: 'Novo' },
    { id: 'good', name: 'Bom' },
    { id: 'fair', name: 'Regular' },
    { id: 'poor', name: 'Ruim' },
    { id: 'damaged', name: 'Danificado' },
  ];

  const statuses: { id: ItemStatus; name: string }[] = [
    { id: 'active', name: 'Ativo' },
    { id: 'inactive', name: 'Inativo' },
    { id: 'maintenance', name: 'Manutenção' },
    { id: 'retired', name: 'Aposentado' },
  ];

  const locations = [
    { id: 'Almoxarifado Principal', name: 'Almoxarifado Principal' },
    { id: 'Vestiário', name: 'Vestiário' },
    { id: 'Departamento Médico', name: 'Departamento Médico' },
    { id: 'Campo', name: 'Campo' },
    { id: 'Escritório', name: 'Escritório' },
  ];

  const handleCategorySelect = (categoryId: string) => {
    setFilters(prev => ({
      ...prev,
      category_id: prev.category_id === categoryId ? undefined : categoryId,
    }));
  };

  const handleConditionSelect = (condition: ItemCondition) => {
    setFilters(prev => ({
      ...prev,
      condition: prev.condition === condition ? undefined : condition,
    }));
  };

  const handleStatusSelect = (status: ItemStatus) => {
    setFilters(prev => ({
      ...prev,
      status: prev.status === status ? undefined : status,
    }));
  };

  const handleLocationSelect = (location: string) => {
    setFilters(prev => ({
      ...prev,
      location: prev.location === location ? undefined : location,
    }));
  };

  const handleLowStockToggle = (value: boolean) => {
    setFilters(prev => ({
      ...prev,
      low_stock_only: value ? true : undefined,
    }));
  };

  const handleOutOfStockToggle = (value: boolean) => {
    setFilters(prev => ({
      ...prev,
      out_of_stock_only: value ? true : undefined,
    }));
  };

  const handleClearFilters = () => {
    setFilters({});
  };

  const handleApply = () => {
    onApply(filters);
  };

  const getActiveFiltersCount = () => {
    return Object.values(filters).filter(value => value !== undefined && value !== '').length;
  };

  return (
    <Portal>
      <Modal
        visible={visible}
        onDismiss={onDismiss}
        contentContainerStyle={[
          styles.container,
          { backgroundColor: theme.colors.surface }
        ]}
      >
        <View style={styles.header}>
          <Text variant="titleLarge" style={styles.title}>
            Filtros de Inventário
          </Text>
          <Button
            mode="text"
            onPress={handleClearFilters}
            disabled={getActiveFiltersCount() === 0}
          >
            Limpar
          </Button>
        </View>

        <Divider />

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Alertas de Estoque */}
          <View style={styles.section}>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Alertas de Estoque
            </Text>
            
            <View style={styles.switchContainer}>
              <View style={styles.switchItem}>
                <Text variant="bodyMedium">Apenas estoque baixo</Text>
                <Switch
                  value={filters.low_stock_only || false}
                  onValueChange={handleLowStockToggle}
                />
              </View>
              
              <View style={styles.switchItem}>
                <Text variant="bodyMedium">Apenas sem estoque</Text>
                <Switch
                  value={filters.out_of_stock_only || false}
                  onValueChange={handleOutOfStockToggle}
                />
              </View>
            </View>
          </View>

          <Divider style={styles.divider} />

          {/* Categoria */}
          <View style={styles.section}>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Categoria
            </Text>
            <View style={styles.chipsContainer}>
              {categories.map((category) => (
                <Chip
                  key={category.id}
                  selected={filters.category_id === category.id}
                  onPress={() => handleCategorySelect(category.id)}
                  style={styles.chip}
                  showSelectedOverlay
                >
                  {category.name}
                </Chip>
              ))}
            </View>
          </View>

          <Divider style={styles.divider} />

          {/* Condição */}
          <View style={styles.section}>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Condição
            </Text>
            <View style={styles.chipsContainer}>
              {conditions.map((condition) => (
                <Chip
                  key={condition.id}
                  selected={filters.condition === condition.id}
                  onPress={() => handleConditionSelect(condition.id)}
                  style={styles.chip}
                  showSelectedOverlay
                >
                  {condition.name}
                </Chip>
              ))}
            </View>
          </View>

          <Divider style={styles.divider} />

          {/* Status */}
          <View style={styles.section}>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Status
            </Text>
            <View style={styles.chipsContainer}>
              {statuses.map((status) => (
                <Chip
                  key={status.id}
                  selected={filters.status === status.id}
                  onPress={() => handleStatusSelect(status.id)}
                  style={styles.chip}
                  showSelectedOverlay
                >
                  {status.name}
                </Chip>
              ))}
            </View>
          </View>

          <Divider style={styles.divider} />

          {/* Local */}
          <View style={styles.section}>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Local
            </Text>
            <View style={styles.chipsContainer}>
              {locations.map((location) => (
                <Chip
                  key={location.id}
                  selected={filters.location === location.id}
                  onPress={() => handleLocationSelect(location.id)}
                  style={styles.chip}
                  showSelectedOverlay
                >
                  {location.name}
                </Chip>
              ))}
            </View>
          </View>

          <Divider style={styles.divider} />

          {/* Fornecedor */}
          <View style={styles.section}>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Fornecedor
            </Text>
            <Text variant="bodySmall" style={styles.sectionSubtitle}>
              Em breve: filtros por fornecedor
            </Text>
            <View style={styles.chipsContainer}>
              <Chip
                style={[styles.chip, styles.disabledChip]}
                textStyle={styles.disabledChipText}
                disabled
              >
                Nike
              </Chip>
              <Chip
                style={[styles.chip, styles.disabledChip]}
                textStyle={styles.disabledChipText}
                disabled
              >
                Adidas
              </Chip>
              <Chip
                style={[styles.chip, styles.disabledChip]}
                textStyle={styles.disabledChipText}
                disabled
              >
                Penalty
              </Chip>
            </View>
          </View>
        </ScrollView>

        <Divider />

        <View style={styles.footer}>
          <Button
            mode="outlined"
            onPress={onDismiss}
            style={styles.footerButton}
          >
            Cancelar
          </Button>
          <Button
            mode="contained"
            onPress={handleApply}
            style={styles.footerButton}
          >
            Aplicar {getActiveFiltersCount() > 0 && `(${getActiveFiltersCount()})`}
          </Button>
        </View>
      </Modal>
    </Portal>
  );
}

const styles = StyleSheet.create({
  container: {
    margin: spacing.md,
    borderRadius: 12,
    maxHeight: '80%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.lg,
    paddingBottom: spacing.md,
  },
  title: {
    fontWeight: '600',
  },
  content: {
    maxHeight: 400,
  },
  section: {
    padding: spacing.lg,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: spacing.sm,
  },
  sectionSubtitle: {
    opacity: 0.7,
    marginBottom: spacing.md,
    fontStyle: 'italic',
  },
  switchContainer: {
    gap: spacing.md,
  },
  switchItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  chipsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  chip: {
    marginBottom: spacing.sm,
  },
  disabledChip: {
    opacity: 0.5,
  },
  disabledChipText: {
    opacity: 0.5,
  },
  divider: {
    marginHorizontal: spacing.lg,
  },
  footer: {
    flexDirection: 'row',
    padding: spacing.lg,
    paddingTop: spacing.md,
    gap: spacing.md,
  },
  footerButton: {
    flex: 1,
  },
});
