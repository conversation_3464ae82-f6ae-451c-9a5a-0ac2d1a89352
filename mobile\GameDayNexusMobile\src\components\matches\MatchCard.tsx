import React from 'react';
import { View, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { Card, Text, Chip, useTheme, Avatar } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { spacing } from '@/theme';
import { Match, getMatchStatusLabel, getMatchStatusColor } from '@/types/matches';

interface MatchCardProps {
  match: Match;
  onPress: () => void;
  showDetails?: boolean;
}

export default function MatchCard({ 
  match, 
  onPress, 
  showDetails = true 
}: MatchCardProps) {
  const theme = useTheme();

  const formatMatchDate = () => {
    try {
      const matchDate = new Date(match.date);
      return format(matchDate, "dd 'de' MMM", { locale: ptBR });
    } catch {
      return match.date;
    }
  };

  const formatMatchTime = () => {
    return match.time;
  };

  const getStatusColor = () => {
    return getMatchStatusColor(match.status);
  };

  const isLiveMatch = () => {
    return match.status === 'live' || match.status === 'halftime';
  };

  const isFinishedMatch = () => {
    return match.status === 'finished';
  };

  const getResultDisplay = () => {
    if (!match.result) return null;
    
    if (match.is_home) {
      return `${match.result.home_score} x ${match.result.away_score}`;
    } else {
      return `${match.result.away_score} x ${match.result.home_score}`;
    }
  };

  const getResultColor = () => {
    if (!match.result) return theme.colors.onSurface;
    
    const isWin = (match.is_home && match.result.winner === 'home') || 
                  (!match.is_home && match.result.winner === 'away');
    const isDraw = match.result.winner === 'draw';
    
    if (isWin) return '#4caf50';
    if (isDraw) return '#ff9800';
    return '#f44336';
  };

  const renderOpponentLogo = () => {
    if (match.opponent_logo) {
      return (
        <Image
          source={{ uri: match.opponent_logo }}
          style={styles.opponentLogo}
        />
      );
    }

    return (
      <Avatar.Text
        size={50}
        label={match.opponent_name.substring(0, 2)}
        style={[styles.opponentAvatar, { backgroundColor: theme.colors.primary }]}
      />
    );
  };

  const renderMatchInfo = () => {
    if (isFinishedMatch() && match.result) {
      return (
        <View style={styles.resultContainer}>
          <Text 
            variant="headlineSmall" 
            style={[styles.result, { color: getResultColor() }]}
          >
            {getResultDisplay()}
          </Text>
          <Text variant="bodySmall" style={styles.resultLabel}>
            Resultado
          </Text>
        </View>
      );
    }

    if (isLiveMatch()) {
      return (
        <View style={styles.liveContainer}>
          <View style={[styles.liveDot, { backgroundColor: '#4caf50' }]} />
          <Text variant="titleMedium" style={[styles.liveText, { color: '#4caf50' }]}>
            AO VIVO
          </Text>
          {match.result && (
            <Text variant="titleMedium" style={styles.liveScore}>
              {getResultDisplay()}
            </Text>
          )}
        </View>
      );
    }

    return (
      <View style={styles.timeContainer}>
        <Text variant="titleMedium" style={styles.time}>
          {formatMatchTime()}
        </Text>
        <Text variant="bodySmall" style={styles.timeLabel}>
          Horário
        </Text>
      </View>
    );
  };

  return (
    <TouchableOpacity onPress={onPress} style={styles.container}>
      <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
        <Card.Content style={styles.content}>
          <View style={styles.header}>
            <View style={styles.dateContainer}>
              <Text variant="bodyMedium" style={styles.date}>
                {formatMatchDate()}
              </Text>
              <Chip
                style={[styles.statusChip, { backgroundColor: `${getStatusColor()}20` }]}
                textStyle={[styles.statusText, { color: getStatusColor() }]}
                compact
              >
                {getMatchStatusLabel(match.status)}
              </Chip>
            </View>
            
            <Text variant="bodySmall" style={styles.competition}>
              {match.competition_name}
            </Text>
          </View>

          <View style={styles.matchInfo}>
            <View style={styles.teamContainer}>
              <View style={styles.homeTeam}>
                <Avatar.Text
                  size={40}
                  label="FC"
                  style={[styles.clubAvatar, { backgroundColor: theme.colors.primary }]}
                />
                <Text variant="bodyMedium" style={styles.teamName}>
                  Nosso Clube
                </Text>
              </View>

              <View style={styles.vsContainer}>
                {renderMatchInfo()}
                <Text variant="bodySmall" style={styles.vs}>
                  VS
                </Text>
              </View>

              <View style={styles.awayTeam}>
                {renderOpponentLogo()}
                <Text variant="bodyMedium" style={styles.teamName}>
                  {match.opponent_name}
                </Text>
              </View>
            </View>
          </View>

          {showDetails && (
            <View style={styles.details}>
              <View style={styles.detailItem}>
                <Icon 
                  name={match.is_home ? 'home' : 'flight-takeoff'} 
                  size={16} 
                  color={theme.colors.outline} 
                />
                <Text variant="bodySmall" style={styles.detailText}>
                  {match.is_home ? 'Casa' : 'Fora'}
                </Text>
              </View>
              
              <View style={styles.detailItem}>
                <Icon name="location-on" size={16} color={theme.colors.outline} />
                <Text variant="bodySmall" style={styles.detailText}>
                  {match.location}
                </Text>
              </View>
            </View>
          )}
        </Card.Content>
      </Card>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    marginHorizontal: spacing.md,
    marginBottom: spacing.sm,
  },
  card: {
    elevation: 2,
  },
  content: {
    padding: spacing.md,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  date: {
    fontWeight: '600',
    marginRight: spacing.sm,
  },
  statusChip: {
    height: 24,
  },
  statusText: {
    fontSize: 10,
    fontWeight: '600',
  },
  competition: {
    opacity: 0.7,
    fontStyle: 'italic',
  },
  matchInfo: {
    marginBottom: spacing.md,
  },
  teamContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  homeTeam: {
    alignItems: 'center',
    flex: 1,
  },
  awayTeam: {
    alignItems: 'center',
    flex: 1,
  },
  clubAvatar: {
    marginBottom: spacing.xs,
  },
  opponentLogo: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginBottom: spacing.xs,
  },
  opponentAvatar: {
    marginBottom: spacing.xs,
  },
  teamName: {
    textAlign: 'center',
    fontWeight: '500',
    fontSize: 12,
  },
  vsContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    paddingHorizontal: spacing.md,
  },
  vs: {
    opacity: 0.5,
    marginTop: spacing.xs,
  },
  timeContainer: {
    alignItems: 'center',
  },
  time: {
    fontWeight: 'bold',
    color: '#1976d2',
  },
  timeLabel: {
    opacity: 0.7,
    marginTop: spacing.xs,
  },
  resultContainer: {
    alignItems: 'center',
  },
  result: {
    fontWeight: 'bold',
  },
  resultLabel: {
    opacity: 0.7,
    marginTop: spacing.xs,
  },
  liveContainer: {
    alignItems: 'center',
  },
  liveDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginBottom: spacing.xs,
  },
  liveText: {
    fontWeight: 'bold',
    fontSize: 12,
  },
  liveScore: {
    fontWeight: 'bold',
    marginTop: spacing.xs,
  },
  details: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: spacing.sm,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  detailText: {
    marginLeft: spacing.xs,
    opacity: 0.7,
  },
});
