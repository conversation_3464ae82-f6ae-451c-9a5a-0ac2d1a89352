import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import {
  Modal,
  Portal,
  Text,
  Button,
  Chip,
  Divider,
  useTheme,
} from 'react-native-paper';
import { spacing } from '@/theme';
import { MatchFilters, MatchStatus } from '@/types/matches';

interface MatchFilterBottomSheetProps {
  visible: boolean;
  onDismiss: () => void;
  onApply: (filters: MatchFilters) => void;
  currentFilters: MatchFilters;
}

export default function MatchFilterBottomSheet({
  visible,
  onDismiss,
  onApply,
  currentFilters,
}: MatchFilterBottomSheetProps) {
  const theme = useTheme();
  const [filters, setFilters] = useState<MatchFilters>(currentFilters);

  useEffect(() => {
    setFilters(currentFilters);
  }, [currentFilters, visible]);

  const competitions = [
    { id: 'comp1', name: 'Campeonato Estadual' },
    { id: 'comp2', name: 'Copa Regional' },
    { id: 'comp3', name: 'Amistoso<PERSON>' },
    { id: 'comp4', name: 'Copa do Brasil' },
  ];

  const statuses: { id: MatchStatus; name: string }[] = [
    { id: 'scheduled', name: 'Agendada' },
    { id: 'live', name: 'Ao Vivo' },
    { id: 'halftime', name: 'Intervalo' },
    { id: 'finished', name: 'Finalizada' },
    { id: 'postponed', name: 'Adiada' },
    { id: 'cancelled', name: 'Cancelada' },
  ];

  const venues = [
    { id: true, name: 'Casa' },
    { id: false, name: 'Fora' },
  ];

  const handleCompetitionSelect = (competitionId: string) => {
    setFilters(prev => ({
      ...prev,
      competition_id: prev.competition_id === competitionId ? undefined : competitionId,
    }));
  };

  const handleStatusSelect = (status: MatchStatus) => {
    setFilters(prev => ({
      ...prev,
      status: prev.status === status ? undefined : status,
    }));
  };

  const handleVenueSelect = (isHome: boolean) => {
    setFilters(prev => ({
      ...prev,
      is_home: prev.is_home === isHome ? undefined : isHome,
    }));
  };

  const handleClearFilters = () => {
    setFilters({});
  };

  const handleApply = () => {
    onApply(filters);
  };

  const getActiveFiltersCount = () => {
    return Object.values(filters).filter(value => value !== undefined && value !== '').length;
  };

  return (
    <Portal>
      <Modal
        visible={visible}
        onDismiss={onDismiss}
        contentContainerStyle={[
          styles.container,
          { backgroundColor: theme.colors.surface }
        ]}
      >
        <View style={styles.header}>
          <Text variant="titleLarge" style={styles.title}>
            Filtros de Partidas
          </Text>
          <Button
            mode="text"
            onPress={handleClearFilters}
            disabled={getActiveFiltersCount() === 0}
          >
            Limpar
          </Button>
        </View>

        <Divider />

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Competição */}
          <View style={styles.section}>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Competição
            </Text>
            <View style={styles.chipsContainer}>
              {competitions.map((competition) => (
                <Chip
                  key={competition.id}
                  selected={filters.competition_id === competition.id}
                  onPress={() => handleCompetitionSelect(competition.id)}
                  style={styles.chip}
                  showSelectedOverlay
                >
                  {competition.name}
                </Chip>
              ))}
            </View>
          </View>

          <Divider style={styles.divider} />

          {/* Status */}
          <View style={styles.section}>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Status
            </Text>
            <View style={styles.chipsContainer}>
              {statuses.map((status) => (
                <Chip
                  key={status.id}
                  selected={filters.status === status.id}
                  onPress={() => handleStatusSelect(status.id)}
                  style={styles.chip}
                  showSelectedOverlay
                >
                  {status.name}
                </Chip>
              ))}
            </View>
          </View>

          <Divider style={styles.divider} />

          {/* Local */}
          <View style={styles.section}>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Local
            </Text>
            <View style={styles.chipsContainer}>
              {venues.map((venue) => (
                <Chip
                  key={venue.id.toString()}
                  selected={filters.is_home === venue.id}
                  onPress={() => handleVenueSelect(venue.id)}
                  style={styles.chip}
                  showSelectedOverlay
                >
                  {venue.name}
                </Chip>
              ))}
            </View>
          </View>

          <Divider style={styles.divider} />

          {/* Período */}
          <View style={styles.section}>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Período
            </Text>
            <Text variant="bodySmall" style={styles.sectionSubtitle}>
              Em breve: filtros por data específica
            </Text>
            <View style={styles.chipsContainer}>
              <Chip
                style={[styles.chip, styles.disabledChip]}
                textStyle={styles.disabledChipText}
                disabled
              >
                Esta Semana
              </Chip>
              <Chip
                style={[styles.chip, styles.disabledChip]}
                textStyle={styles.disabledChipText}
                disabled
              >
                Este Mês
              </Chip>
              <Chip
                style={[styles.chip, styles.disabledChip]}
                textStyle={styles.disabledChipText}
                disabled
              >
                Personalizado
              </Chip>
            </View>
          </View>
        </ScrollView>

        <Divider />

        <View style={styles.footer}>
          <Button
            mode="outlined"
            onPress={onDismiss}
            style={styles.footerButton}
          >
            Cancelar
          </Button>
          <Button
            mode="contained"
            onPress={handleApply}
            style={styles.footerButton}
          >
            Aplicar {getActiveFiltersCount() > 0 && `(${getActiveFiltersCount()})`}
          </Button>
        </View>
      </Modal>
    </Portal>
  );
}

const styles = StyleSheet.create({
  container: {
    margin: spacing.md,
    borderRadius: 12,
    maxHeight: '80%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.lg,
    paddingBottom: spacing.md,
  },
  title: {
    fontWeight: '600',
  },
  content: {
    maxHeight: 400,
  },
  section: {
    padding: spacing.lg,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: spacing.sm,
  },
  sectionSubtitle: {
    opacity: 0.7,
    marginBottom: spacing.md,
    fontStyle: 'italic',
  },
  chipsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  chip: {
    marginBottom: spacing.sm,
  },
  disabledChip: {
    opacity: 0.5,
  },
  disabledChipText: {
    opacity: 0.5,
  },
  divider: {
    marginHorizontal: spacing.lg,
  },
  footer: {
    flexDirection: 'row',
    padding: spacing.lg,
    paddingTop: spacing.md,
    gap: spacing.md,
  },
  footerButton: {
    flex: 1,
  },
});
