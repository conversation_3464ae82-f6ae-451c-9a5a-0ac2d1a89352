import React from 'react';
import { View, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { Card, Text, Chip, useTheme, Avatar } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { spacing } from '@/theme';
import { 
  MedicalRecord, 
  getMedicalRecordTypeLabel, 
  getMedicalRecordTypeColor,
  getMedicalStatusLabel,
  getMedicalStatusColor,
  getMedicalPriorityLabel,
  getMedicalPriorityColor
} from '@/types/medical';

interface MedicalCardProps {
  record: MedicalRecord;
  onPress: () => void;
  showDetails?: boolean;
}

export default function MedicalCard({ 
  record, 
  onPress, 
  showDetails = true 
}: MedicalCardProps) {
  const theme = useTheme();

  const formatRecordDate = () => {
    try {
      const recordDate = new Date(record.date);
      return format(recordDate, "dd 'de' MMM", { locale: ptBR });
    } catch {
      return record.date;
    }
  };

  const getTypeColor = () => {
    return getMedicalRecordTypeColor(record.type);
  };

  const getStatusColor = () => {
    return getMedicalStatusColor(record.status);
  };

  const getPriorityColor = () => {
    return getMedicalPriorityColor(record.priority);
  };

  const isUrgentRecord = () => {
    return record.priority === 'urgent' || record.priority === 'high';
  };

  const isActiveRecord = () => {
    return record.status === 'in_progress' || record.status === 'scheduled';
  };

  const getTypeIcon = () => {
    switch (record.type) {
      case 'consultation':
        return 'medical-services';
      case 'exam':
        return 'science';
      case 'injury':
        return 'healing';
      case 'treatment':
        return 'medication';
      case 'surgery':
        return 'local-hospital';
      case 'physiotherapy':
        return 'accessibility';
      case 'vaccination':
        return 'vaccines';
      case 'checkup':
        return 'health-and-safety';
      default:
        return 'medical-services';
    }
  };

  const renderAthleteInfo = () => {
    return (
      <View style={styles.athleteContainer}>
        {record.athlete.photo_url ? (
          <Image
            source={{ uri: record.athlete.photo_url }}
            style={styles.athletePhoto}
          />
        ) : (
          <Avatar.Text
            size={40}
            label={record.athlete.name.split(' ').map(n => n[0]).join('').substring(0, 2)}
            style={[styles.athleteAvatar, { backgroundColor: record.athlete.position?.color || theme.colors.primary }]}
          />
        )}
        
        <View style={styles.athleteInfo}>
          <Text variant="bodyMedium" style={styles.athleteName} numberOfLines={1}>
            {record.athlete.name}
          </Text>
          {record.athlete.jersey_number && (
            <Text variant="bodySmall" style={styles.athleteNumber}>
              #{record.athlete.jersey_number} • {record.athlete.position?.abbreviation}
            </Text>
          )}
        </View>
      </View>
    );
  };

  return (
    <TouchableOpacity onPress={onPress} style={styles.container}>
      <Card style={[
        styles.card, 
        { backgroundColor: theme.colors.surface },
        isUrgentRecord() && styles.urgentCard
      ]}>
        <Card.Content style={styles.content}>
          <View style={styles.header}>
            <View style={styles.dateContainer}>
              <Text variant="bodyMedium" style={styles.date}>
                {formatRecordDate()}
              </Text>
              <View style={styles.statusContainer}>
                <Chip
                  style={[styles.statusChip, { backgroundColor: `${getStatusColor()}20` }]}
                  textStyle={[styles.statusText, { color: getStatusColor() }]}
                  compact
                  icon={isActiveRecord() ? 'circle' : undefined}
                >
                  {getMedicalStatusLabel(record.status)}
                </Chip>
                
                <Chip
                  style={[styles.priorityChip, { backgroundColor: `${getPriorityColor()}20` }]}
                  textStyle={[styles.priorityText, { color: getPriorityColor() }]}
                  compact
                >
                  {getMedicalPriorityLabel(record.priority)}
                </Chip>
              </View>
            </div>
          </View>

          <View style={styles.recordInfo}>
            <View style={styles.titleContainer}>
              <View style={[styles.typeIcon, { backgroundColor: `${getTypeColor()}20` }]}>
                <Icon 
                  name={getTypeIcon()} 
                  size={20} 
                  color={getTypeColor()} 
                />
              </View>
              
              <View style={styles.titleText}>
                <Text variant="titleMedium" style={styles.title} numberOfLines={1}>
                  {record.title}
                </Text>
                
                <Chip
                  style={[styles.typeChip, { backgroundColor: `${getTypeColor()}20` }]}
                  textStyle={[styles.typeText, { color: getTypeColor() }]}
                  compact
                >
                  {getMedicalRecordTypeLabel(record.type)}
                </Chip>
              </View>
            </View>

            {record.description && (
              <Text variant="bodySmall" style={styles.description} numberOfLines={2}>
                {record.description}
              </Text>
            )}
          </View>

          {renderAthleteInfo()}

          {showDetails && (
            <View style={styles.details}>
              {record.professional_name && (
                <View style={styles.detailItem}>
                  <Icon name="person" size={16} color={theme.colors.outline} />
                  <Text variant="bodySmall" style={styles.detailText}>
                    {record.professional_name}
                    {record.professional_specialty && ` - ${record.professional_specialty}`}
                  </Text>
                </View>
              )}
              
              {record.diagnosis && (
                <View style={styles.detailItem}>
                  <Icon name="assignment" size={16} color={theme.colors.outline} />
                  <Text variant="bodySmall" style={styles.detailText}>
                    {record.diagnosis}
                  </Text>
                </View>
              )}
              
              {record.follow_up_date && (
                <View style={styles.detailItem}>
                  <Icon name="event-repeat" size={16} color={theme.colors.outline} />
                  <Text variant="bodySmall" style={styles.detailText}>
                    Retorno: {format(new Date(record.follow_up_date), "dd/MM/yyyy")}
                  </Text>
                </View>
              )}
            </View>
          )}

          {record.treatment && (
            <View style={styles.treatment}>
              <Text variant="bodySmall" style={styles.treatmentLabel}>
                Tratamento:
              </Text>
              <Text variant="bodySmall" style={styles.treatmentText} numberOfLines={2}>
                {record.treatment}
              </Text>
            </View>
          )}
        </Card.Content>
      </Card>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    marginHorizontal: spacing.md,
    marginBottom: spacing.sm,
  },
  card: {
    elevation: 2,
  },
  urgentCard: {
    borderLeftWidth: 4,
    borderLeftColor: '#f44336',
  },
  content: {
    padding: spacing.md,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  date: {
    fontWeight: '600',
    marginRight: spacing.sm,
  },
  statusContainer: {
    flexDirection: 'row',
    gap: spacing.xs,
  },
  statusChip: {
    height: 24,
  },
  statusText: {
    fontSize: 10,
    fontWeight: '600',
  },
  priorityChip: {
    height: 24,
  },
  priorityText: {
    fontSize: 10,
    fontWeight: '600',
  },
  recordInfo: {
    marginBottom: spacing.md,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  typeIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  titleText: {
    flex: 1,
  },
  title: {
    fontWeight: '600',
    marginBottom: spacing.xs,
  },
  typeChip: {
    height: 24,
    alignSelf: 'flex-start',
  },
  typeText: {
    fontSize: 10,
    fontWeight: '600',
  },
  description: {
    opacity: 0.8,
    lineHeight: 18,
    marginLeft: 52, // Alinhado com o título
  },
  athleteContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
    paddingTop: spacing.sm,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  athletePhoto: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: spacing.md,
  },
  athleteAvatar: {
    marginRight: spacing.md,
  },
  athleteInfo: {
    flex: 1,
  },
  athleteName: {
    fontWeight: '500',
    marginBottom: spacing.xs,
  },
  athleteNumber: {
    opacity: 0.7,
  },
  details: {
    marginBottom: spacing.sm,
    gap: spacing.xs,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  detailText: {
    marginLeft: spacing.xs,
    opacity: 0.7,
    flex: 1,
  },
  treatment: {
    paddingTop: spacing.sm,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  treatmentLabel: {
    opacity: 0.7,
    marginBottom: spacing.xs,
    fontWeight: '500',
  },
  treatmentText: {
    opacity: 0.8,
    lineHeight: 16,
  },
});
