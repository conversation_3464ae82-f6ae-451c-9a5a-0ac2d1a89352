import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import {
  Modal,
  Portal,
  Text,
  Button,
  Chip,
  Divider,
  useTheme,
} from 'react-native-paper';
import { spacing } from '@/theme';
import { MedicalFilters, MedicalRecordType, MedicalStatus, MedicalPriority } from '@/types/medical';

interface MedicalFilterBottomSheetProps {
  visible: boolean;
  onDismiss: () => void;
  onApply: (filters: MedicalFilters) => void;
  currentFilters: MedicalFilters;
}

export default function MedicalFilterBottomSheet({
  visible,
  onDismiss,
  onApply,
  currentFilters,
}: MedicalFilterBottomSheetProps) {
  const theme = useTheme();
  const [filters, setFilters] = useState<MedicalFilters>(currentFilters);

  useEffect(() => {
    setFilters(currentFilters);
  }, [currentFilters, visible]);

  const athletes = [
    { id: 'athlete1', name: '<PERSON>' },
    { id: 'athlete2', name: '<PERSON>' },
    { id: 'athlete3', name: '<PERSON>' },
    { id: 'athlete4', name: '<PERSON>' },
  ];

  const types: { id: MedicalRecordType; name: string }[] = [
    { id: 'consultation', name: 'Consulta' },
    { id: 'exam', name: 'Exame' },
    { id: 'injury', name: 'Lesão' },
    { id: 'treatment', name: 'Tratamento' },
    { id: 'surgery', name: 'Cirurgia' },
    { id: 'physiotherapy', name: 'Fisioterapia' },
    { id: 'vaccination', name: 'Vacinação' },
    { id: 'checkup', name: 'Check-up' },
  ];

  const statuses: { id: MedicalStatus; name: string }[] = [
    { id: 'scheduled', name: 'Agendado' },
    { id: 'in_progress', name: 'Em Andamento' },
    { id: 'completed', name: 'Concluído' },
    { id: 'cancelled', name: 'Cancelado' },
    { id: 'postponed', name: 'Adiado' },
    { id: 'pending_results', name: 'Aguardando Resultados' },
  ];

  const priorities: { id: MedicalPriority; name: string }[] = [
    { id: 'urgent', name: 'Urgente' },
    { id: 'high', name: 'Alta' },
    { id: 'medium', name: 'Média' },
    { id: 'low', name: 'Baixa' },
  ];

  const handleAthleteSelect = (athleteId: string) => {
    setFilters(prev => ({
      ...prev,
      athlete_id: prev.athlete_id === athleteId ? undefined : athleteId,
    }));
  };

  const handleTypeSelect = (type: MedicalRecordType) => {
    setFilters(prev => ({
      ...prev,
      type: prev.type === type ? undefined : type,
    }));
  };

  const handleStatusSelect = (status: MedicalStatus) => {
    setFilters(prev => ({
      ...prev,
      status: prev.status === status ? undefined : status,
    }));
  };

  const handlePrioritySelect = (priority: MedicalPriority) => {
    setFilters(prev => ({
      ...prev,
      priority: prev.priority === priority ? undefined : priority,
    }));
  };

  const handleClearFilters = () => {
    setFilters({});
  };

  const handleApply = () => {
    onApply(filters);
  };

  const getActiveFiltersCount = () => {
    return Object.values(filters).filter(value => value !== undefined && value !== '').length;
  };

  return (
    <Portal>
      <Modal
        visible={visible}
        onDismiss={onDismiss}
        contentContainerStyle={[
          styles.container,
          { backgroundColor: theme.colors.surface }
        ]}
      >
        <View style={styles.header}>
          <Text variant="titleLarge" style={styles.title}>
            Filtros Médicos
          </Text>
          <Button
            mode="text"
            onPress={handleClearFilters}
            disabled={getActiveFiltersCount() === 0}
          >
            Limpar
          </Button>
        </View>

        <Divider />

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Atleta */}
          <View style={styles.section}>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Atleta
            </Text>
            <View style={styles.chipsContainer}>
              {athletes.map((athlete) => (
                <Chip
                  key={athlete.id}
                  selected={filters.athlete_id === athlete.id}
                  onPress={() => handleAthleteSelect(athlete.id)}
                  style={styles.chip}
                  showSelectedOverlay
                >
                  {athlete.name}
                </Chip>
              ))}
            </View>
          </View>

          <Divider style={styles.divider} />

          {/* Tipo de Registro */}
          <View style={styles.section}>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Tipo de Registro
            </Text>
            <View style={styles.chipsContainer}>
              {types.map((type) => (
                <Chip
                  key={type.id}
                  selected={filters.type === type.id}
                  onPress={() => handleTypeSelect(type.id)}
                  style={styles.chip}
                  showSelectedOverlay
                >
                  {type.name}
                </Chip>
              ))}
            </View>
          </View>

          <Divider style={styles.divider} />

          {/* Status */}
          <View style={styles.section}>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Status
            </Text>
            <View style={styles.chipsContainer}>
              {statuses.map((status) => (
                <Chip
                  key={status.id}
                  selected={filters.status === status.id}
                  onPress={() => handleStatusSelect(status.id)}
                  style={styles.chip}
                  showSelectedOverlay
                >
                  {status.name}
                </Chip>
              ))}
            </View>
          </View>

          <Divider style={styles.divider} />

          {/* Prioridade */}
          <View style={styles.section}>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Prioridade
            </Text>
            <View style={styles.chipsContainer}>
              {priorities.map((priority) => (
                <Chip
                  key={priority.id}
                  selected={filters.priority === priority.id}
                  onPress={() => handlePrioritySelect(priority.id)}
                  style={styles.chip}
                  showSelectedOverlay
                >
                  {priority.name}
                </Chip>
              ))}
            </View>
          </View>

          <Divider style={styles.divider} />

          {/* Período */}
          <View style={styles.section}>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Período
            </Text>
            <Text variant="bodySmall" style={styles.sectionSubtitle}>
              Em breve: filtros por data específica
            </Text>
            <View style={styles.chipsContainer}>
              <Chip
                style={[styles.chip, styles.disabledChip]}
                textStyle={styles.disabledChipText}
                disabled
              >
                Esta Semana
              </Chip>
              <Chip
                style={[styles.chip, styles.disabledChip]}
                textStyle={styles.disabledChipText}
                disabled
              >
                Este Mês
              </Chip>
              <Chip
                style={[styles.chip, styles.disabledChip]}
                textStyle={styles.disabledChipText}
                disabled
              >
                Personalizado
              </Chip>
            </View>
          </View>
        </ScrollView>

        <Divider />

        <View style={styles.footer}>
          <Button
            mode="outlined"
            onPress={onDismiss}
            style={styles.footerButton}
          >
            Cancelar
          </Button>
          <Button
            mode="contained"
            onPress={handleApply}
            style={styles.footerButton}
          >
            Aplicar {getActiveFiltersCount() > 0 && `(${getActiveFiltersCount()})`}
          </Button>
        </View>
      </Modal>
    </Portal>
  );
}

const styles = StyleSheet.create({
  container: {
    margin: spacing.md,
    borderRadius: 12,
    maxHeight: '80%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.lg,
    paddingBottom: spacing.md,
  },
  title: {
    fontWeight: '600',
  },
  content: {
    maxHeight: 400,
  },
  section: {
    padding: spacing.lg,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: spacing.sm,
  },
  sectionSubtitle: {
    opacity: 0.7,
    marginBottom: spacing.md,
    fontStyle: 'italic',
  },
  chipsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  chip: {
    marginBottom: spacing.sm,
  },
  disabledChip: {
    opacity: 0.5,
  },
  disabledChipText: {
    opacity: 0.5,
  },
  divider: {
    marginHorizontal: spacing.lg,
  },
  footer: {
    flexDirection: 'row',
    padding: spacing.lg,
    paddingTop: spacing.md,
    gap: spacing.md,
  },
  footerButton: {
    flex: 1,
  },
});
