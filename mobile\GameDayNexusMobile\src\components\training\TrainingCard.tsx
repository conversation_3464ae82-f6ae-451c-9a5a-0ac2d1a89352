import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { Card, Text, Chip, useTheme } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { spacing } from '@/theme';
import { 
  Training, 
  getTrainingTypeLabel, 
  getTrainingTypeColor,
  getTrainingStatusLabel,
  getTrainingStatusColor 
} from '@/types/training';

interface TrainingCardProps {
  training: Training;
  onPress: () => void;
  showDetails?: boolean;
}

export default function TrainingCard({ 
  training, 
  onPress, 
  showDetails = true 
}: TrainingCardProps) {
  const theme = useTheme();

  const formatTrainingDate = () => {
    try {
      const trainingDate = new Date(training.date);
      return format(trainingDate, "dd 'de' MMM", { locale: ptBR });
    } catch {
      return training.date;
    }
  };

  const formatTimeRange = () => {
    return `${training.start_time} - ${training.end_time}`;
  };

  const getTypeColor = () => {
    return getTrainingTypeColor(training.type);
  };

  const getStatusColor = () => {
    return getTrainingStatusColor(training.status);
  };

  const isLiveTraining = () => {
    return training.status === 'in_progress';
  };

  const isCompletedTraining = () => {
    return training.status === 'completed';
  };

  const getDuration = () => {
    try {
      const start = new Date(`2000-01-01 ${training.start_time}`);
      const end = new Date(`2000-01-01 ${training.end_time}`);
      const diffMs = end.getTime() - start.getTime();
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
      const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
      
      if (diffHours > 0) {
        return `${diffHours}h${diffMinutes > 0 ? ` ${diffMinutes}min` : ''}`;
      }
      return `${diffMinutes}min`;
    } catch {
      return '';
    }
  };

  const getTypeIcon = () => {
    switch (training.type) {
      case 'technical':
        return 'sports-soccer';
      case 'tactical':
        return 'strategy';
      case 'physical':
        return 'fitness-center';
      case 'recovery':
        return 'spa';
      case 'friendly':
        return 'sports';
      case 'evaluation':
        return 'assessment';
      case 'integration':
        return 'group';
      default:
        return 'sports-soccer';
    }
  };

  return (
    <TouchableOpacity onPress={onPress} style={styles.container}>
      <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
        <Card.Content style={styles.content}>
          <View style={styles.header}>
            <View style={styles.dateContainer}>
              <Text variant="bodyMedium" style={styles.date}>
                {formatTrainingDate()}
              </Text>
              <Chip
                style={[styles.statusChip, { backgroundColor: `${getStatusColor()}20` }]}
                textStyle={[styles.statusText, { color: getStatusColor() }]}
                compact
                icon={isLiveTraining() ? 'circle' : undefined}
              >
                {getTrainingStatusLabel(training.status)}
              </Chip>
            </View>
            
            {training.category_name && (
              <Text variant="bodySmall" style={styles.category}>
                {training.category_name}
              </Text>
            )}
          </View>

          <View style={styles.trainingInfo}>
            <View style={styles.titleContainer}>
              <View style={[styles.typeIcon, { backgroundColor: `${getTypeColor()}20` }]}>
                <Icon 
                  name={getTypeIcon()} 
                  size={20} 
                  color={getTypeColor()} 
                />
              </View>
              
              <View style={styles.titleText}>
                <Text variant="titleMedium" style={styles.title} numberOfLines={1}>
                  {training.title}
                </Text>
                
                <View style={styles.typeContainer}>
                  <Chip
                    style={[styles.typeChip, { backgroundColor: `${getTypeColor()}20` }]}
                    textStyle={[styles.typeText, { color: getTypeColor() }]}
                    compact
                  >
                    {getTrainingTypeLabel(training.type)}
                  </Chip>
                </View>
              </View>
            </View>

            {training.description && (
              <Text variant="bodySmall" style={styles.description} numberOfLines={2}>
                {training.description}
              </Text>
            )}
          </View>

          {showDetails && (
            <View style={styles.details}>
              <View style={styles.detailItem}>
                <Icon name="access-time" size={16} color={theme.colors.outline} />
                <Text variant="bodySmall" style={styles.detailText}>
                  {formatTimeRange()}
                </Text>
                {getDuration() && (
                  <Text variant="bodySmall" style={styles.duration}>
                    ({getDuration()})
                  </Text>
                )}
              </View>
              
              <View style={styles.detailItem}>
                <Icon name="location-on" size={16} color={theme.colors.outline} />
                <Text variant="bodySmall" style={styles.detailText}>
                  {training.location}
                </Text>
              </View>
              
              {training.coach_name && (
                <View style={styles.detailItem}>
                  <Icon name="person" size={16} color={theme.colors.outline} />
                  <Text variant="bodySmall" style={styles.detailText}>
                    {training.coach_name}
                  </Text>
                </View>
              )}
            </View>
          )}

          {training.objectives && training.objectives.length > 0 && (
            <View style={styles.objectives}>
              <Text variant="bodySmall" style={styles.objectivesLabel}>
                Objetivos:
              </Text>
              <View style={styles.objectivesList}>
                {training.objectives.slice(0, 2).map((objective, index) => (
                  <Chip
                    key={index}
                    style={styles.objectiveChip}
                    textStyle={styles.objectiveText}
                    compact
                  >
                    {objective}
                  </Chip>
                ))}
                {training.objectives.length > 2 && (
                  <Text variant="bodySmall" style={styles.moreObjectives}>
                    +{training.objectives.length - 2} mais
                  </Text>
                )}
              </View>
            </View>
          )}
        </Card.Content>
      </Card>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    marginHorizontal: spacing.md,
    marginBottom: spacing.sm,
  },
  card: {
    elevation: 2,
  },
  content: {
    padding: spacing.md,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  date: {
    fontWeight: '600',
    marginRight: spacing.sm,
  },
  statusChip: {
    height: 24,
  },
  statusText: {
    fontSize: 10,
    fontWeight: '600',
  },
  category: {
    opacity: 0.7,
    fontStyle: 'italic',
  },
  trainingInfo: {
    marginBottom: spacing.md,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  typeIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  titleText: {
    flex: 1,
  },
  title: {
    fontWeight: '600',
    marginBottom: spacing.xs,
  },
  typeContainer: {
    flexDirection: 'row',
  },
  typeChip: {
    height: 24,
  },
  typeText: {
    fontSize: 10,
    fontWeight: '600',
  },
  description: {
    opacity: 0.8,
    lineHeight: 18,
    marginLeft: 52, // Alinhado com o título
  },
  details: {
    marginBottom: spacing.sm,
    gap: spacing.xs,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  detailText: {
    marginLeft: spacing.xs,
    opacity: 0.7,
  },
  duration: {
    marginLeft: spacing.xs,
    opacity: 0.5,
    fontStyle: 'italic',
  },
  objectives: {
    paddingTop: spacing.sm,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  objectivesLabel: {
    opacity: 0.7,
    marginBottom: spacing.xs,
    fontWeight: '500',
  },
  objectivesList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
    gap: spacing.xs,
  },
  objectiveChip: {
    height: 20,
    backgroundColor: '#f5f5f5',
  },
  objectiveText: {
    fontSize: 9,
    opacity: 0.8,
  },
  moreObjectives: {
    opacity: 0.6,
    fontStyle: 'italic',
    marginLeft: spacing.xs,
  },
});
