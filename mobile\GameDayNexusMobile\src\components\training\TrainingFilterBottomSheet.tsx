import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import {
  Modal,
  Portal,
  Text,
  Button,
  Chip,
  Divider,
  useTheme,
} from 'react-native-paper';
import { spacing } from '@/theme';
import { TrainingFilters, TrainingType, TrainingStatus } from '@/types/training';

interface TrainingFilterBottomSheetProps {
  visible: boolean;
  onDismiss: () => void;
  onApply: (filters: TrainingFilters) => void;
  currentFilters: TrainingFilters;
}

export default function TrainingFilterBottomSheet({
  visible,
  onDismiss,
  onApply,
  currentFilters,
}: TrainingFilterBottomSheetProps) {
  const theme = useTheme();
  const [filters, setFilters] = useState<TrainingFilters>(currentFilters);

  useEffect(() => {
    setFilters(currentFilters);
  }, [currentFilters, visible]);

  const categories = [
    { id: 'cat1', name: 'Sub-20' },
    { id: 'cat2', name: 'Sub-17' },
    { id: 'cat3', name: 'Sub-15' },
    { id: 'cat4', name: 'Profissional' },
  ];

  const types: { id: TrainingType; name: string }[] = [
    { id: 'technical', name: 'Técnico' },
    { id: 'tactical', name: 'Tático' },
    { id: 'physical', name: 'Físico' },
    { id: 'recovery', name: 'Recuperação' },
    { id: 'friendly', name: 'Jogo-treino' },
    { id: 'evaluation', name: 'Avaliação' },
    { id: 'integration', name: 'Integração' },
  ];

  const statuses: { id: TrainingStatus; name: string }[] = [
    { id: 'scheduled', name: 'Agendado' },
    { id: 'in_progress', name: 'Em Andamento' },
    { id: 'completed', name: 'Concluído' },
    { id: 'cancelled', name: 'Cancelado' },
    { id: 'postponed', name: 'Adiado' },
  ];

  const locations = [
    { id: 'Campo Principal', name: 'Campo Principal' },
    { id: 'Campo Secundário', name: 'Campo Secundário' },
    { id: 'Academia', name: 'Academia' },
    { id: 'Quadra', name: 'Quadra' },
  ];

  const handleCategorySelect = (categoryId: string) => {
    setFilters(prev => ({
      ...prev,
      category_id: prev.category_id === categoryId ? undefined : categoryId,
    }));
  };

  const handleTypeSelect = (type: TrainingType) => {
    setFilters(prev => ({
      ...prev,
      type: prev.type === type ? undefined : type,
    }));
  };

  const handleStatusSelect = (status: TrainingStatus) => {
    setFilters(prev => ({
      ...prev,
      status: prev.status === status ? undefined : status,
    }));
  };

  const handleLocationSelect = (location: string) => {
    setFilters(prev => ({
      ...prev,
      location: prev.location === location ? undefined : location,
    }));
  };

  const handleClearFilters = () => {
    setFilters({});
  };

  const handleApply = () => {
    onApply(filters);
  };

  const getActiveFiltersCount = () => {
    return Object.values(filters).filter(value => value !== undefined && value !== '').length;
  };

  return (
    <Portal>
      <Modal
        visible={visible}
        onDismiss={onDismiss}
        contentContainerStyle={[
          styles.container,
          { backgroundColor: theme.colors.surface }
        ]}
      >
        <View style={styles.header}>
          <Text variant="titleLarge" style={styles.title}>
            Filtros de Treinamentos
          </Text>
          <Button
            mode="text"
            onPress={handleClearFilters}
            disabled={getActiveFiltersCount() === 0}
          >
            Limpar
          </Button>
        </View>

        <Divider />

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Categoria */}
          <View style={styles.section}>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Categoria
            </Text>
            <View style={styles.chipsContainer}>
              {categories.map((category) => (
                <Chip
                  key={category.id}
                  selected={filters.category_id === category.id}
                  onPress={() => handleCategorySelect(category.id)}
                  style={styles.chip}
                  showSelectedOverlay
                >
                  {category.name}
                </Chip>
              ))}
            </View>
          </View>

          <Divider style={styles.divider} />

          {/* Tipo de Treino */}
          <View style={styles.section}>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Tipo de Treino
            </Text>
            <View style={styles.chipsContainer}>
              {types.map((type) => (
                <Chip
                  key={type.id}
                  selected={filters.type === type.id}
                  onPress={() => handleTypeSelect(type.id)}
                  style={styles.chip}
                  showSelectedOverlay
                >
                  {type.name}
                </Chip>
              ))}
            </View>
          </View>

          <Divider style={styles.divider} />

          {/* Status */}
          <View style={styles.section}>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Status
            </Text>
            <View style={styles.chipsContainer}>
              {statuses.map((status) => (
                <Chip
                  key={status.id}
                  selected={filters.status === status.id}
                  onPress={() => handleStatusSelect(status.id)}
                  style={styles.chip}
                  showSelectedOverlay
                >
                  {status.name}
                </Chip>
              ))}
            </View>
          </View>

          <Divider style={styles.divider} />

          {/* Local */}
          <View style={styles.section}>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Local
            </Text>
            <View style={styles.chipsContainer}>
              {locations.map((location) => (
                <Chip
                  key={location.id}
                  selected={filters.location === location.id}
                  onPress={() => handleLocationSelect(location.id)}
                  style={styles.chip}
                  showSelectedOverlay
                >
                  {location.name}
                </Chip>
              ))}
            </View>
          </View>

          <Divider style={styles.divider} />

          {/* Período */}
          <View style={styles.section}>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Período
            </Text>
            <Text variant="bodySmall" style={styles.sectionSubtitle}>
              Em breve: filtros por data específica
            </Text>
            <View style={styles.chipsContainer}>
              <Chip
                style={[styles.chip, styles.disabledChip]}
                textStyle={styles.disabledChipText}
                disabled
              >
                Esta Semana
              </Chip>
              <Chip
                style={[styles.chip, styles.disabledChip]}
                textStyle={styles.disabledChipText}
                disabled
              >
                Este Mês
              </Chip>
              <Chip
                style={[styles.chip, styles.disabledChip]}
                textStyle={styles.disabledChipText}
                disabled
              >
                Personalizado
              </Chip>
            </View>
          </View>
        </ScrollView>

        <Divider />

        <View style={styles.footer}>
          <Button
            mode="outlined"
            onPress={onDismiss}
            style={styles.footerButton}
          >
            Cancelar
          </Button>
          <Button
            mode="contained"
            onPress={handleApply}
            style={styles.footerButton}
          >
            Aplicar {getActiveFiltersCount() > 0 && `(${getActiveFiltersCount()})`}
          </Button>
        </View>
      </Modal>
    </Portal>
  );
}

const styles = StyleSheet.create({
  container: {
    margin: spacing.md,
    borderRadius: 12,
    maxHeight: '80%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.lg,
    paddingBottom: spacing.md,
  },
  title: {
    fontWeight: '600',
  },
  content: {
    maxHeight: 400,
  },
  section: {
    padding: spacing.lg,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: spacing.sm,
  },
  sectionSubtitle: {
    opacity: 0.7,
    marginBottom: spacing.md,
    fontStyle: 'italic',
  },
  chipsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  chip: {
    marginBottom: spacing.sm,
  },
  disabledChip: {
    opacity: 0.5,
  },
  disabledChipText: {
    opacity: 0.5,
  },
  divider: {
    marginHorizontal: spacing.lg,
  },
  footer: {
    flexDirection: 'row',
    padding: spacing.lg,
    paddingTop: spacing.md,
    gap: spacing.md,
  },
  footerButton: {
    flex: 1,
  },
});
