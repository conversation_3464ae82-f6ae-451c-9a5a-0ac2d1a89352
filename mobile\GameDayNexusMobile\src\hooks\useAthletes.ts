import { useState, useEffect, useCallback } from 'react';
import { AthleteService } from '@/services/athleteService';
import { Athlete, Category } from '@/types/athletes';
import { AthleteFormData } from '@/types/forms';

interface UseAthletesOptions {
  clubId: string;
  autoLoad?: boolean;
  filters?: {
    category_id?: string;
    status?: string;
    position?: string;
    search?: string;
  };
}

export const useAthletes = (options: UseAthletesOptions) => {
  const { clubId, autoLoad = true, filters } = options;
  
  const [athletes, setAthletes] = useState<Athlete[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  const loadAthletes = useCallback(async () => {
    if (!clubId) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const response = await AthleteService.getAthletes(clubId, filters);
      
      if (response.error) {
        setError(response.error);
        setAthletes([]);
      } else {
        setAthletes(response.data || []);
      }
    } catch (err) {
      setError('Erro ao carregar atletas');
      setAthletes([]);
    } finally {
      setLoading(false);
    }
  }, [clubId, filters]);

  const refreshAthletes = useCallback(async () => {
    setRefreshing(true);
    await loadAthletes();
    setRefreshing(false);
  }, [loadAthletes]);

  const createAthlete = useCallback(async (athleteData: AthleteFormData) => {
    if (!clubId) return { data: null, error: 'Club ID não fornecido' };
    
    try {
      const response = await AthleteService.createAthlete(clubId, athleteData);
      
      if (response.error) {
        return { data: null, error: response.error };
      }
      
      // Recarregar lista após criação
      await loadAthletes();
      
      return { data: response.data, error: null };
    } catch (err) {
      return { data: null, error: 'Erro ao criar atleta' };
    }
  }, [clubId, loadAthletes]);

  const updateAthlete = useCallback(async (id: string, athleteData: Partial<AthleteFormData>) => {
    try {
      const response = await AthleteService.updateAthlete(id, athleteData);
      
      if (response.error) {
        return { data: null, error: response.error };
      }
      
      // Atualizar lista local
      setAthletes(prev => 
        prev.map(athlete => 
          athlete.id === id ? { ...athlete, ...response.data } : athlete
        )
      );
      
      return { data: response.data, error: null };
    } catch (err) {
      return { data: null, error: 'Erro ao atualizar atleta' };
    }
  }, []);

  const deleteAthlete = useCallback(async (id: string) => {
    try {
      const response = await AthleteService.deleteAthlete(id);
      
      if (response.error) {
        return { data: null, error: response.error };
      }
      
      // Remover da lista local
      setAthletes(prev => prev.filter(athlete => athlete.id !== id));
      
      return { data: true, error: null };
    } catch (err) {
      return { data: null, error: 'Erro ao deletar atleta' };
    }
  }, []);

  const uploadPhoto = useCallback(async (athleteId: string, photoFile: File | Blob) => {
    try {
      const response = await AthleteService.uploadAthletePhoto(athleteId, photoFile);
      
      if (response.error) {
        return { data: null, error: response.error };
      }
      
      // Atualizar lista local com nova URL da foto
      setAthletes(prev => 
        prev.map(athlete => 
          athlete.id === athleteId ? { ...athlete, photo_url: response.data?.photo_url } : athlete
        )
      );
      
      return { data: response.data, error: null };
    } catch (err) {
      return { data: null, error: 'Erro ao fazer upload da foto' };
    }
  }, []);

  // Carregar automaticamente se autoLoad estiver ativo
  useEffect(() => {
    if (autoLoad) {
      loadAthletes();
    }
  }, [autoLoad, loadAthletes]);

  // Filtros derivados
  const activeAthletes = athletes.filter(athlete => athlete.status === 'active');
  const inactiveAthletes = athletes.filter(athlete => athlete.status === 'inactive');
  const injuredAthletes = athletes.filter(athlete => athlete.status === 'injured');
  const suspendedAthletes = athletes.filter(athlete => athlete.status === 'suspended');
  const loanedAthletes = athletes.filter(athlete => athlete.status === 'loaned');

  // Estatísticas
  const stats = {
    total: athletes.length,
    active: activeAthletes.length,
    inactive: inactiveAthletes.length,
    injured: injuredAthletes.length,
    suspended: suspendedAthletes.length,
    loaned: loanedAthletes.length,
  };

  return {
    // Dados
    athletes,
    activeAthletes,
    inactiveAthletes,
    injuredAthletes,
    suspendedAthletes,
    loanedAthletes,
    stats,
    
    // Estados
    loading,
    error,
    refreshing,
    
    // Ações
    loadAthletes,
    refreshAthletes,
    createAthlete,
    updateAthlete,
    deleteAthlete,
    uploadPhoto,
  };
};

// Hook para buscar um atleta específico
export const useAthlete = (athleteId: string) => {
  const [athlete, setAthlete] = useState<Athlete | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadAthlete = useCallback(async () => {
    if (!athleteId) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const response = await AthleteService.getAthleteById(athleteId);
      
      if (response.error) {
        setError(response.error);
        setAthlete(null);
      } else {
        setAthlete(response.data);
      }
    } catch (err) {
      setError('Erro ao carregar atleta');
      setAthlete(null);
    } finally {
      setLoading(false);
    }
  }, [athleteId]);

  useEffect(() => {
    loadAthlete();
  }, [loadAthlete]);

  return {
    athlete,
    loading,
    error,
    loadAthlete,
  };
};

// Hook para categorias
export const useCategories = (clubId: string) => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadCategories = useCallback(async () => {
    if (!clubId) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const response = await AthleteService.getCategories(clubId);
      
      if (response.error) {
        setError(response.error);
        setCategories([]);
      } else {
        setCategories(response.data || []);
      }
    } catch (err) {
      setError('Erro ao carregar categorias');
      setCategories([]);
    } finally {
      setLoading(false);
    }
  }, [clubId]);

  useEffect(() => {
    loadCategories();
  }, [loadCategories]);

  return {
    categories,
    loading,
    error,
    loadCategories,
  };
};
