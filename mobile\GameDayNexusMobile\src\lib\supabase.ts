import { createClient } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Configurações do Supabase
const supabaseUrl = 'https://qoujacltecwxvymynbsh.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFvdWphY2x0ZWN3eHZ5bXluYnNoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ5NDAxNTYsImV4cCI6MjA2MDUxNjE1Nn0.YKsYHPtM7VaMNUge_bEt-RIszA_n8ZHBO0T3ahjyyeI';

// Criar cliente Supabase com configuração para React Native
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});

// Tipos de resposta da API
export interface ApiResponse<T> {
  data: T | null;
  error: string | null;
  loading: boolean;
}

// Função helper para lidar com respostas do Supabase
export const handleSupabaseResponse = <T>(
  data: T | null,
  error: any
): { data: T | null; error: string | null } => {
  if (error) {
    console.error('Supabase Error:', error);
    return {
      data: null,
      error: error.message || 'Erro desconhecido',
    };
  }
  
  return {
    data,
    error: null,
  };
};

// Função para upload de arquivos
export const uploadFile = async (
  bucket: string,
  path: string,
  file: File | Blob,
  options?: {
    cacheControl?: string;
    contentType?: string;
    upsert?: boolean;
  }
) => {
  try {
    const { data, error } = await supabase.storage
      .from(bucket)
      .upload(path, file, {
        cacheControl: options?.cacheControl || '3600',
        upsert: options?.upsert || false,
        contentType: options?.contentType,
      });

    return handleSupabaseResponse(data, error);
  } catch (error) {
    return handleSupabaseResponse(null, error);
  }
};

// Função para obter URL pública de arquivo
export const getPublicUrl = (bucket: string, path: string): string => {
  const { data } = supabase.storage.from(bucket).getPublicUrl(path);
  return data.publicUrl;
};

// Função para deletar arquivo
export const deleteFile = async (bucket: string, paths: string[]) => {
  try {
    const { data, error } = await supabase.storage
      .from(bucket)
      .remove(paths);

    return handleSupabaseResponse(data, error);
  } catch (error) {
    return handleSupabaseResponse(null, error);
  }
};

// Função para verificar conexão
export const testConnection = async (): Promise<boolean> => {
  try {
    const { data, error } = await supabase
      .from('clubs')
      .select('id')
      .limit(1);
    
    return !error;
  } catch (error) {
    console.error('Erro de conexão:', error);
    return false;
  }
};

// Função para obter usuário atual
export const getCurrentUser = async () => {
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    return handleSupabaseResponse(user, error);
  } catch (error) {
    return handleSupabaseResponse(null, error);
  }
};

// Função para obter sessão atual
export const getCurrentSession = async () => {
  try {
    const { data: { session }, error } = await supabase.auth.getSession();
    return handleSupabaseResponse(session, error);
  } catch (error) {
    return handleSupabaseResponse(null, error);
  }
};

// Função para login
export const signIn = async (email: string, password: string) => {
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    return handleSupabaseResponse(data, error);
  } catch (error) {
    return handleSupabaseResponse(null, error);
  }
};

// Função para logout
export const signOut = async () => {
  try {
    const { error } = await supabase.auth.signOut();
    return handleSupabaseResponse(true, error);
  } catch (error) {
    return handleSupabaseResponse(null, error);
  }
};

// Função para registrar usuário
export const signUp = async (email: string, password: string, metadata?: any) => {
  try {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: metadata,
      },
    });
    return handleSupabaseResponse(data, error);
  } catch (error) {
    return handleSupabaseResponse(null, error);
  }
};

// Função para resetar senha
export const resetPassword = async (email: string) => {
  try {
    const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: 'https://www.gamedaynexus.com.br/reset-password',
    });
    return handleSupabaseResponse(data, error);
  } catch (error) {
    return handleSupabaseResponse(null, error);
  }
};

// Listener para mudanças de autenticação
export const onAuthStateChange = (callback: (event: string, session: any) => void) => {
  return supabase.auth.onAuthStateChange(callback);
};

// Função para executar RPC (funções do banco)
export const executeRPC = async (functionName: string, params?: any) => {
  try {
    const { data, error } = await supabase.rpc(functionName, params);
    return handleSupabaseResponse(data, error);
  } catch (error) {
    return handleSupabaseResponse(null, error);
  }
};

// Função para executar query customizada
export const executeQuery = async (query: string) => {
  try {
    const { data, error } = await supabase.rpc('execute_sql', { query });
    return handleSupabaseResponse(data, error);
  } catch (error) {
    return handleSupabaseResponse(null, error);
  }
};

export default supabase;
