import React from 'react';
import { createDrawerNavigator } from '@react-navigation/drawer';
import { createStackNavigator } from '@react-navigation/stack';
import DashboardScreen from '@/screens/dashboard/DashboardScreen';
import AthleteListScreen from '@/screens/athletes/AthleteListScreen';
import MatchListScreen from '@/screens/matches/MatchListScreen';
import TrainingListScreen from '@/screens/training/TrainingListScreen';
import MedicalListScreen from '@/screens/medical/MedicalListScreen';
import FinancialListScreen from '@/screens/financial/FinancialListScreen';
import InventoryListScreen from '@/screens/inventory/InventoryListScreen';
import UserListScreen from '@/screens/users/UserListScreen';
import ReportListScreen from '@/screens/reports/ReportListScreen';
import SettingsListScreen from '@/screens/settings/SettingsListScreen';

export type MainDrawerParamList = {
  Dashboard: undefined;
  Athletes: undefined;
  Matches: undefined;
  Training: undefined;
  Medical: undefined;
  Financial: undefined;
  Administrative: undefined;
  Inventory: undefined;
  Nutrition: undefined;
  Chat: undefined;
  Reports: undefined;
  Settings: undefined;
};

export type MainStackParamList = {
  DrawerNavigator: undefined;
};

const Drawer = createDrawerNavigator<MainDrawerParamList>();
const Stack = createStackNavigator<MainStackParamList>();

// Componentes temporários para as outras telas
const PlaceholderScreen = ({ route }: any) => {
  return (
    <DashboardScreen />
  );
};

function DrawerNavigator() {
  return (
    <Drawer.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: '#1976d2',
        },
        headerTintColor: '#ffffff',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
        drawerStyle: {
          backgroundColor: '#ffffff',
        },
        drawerActiveTintColor: '#1976d2',
        drawerInactiveTintColor: '#666666',
      }}
    >
      <Drawer.Screen 
        name="Dashboard" 
        component={DashboardScreen}
        options={{
          title: '🏠 Dashboard',
          headerTitle: 'Dashboard',
        }}
      />
      <Drawer.Screen
        name="Athletes"
        component={AthleteListScreen}
        options={{
          title: '👥 Atletas',
          headerTitle: 'Atletas',
        }}
      />
      <Drawer.Screen
        name="Matches"
        component={MatchListScreen}
        options={{
          title: '⚽ Partidas',
          headerTitle: 'Partidas',
        }}
      />
      <Drawer.Screen
        name="Training"
        component={TrainingListScreen}
        options={{
          title: '🏃 Treinamentos',
          headerTitle: 'Treinamentos',
        }}
      />
      <Drawer.Screen
        name="Medical"
        component={MedicalListScreen}
        options={{
          title: '🏥 Médico',
          headerTitle: 'Médico',
        }}
      />
      <Drawer.Screen
        name="Financial"
        component={FinancialListScreen}
        options={{
          title: '💰 Financeiro',
          headerTitle: 'Financeiro',
        }}
      />
      <Drawer.Screen
        name="Administrative"
        component={UserListScreen}
        options={{
          title: '📋 Administrativo',
          headerTitle: 'Administrativo',
        }}
      />
      <Drawer.Screen
        name="Inventory"
        component={InventoryListScreen}
        options={{
          title: '📦 Estoque',
          headerTitle: 'Estoque',
        }}
      />
      <Drawer.Screen 
        name="Nutrition" 
        component={PlaceholderScreen}
        options={{
          title: '🍽️ Alimentação',
          headerTitle: 'Alimentação',
        }}
      />
      <Drawer.Screen 
        name="Chat" 
        component={PlaceholderScreen}
        options={{
          title: '💬 Chat',
          headerTitle: 'Chat',
        }}
      />
      <Drawer.Screen
        name="Reports"
        component={ReportListScreen}
        options={{
          title: '📊 Relatórios',
          headerTitle: 'Relatórios',
        }}
      />
      <Drawer.Screen
        name="Settings"
        component={SettingsListScreen}
        options={{
          title: '⚙️ Configurações',
          headerTitle: 'Configurações',
        }}
      />
    </Drawer.Navigator>
  );
}

export default function MainNavigator() {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="DrawerNavigator" component={DrawerNavigator} />
    </Stack.Navigator>
  );
}
