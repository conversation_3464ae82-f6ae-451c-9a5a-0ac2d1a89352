import React, { useState, useCallback } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  RefreshControl,
  TouchableOpacity,
} from 'react-native';
import {
  Text,
  Searchbar,
  FAB,
  Chip,
  useTheme,
  ActivityIndicator,
} from 'react-native-paper';
import { useFocusEffect } from '@react-navigation/native';
import { spacing } from '@/theme';
import AthleteCard from '@/components/athletes/AthleteCard';
import FilterBottomSheet from '@/components/common/FilterBottomSheet';
import { Athlete, AthleteFilters } from '@/types/athletes';
import { useAthletes } from '@/hooks/useAthletes';

interface AthleteListScreenProps {
  navigation: any;
}

export default function AthleteListScreen({ navigation }: AthleteListScreenProps) {
  const theme = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [athletes, setAthletes] = useState<Athlete[]>([]);
  const [filteredAthletes, setFilteredAthletes] = useState<Athlete[]>([]);
  const [filters, setFilters] = useState<AthleteFilters>({});
  const [showFilters, setShowFilters] = useState(false);

  // Dados mockados para demonstração
  const mockAthletes: Athlete[] = [
    {
      id: '1',
      club_id: 'club1',
      name: 'João Silva',
      birth_date: '2000-05-15',
      cpf: '123.456.789-00',
      jersey_number: 10,
      position_id: 'pos1',
      category_id: 'cat1',
      status: 'active',
      height: 175,
      weight: 70,
      dominant_foot: 'right',
      nationality: 'Brasileiro',
      photo_url: 'https://via.placeholder.com/150',
      created_at: '2025-01-01',
      updated_at: '2025-01-01',
      position: {
        id: 'pos1',
        name: 'Meio-campo',
        abbreviation: 'MC',
        type: 'midfielder',
        color: '#4caf50',
      },
      category: {
        id: 'cat1',
        name: 'Sub-20',
        active: true,
      },
    },
    {
      id: '2',
      club_id: 'club1',
      name: 'Pedro Santos',
      birth_date: '1999-08-22',
      jersey_number: 9,
      position_id: 'pos2',
      category_id: 'cat1',
      status: 'active',
      height: 180,
      weight: 75,
      dominant_foot: 'left',
      nationality: 'Brasileiro',
      photo_url: 'https://via.placeholder.com/150',
      created_at: '2025-01-01',
      updated_at: '2025-01-01',
      position: {
        id: 'pos2',
        name: 'Atacante',
        abbreviation: 'ATA',
        type: 'forward',
        color: '#f44336',
      },
      category: {
        id: 'cat1',
        name: 'Sub-20',
        active: true,
      },
    },
    {
      id: '3',
      club_id: 'club1',
      name: 'Carlos Oliveira',
      birth_date: '2001-03-10',
      jersey_number: 1,
      position_id: 'pos3',
      category_id: 'cat2',
      status: 'active',
      height: 185,
      weight: 80,
      dominant_foot: 'right',
      nationality: 'Brasileiro',
      photo_url: 'https://via.placeholder.com/150',
      created_at: '2025-01-01',
      updated_at: '2025-01-01',
      position: {
        id: 'pos3',
        name: 'Goleiro',
        abbreviation: 'GOL',
        type: 'goalkeeper',
        color: '#ffc107',
      },
      category: {
        id: 'cat2',
        name: 'Sub-17',
        active: true,
      },
    },
  ];

  useFocusEffect(
    useCallback(() => {
      loadAthletes();
    }, [])
  );

  const loadAthletes = async () => {
    setLoading(true);
    try {
      // Simular carregamento da API
      await new Promise(resolve => setTimeout(resolve, 1000));
      setAthletes(mockAthletes);
      setFilteredAthletes(mockAthletes);
    } catch (error) {
      console.error('Erro ao carregar atletas:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadAthletes();
    setRefreshing(false);
  }, []);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    filterAthletes(query, filters);
  };

  const handleFilter = (newFilters: AthleteFilters) => {
    setFilters(newFilters);
    filterAthletes(searchQuery, newFilters);
    setShowFilters(false);
  };

  const filterAthletes = (query: string, currentFilters: AthleteFilters) => {
    let filtered = [...athletes];

    // Filtro por busca
    if (query) {
      filtered = filtered.filter(athlete =>
        athlete.name.toLowerCase().includes(query.toLowerCase()) ||
        athlete.jersey_number?.toString().includes(query)
      );
    }

    // Filtro por categoria
    if (currentFilters.category_id) {
      filtered = filtered.filter(athlete => 
        athlete.category_id === currentFilters.category_id
      );
    }

    // Filtro por posição
    if (currentFilters.position_id) {
      filtered = filtered.filter(athlete => 
        athlete.position_id === currentFilters.position_id
      );
    }

    // Filtro por status
    if (currentFilters.status) {
      filtered = filtered.filter(athlete => 
        athlete.status === currentFilters.status
      );
    }

    setFilteredAthletes(filtered);
  };

  const handleAthletePress = (athlete: Athlete) => {
    navigation.navigate('AthleteProfile', { athleteId: athlete.id });
  };

  const handleAddAthlete = () => {
    navigation.navigate('AthleteForm');
  };

  const getActiveFiltersCount = () => {
    return Object.values(filters).filter(value => value).length;
  };

  const renderAthlete = ({ item }: { item: Athlete }) => (
    <AthleteCard
      athlete={item}
      onPress={() => handleAthletePress(item)}
    />
  );

  const renderHeader = () => (
    <View style={styles.header}>
      <Searchbar
        placeholder="Buscar atletas..."
        onChangeText={handleSearch}
        value={searchQuery}
        style={styles.searchbar}
      />
      
      <View style={styles.filtersContainer}>
        <TouchableOpacity onPress={() => setShowFilters(true)}>
          <Chip
            icon="filter-variant"
            style={styles.filterChip}
            textStyle={styles.filterChipText}
          >
            Filtros {getActiveFiltersCount() > 0 && `(${getActiveFiltersCount()})`}
          </Chip>
        </TouchableOpacity>
      </View>

      <View style={styles.resultsContainer}>
        <Text variant="bodyMedium" style={styles.resultsText}>
          {filteredAthletes.length} atleta{filteredAthletes.length !== 1 ? 's' : ''} encontrado{filteredAthletes.length !== 1 ? 's' : ''}
        </Text>
      </View>
    </View>
  );

  const renderEmpty = () => (
    <View style={styles.emptyContainer}>
      <Text variant="titleMedium" style={styles.emptyTitle}>
        Nenhum atleta encontrado
      </Text>
      <Text variant="bodyMedium" style={styles.emptyMessage}>
        {searchQuery || Object.keys(filters).length > 0
          ? 'Tente ajustar os filtros de busca'
          : 'Adicione o primeiro atleta do clube'
        }
      </Text>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text variant="bodyMedium" style={styles.loadingText}>
          Carregando atletas...
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <FlatList
        data={filteredAthletes}
        renderItem={renderAthlete}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={renderEmpty}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
      />

      <FAB
        icon="plus"
        style={[styles.fab, { backgroundColor: theme.colors.primary }]}
        onPress={handleAddAthlete}
        label="Adicionar"
      />

      <FilterBottomSheet
        visible={showFilters}
        onDismiss={() => setShowFilters(false)}
        onApply={handleFilter}
        currentFilters={filters}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: spacing.md,
    opacity: 0.7,
  },
  listContent: {
    flexGrow: 1,
  },
  header: {
    padding: spacing.md,
    paddingBottom: spacing.sm,
  },
  searchbar: {
    marginBottom: spacing.md,
  },
  filtersContainer: {
    flexDirection: 'row',
    marginBottom: spacing.md,
  },
  filterChip: {
    marginRight: spacing.sm,
  },
  filterChipText: {
    fontSize: 12,
  },
  resultsContainer: {
    marginBottom: spacing.sm,
  },
  resultsText: {
    opacity: 0.7,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.xxxl,
  },
  emptyTitle: {
    fontWeight: '600',
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  emptyMessage: {
    textAlign: 'center',
    opacity: 0.7,
    lineHeight: 20,
  },
  fab: {
    position: 'absolute',
    margin: spacing.md,
    right: 0,
    bottom: 0,
  },
});
