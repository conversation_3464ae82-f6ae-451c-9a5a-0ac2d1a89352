import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
} from 'react-native';
import {
  Text,
  Card,
  Chip,
  Button,
  useTheme,
  ActivityIndicator,
  Avatar,
  Divider,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { spacing, statusColors, positionColors } from '@/theme';
import { Athlete } from '@/types/athletes';

interface AthleteProfileScreenProps {
  route: {
    params: {
      athleteId: string;
    };
  };
  navigation: any;
}

export default function AthleteProfileScreen({ route, navigation }: AthleteProfileScreenProps) {
  const theme = useTheme();
  const { athleteId } = route.params;
  const [athlete, setAthlete] = useState<Athlete | null>(null);
  const [loading, setLoading] = useState(true);

  // Mock data - em produção viria da API
  const mockAthlete: Athlete = {
    id: athleteId,
    club_id: 'club1',
    name: '<PERSON>',
    birth_date: '2000-05-15',
    cpf: '123.456.789-00',
    rg: '12.345.678-9',
    jersey_number: 10,
    position_id: 'pos1',
    category_id: 'cat1',
    status: 'active',
    height: 175,
    weight: 70,
    dominant_foot: 'right',
    nationality: 'Brasileiro',
    place_of_birth: 'São Paulo, SP',
    photo_url: 'https://via.placeholder.com/200',
    created_at: '2025-01-01',
    updated_at: '2025-01-01',
    position: {
      id: 'pos1',
      name: 'Meio-campo',
      abbreviation: 'MC',
      type: 'midfielder',
      color: '#4caf50',
    },
    category: {
      id: 'cat1',
      name: 'Sub-20',
      active: true,
    },
    contact: {
      phone: '(11) 99999-9999',
      email: '<EMAIL>',
      emergency_contact: 'Maria Silva',
      emergency_phone: '(11) 88888-8888',
    },
    address: {
      street: 'Rua das Flores',
      number: '123',
      neighborhood: 'Centro',
      city: 'São Paulo',
      state: 'SP',
      zip_code: '01234-567',
      country: 'Brasil',
    },
  };

  useEffect(() => {
    loadAthlete();
  }, [athleteId]);

  const loadAthlete = async () => {
    setLoading(true);
    try {
      // Simular carregamento da API
      await new Promise(resolve => setTimeout(resolve, 1000));
      setAthlete(mockAthlete);
    } catch (error) {
      console.error('Erro ao carregar atleta:', error);
    } finally {
      setLoading(false);
    }
  };

  const calculateAge = () => {
    if (!athlete) return 0;
    const birthDate = new Date(athlete.birth_date);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    return age;
  };

  const getStatusColor = () => {
    if (!athlete) return theme.colors.outline;
    return statusColors[athlete.status] || theme.colors.outline;
  };

  const getPositionColor = () => {
    if (!athlete?.position) return theme.colors.primary;
    return positionColors[athlete.position.type] || theme.colors.primary;
  };

  const getStatusLabel = () => {
    if (!athlete) return '';
    switch (athlete.status) {
      case 'active':
        return 'Ativo';
      case 'inactive':
        return 'Inativo';
      case 'suspended':
        return 'Suspenso';
      case 'transferred':
        return 'Transferido';
      case 'loaned':
        return 'Emprestado';
      default:
        return athlete.status;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('pt-BR');
  };

  const handleEdit = () => {
    navigation.navigate('AthleteForm', { athleteId: athlete?.id });
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text variant="bodyMedium" style={styles.loadingText}>
          Carregando perfil...
        </Text>
      </View>
    );
  }

  if (!athlete) {
    return (
      <View style={styles.errorContainer}>
        <Text variant="titleMedium">Atleta não encontrado</Text>
        <Button mode="outlined" onPress={() => navigation.goBack()}>
          Voltar
        </Button>
      </View>
    );
  }

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header com foto e info básica */}
      <Card style={styles.headerCard}>
        <Card.Content style={styles.headerContent}>
          <View style={styles.photoContainer}>
            {athlete.photo_url ? (
              <Image source={{ uri: athlete.photo_url }} style={styles.photo} />
            ) : (
              <Avatar.Text
                size={120}
                label={athlete.name.split(' ').map(n => n[0]).join('').substring(0, 2)}
                style={[styles.avatarText, { backgroundColor: getPositionColor() }]}
              />
            )}
            {athlete.jersey_number && (
              <View style={[styles.jerseyBadge, { backgroundColor: getPositionColor() }]}>
                <Text variant="titleMedium" style={styles.jerseyNumber}>
                  {athlete.jersey_number}
                </Text>
              </View>
            )}
          </View>

          <View style={styles.basicInfo}>
            <Text variant="headlineSmall" style={styles.name}>
              {athlete.name}
            </Text>
            
            <View style={styles.chips}>
              {athlete.position && (
                <Chip
                  style={[styles.chip, { backgroundColor: `${getPositionColor()}20` }]}
                  textStyle={[styles.chipText, { color: getPositionColor() }]}
                  compact
                >
                  {athlete.position.name}
                </Chip>
              )}
              
              <Chip
                style={[styles.chip, { backgroundColor: `${getStatusColor()}20` }]}
                textStyle={[styles.chipText, { color: getStatusColor() }]}
                compact
              >
                {getStatusLabel()}
              </Chip>
            </View>

            <View style={styles.quickStats}>
              <View style={styles.statItem}>
                <Text variant="bodySmall" style={styles.statLabel}>Idade</Text>
                <Text variant="titleMedium" style={styles.statValue}>{calculateAge()}</Text>
              </View>
              <View style={styles.statItem}>
                <Text variant="bodySmall" style={styles.statLabel}>Altura</Text>
                <Text variant="titleMedium" style={styles.statValue}>
                  {athlete.height ? `${athlete.height}cm` : '-'}
                </Text>
              </View>
              <View style={styles.statItem}>
                <Text variant="bodySmall" style={styles.statLabel}>Peso</Text>
                <Text variant="titleMedium" style={styles.statValue}>
                  {athlete.weight ? `${athlete.weight}kg` : '-'}
                </Text>
              </View>
            </View>
          </View>
        </Card.Content>
      </Card>

      {/* Informações Pessoais */}
      <Card style={styles.card}>
        <Card.Content>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Informações Pessoais
          </Text>
          <Divider style={styles.divider} />
          
          <View style={styles.infoGrid}>
            <View style={styles.infoItem}>
              <Text variant="bodySmall" style={styles.infoLabel}>Data de Nascimento</Text>
              <Text variant="bodyMedium">{formatDate(athlete.birth_date)}</Text>
            </View>
            
            <View style={styles.infoItem}>
              <Text variant="bodySmall" style={styles.infoLabel}>Nacionalidade</Text>
              <Text variant="bodyMedium">{athlete.nationality}</Text>
            </View>
            
            {athlete.place_of_birth && (
              <View style={styles.infoItem}>
                <Text variant="bodySmall" style={styles.infoLabel}>Local de Nascimento</Text>
                <Text variant="bodyMedium">{athlete.place_of_birth}</Text>
              </View>
            )}
            
            <View style={styles.infoItem}>
              <Text variant="bodySmall" style={styles.infoLabel}>Pé Dominante</Text>
              <Text variant="bodyMedium">
                {athlete.dominant_foot === 'right' ? 'Destro' : 
                 athlete.dominant_foot === 'left' ? 'Canhoto' : 'Ambidestro'}
              </Text>
            </View>
            
            {athlete.category && (
              <View style={styles.infoItem}>
                <Text variant="bodySmall" style={styles.infoLabel}>Categoria</Text>
                <Text variant="bodyMedium">{athlete.category.name}</Text>
              </View>
            )}
          </View>
        </Card.Content>
      </Card>

      {/* Contato */}
      {athlete.contact && (
        <Card style={styles.card}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Contato
            </Text>
            <Divider style={styles.divider} />
            
            <View style={styles.infoGrid}>
              {athlete.contact.phone && (
                <View style={styles.infoItem}>
                  <Text variant="bodySmall" style={styles.infoLabel}>Telefone</Text>
                  <Text variant="bodyMedium">{athlete.contact.phone}</Text>
                </View>
              )}
              
              {athlete.contact.email && (
                <View style={styles.infoItem}>
                  <Text variant="bodySmall" style={styles.infoLabel}>Email</Text>
                  <Text variant="bodyMedium">{athlete.contact.email}</Text>
                </View>
              )}
              
              {athlete.contact.emergency_contact && (
                <View style={styles.infoItem}>
                  <Text variant="bodySmall" style={styles.infoLabel}>Contato de Emergência</Text>
                  <Text variant="bodyMedium">{athlete.contact.emergency_contact}</Text>
                </View>
              )}
              
              {athlete.contact.emergency_phone && (
                <View style={styles.infoItem}>
                  <Text variant="bodySmall" style={styles.infoLabel}>Telefone de Emergência</Text>
                  <Text variant="bodyMedium">{athlete.contact.emergency_phone}</Text>
                </View>
              )}
            </View>
          </Card.Content>
        </Card>
      )}

      {/* Botão de Editar */}
      <View style={styles.actions}>
        <Button
          mode="contained"
          onPress={handleEdit}
          style={styles.editButton}
          icon="edit"
        >
          Editar Atleta
        </Button>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: spacing.md,
    opacity: 0.7,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.lg,
  },
  headerCard: {
    margin: spacing.md,
    marginBottom: spacing.sm,
  },
  headerContent: {
    padding: spacing.lg,
  },
  photoContainer: {
    alignItems: 'center',
    marginBottom: spacing.lg,
    position: 'relative',
  },
  photo: {
    width: 120,
    height: 120,
    borderRadius: 60,
  },
  avatarText: {
    backgroundColor: '#1976d2',
  },
  jerseyBadge: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: '#ffffff',
  },
  jerseyNumber: {
    color: '#ffffff',
    fontWeight: 'bold',
  },
  basicInfo: {
    alignItems: 'center',
  },
  name: {
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: spacing.md,
  },
  chips: {
    flexDirection: 'row',
    marginBottom: spacing.lg,
    gap: spacing.sm,
  },
  chip: {
    height: 28,
  },
  chipText: {
    fontSize: 12,
    fontWeight: '600',
  },
  quickStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
  },
  statItem: {
    alignItems: 'center',
  },
  statLabel: {
    opacity: 0.7,
    marginBottom: spacing.xs,
  },
  statValue: {
    fontWeight: '600',
  },
  card: {
    margin: spacing.md,
    marginTop: spacing.sm,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: spacing.sm,
  },
  divider: {
    marginBottom: spacing.md,
  },
  infoGrid: {
    gap: spacing.md,
  },
  infoItem: {
    marginBottom: spacing.sm,
  },
  infoLabel: {
    opacity: 0.7,
    marginBottom: spacing.xs,
  },
  actions: {
    padding: spacing.md,
    paddingBottom: spacing.xl,
  },
  editButton: {
    marginTop: spacing.md,
  },
});
