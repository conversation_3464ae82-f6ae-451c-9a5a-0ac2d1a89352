import React from 'react';
import { View, StyleSheet, ScrollView, RefreshControl } from 'react-native';
import { Text, Card, Button, useTheme, Divider } from 'react-native-paper';
import { useAuth } from '@/contexts/AuthContext';
import { spacing } from '@/theme';
import StatWidget from '@/components/dashboard/StatWidget';
import QuickActionButton from '@/components/dashboard/QuickActionButton';
import UpcomingEventCard from '@/components/dashboard/UpcomingEventCard';

export default function DashboardScreen() {
  const theme = useTheme();
  const { user, signOut } = useAuth();
  const [refreshing, setRefreshing] = React.useState(false);

  const handleLogout = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Erro ao fazer logout:', error);
    }
  };

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    // Simular carregamento de dados
    setTimeout(() => {
      setRefreshing(false);
    }, 2000);
  }, []);

  // Dados mockados para demonstração
  const stats = [
    {
      title: 'Atletas',
      value: 45,
      subtitle: 'Ativos no clube',
      icon: 'people',
      color: '#4caf50',
      trend: { value: 12, isPositive: true },
    },
    {
      title: 'Partidas',
      value: 8,
      subtitle: 'Este mês',
      icon: 'sports-soccer',
      color: '#2196f3',
      trend: { value: 5, isPositive: false },
    },
    {
      title: 'Treinos',
      value: 24,
      subtitle: 'Esta semana',
      icon: 'fitness-center',
      color: '#ff9800',
    },
    {
      title: 'Pendências',
      value: 3,
      subtitle: 'Requer atenção',
      icon: 'warning',
      color: '#f44336',
    },
  ];

  const upcomingEvents = [
    {
      id: '1',
      title: 'Treino Sub-20',
      type: 'training' as const,
      date: '2025-08-25',
      time: '15:00',
      location: 'Campo Principal',
      priority: 'medium' as const,
    },
    {
      id: '2',
      title: 'Jogo vs Rival FC',
      type: 'match' as const,
      date: '2025-08-27',
      time: '16:00',
      location: 'Estádio Municipal',
      priority: 'high' as const,
    },
    {
      id: '3',
      title: 'Consulta Médica - João',
      type: 'medical' as const,
      date: '2025-08-26',
      time: '10:30',
      location: 'Clínica do Clube',
      priority: 'low' as const,
    },
  ];

  const quickActions = [
    { title: 'Novo Atleta', icon: 'person-add', color: '#4caf50' },
    { title: 'Nova Partida', icon: 'add-circle', color: '#2196f3' },
    { title: 'Novo Treino', icon: 'fitness-center', color: '#ff9800' },
    { title: 'Relatórios', icon: 'assessment', color: '#9c27b0' },
  ];

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      <View style={styles.header}>
        <Text variant="headlineMedium" style={styles.title}>
          Dashboard
        </Text>
        <Text variant="bodyMedium" style={styles.subtitle}>
          Bem-vindo, {user?.email?.split('@')[0]}!
        </Text>
      </View>

      <View style={styles.content}>
        {/* Estatísticas */}
        <View style={styles.statsGrid}>
          {stats.map((stat, index) => (
            <StatWidget
              key={index}
              title={stat.title}
              value={stat.value}
              subtitle={stat.subtitle}
              icon={stat.icon}
              color={stat.color}
              trend={stat.trend}
              onPress={() => console.log(`Pressed ${stat.title}`)}
            />
          ))}
        </View>

        {/* Próximos Eventos */}
        <Card style={styles.card}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.cardTitle}>
              Próximos Eventos
            </Text>
            {upcomingEvents.length > 0 ? (
              upcomingEvents.map((event) => (
                <UpcomingEventCard
                  key={event.id}
                  event={event}
                  onPress={() => console.log(`Pressed event ${event.id}`)}
                />
              ))
            ) : (
              <Text variant="bodyMedium" style={styles.emptyMessage}>
                Nenhum evento agendado
              </Text>
            )}
          </Card.Content>
        </Card>

        {/* Ações Rápidas */}
        <Card style={styles.card}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.cardTitle}>
              Ações Rápidas
            </Text>
            <View style={styles.quickActionsGrid}>
              {quickActions.map((action, index) => (
                <QuickActionButton
                  key={index}
                  title={action.title}
                  icon={action.icon}
                  color={action.color}
                  onPress={() => console.log(`Pressed ${action.title}`)}
                />
              ))}
            </View>
          </Card.Content>
        </Card>

        {/* Configurações */}
        <Card style={styles.card}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.cardTitle}>
              Configurações
            </Text>
            <Divider style={styles.divider} />
            <Button
              mode="text"
              onPress={handleLogout}
              style={styles.logoutButton}
              icon="logout"
            >
              Sair da Conta
            </Button>
          </Card.Content>
        </Card>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: spacing.lg,
    paddingBottom: spacing.md,
  },
  title: {
    fontWeight: 'bold',
    marginBottom: spacing.xs,
  },
  subtitle: {
    opacity: 0.7,
  },
  content: {
    padding: spacing.md,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -spacing.xs,
    marginBottom: spacing.md,
  },
  card: {
    marginBottom: spacing.md,
  },
  cardTitle: {
    fontWeight: '600',
    marginBottom: spacing.md,
  },
  emptyMessage: {
    textAlign: 'center',
    opacity: 0.6,
    fontStyle: 'italic',
    paddingVertical: spacing.lg,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -spacing.xs,
  },
  divider: {
    marginVertical: spacing.md,
  },
  logoutButton: {
    alignSelf: 'flex-start',
  },
});
