import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
} from 'react-native';
import {
  Text,
  Card,
  Chip,
  Button,
  useTheme,
  ActivityIndicator,
  Divider,
  SegmentedButtons,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { spacing } from '@/theme';
import { 
  FinancialTransaction, 
  getTransactionTypeColor,
  getTransactionStatusLabel,
  getTransactionStatusColor,
  getPaymentMethodLabel,
  getPaymentMethodIcon,
  formatCurrency,
  isOverdue
} from '@/types/financial';

interface FinancialDetailScreenProps {
  route: {
    params: {
      transactionId: string;
    };
  };
  navigation: any;
}

export default function FinancialDetailScreen({ route, navigation }: FinancialDetailScreenProps) {
  const theme = useTheme();
  const { transactionId } = route.params;
  const [transaction, setTransaction] = useState<FinancialTransaction | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedTab, setSelectedTab] = useState('info');

  // Mock data - em produção viria da API
  const mockTransaction: FinancialTransaction = {
    id: transactionId,
    club_id: 'club1',
    type: 'expense',
    category_id: 'cat2',
    category: {
      id: 'cat2',
      name: 'Material Esportivo',
      description: 'Equipamentos e materiais para treino e jogos',
      type: 'expense',
      color: '#f44336',
      icon: 'sports-soccer',
      active: true,
      created_at: '2025-01-01',
      updated_at: '2025-01-01',
    },
    title: 'Compra de Bolas Oficiais',
    description: '10 bolas oficiais FIFA para treinos e jogos da categoria sub-20. Marca: Nike Premier League.',
    amount: 500.00,
    date: '2025-08-20',
    due_date: '2025-08-30',
    status: 'pending',
    payment_method: 'bank_transfer',
    reference_number: 'NF-2025-001234',
    tags: ['Material', 'Treino', 'Sub-20'],
    created_at: '2025-01-01',
    updated_at: '2025-01-01',
  };

  useEffect(() => {
    loadTransaction();
  }, [transactionId]);

  const loadTransaction = async () => {
    setLoading(true);
    try {
      // Simular carregamento da API
      await new Promise(resolve => setTimeout(resolve, 1000));
      setTransaction(mockTransaction);
    } catch (error) {
      console.error('Erro ao carregar transação:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatTransactionDate = () => {
    if (!transaction) return '';
    try {
      const transactionDate = new Date(transaction.date);
      return format(transactionDate, "EEEE, dd 'de' MMMM 'de' yyyy", { locale: ptBR });
    } catch {
      return transaction.date;
    }
  };

  const getTypeColor = () => {
    if (!transaction) return theme.colors.outline;
    return getTransactionTypeColor(transaction.type);
  };

  const getStatusColor = () => {
    if (!transaction) return theme.colors.outline;
    return getTransactionStatusColor(transaction.status);
  };

  const isIncomeTransaction = () => {
    return transaction?.type === 'income';
  };

  const isOverdueTransaction = () => {
    return transaction?.due_date && isOverdue(transaction.due_date, transaction.status);
  };

  const getAmountColor = () => {
    if (isOverdueTransaction()) return '#f44336';
    return getTypeColor();
  };

  const handleEdit = () => {
    navigation.navigate('FinancialForm', { transactionId: transaction?.id });
  };

  const handleMarkAsPaid = () => {
    // Implementar lógica para marcar como pago
    console.log('Marcar como pago');
  };

  const renderTransactionHeader = () => {
    if (!transaction) return null;

    const amount = formatCurrency(transaction.amount);
    const prefix = isIncomeTransaction() ? '+' : '-';

    return (
      <Card style={[
        styles.headerCard,
        isOverdueTransaction() && styles.overdueCard
      ]}>
        <Card.Content style={styles.headerContent}>
          <View style={styles.statusContainer}>
            <Chip
              style={[styles.statusChip, { backgroundColor: `${getStatusColor()}20` }]}
              textStyle={[styles.statusText, { color: getStatusColor() }]}
            >
              {getTransactionStatusLabel(transaction.status)}
            </Chip>
            
            <Text variant="headlineMedium" style={[styles.amount, { color: getAmountColor() }]}>
              {prefix} {amount}
            </Text>
          </View>

          <View style={styles.titleContainer}>
            <View style={[styles.typeIcon, { backgroundColor: `${getTypeColor()}20` }]}>
              <Icon 
                name={isIncomeTransaction() ? 'trending-up' : 'trending-down'} 
                size={32} 
                color={getTypeColor()} 
              />
            </View>
            
            <View style={styles.titleText}>
              <Text variant="headlineSmall" style={styles.title}>
                {transaction.title}
              </Text>
              
              <View style={styles.categoryContainer}>
                <View style={[styles.categoryDot, { backgroundColor: transaction.category.color }]} />
                <Text variant="titleSmall" style={styles.categoryName}>
                  {transaction.category.name}
                </Text>
              </View>
            </View>
          </View>

          {transaction.description && (
            <Text variant="bodyMedium" style={styles.description}>
              {transaction.description}
            </Text>
          )}

          <View style={styles.transactionDetails}>
            <View style={styles.detailItem}>
              <Icon name="event" size={20} color={theme.colors.outline} />
              <Text variant="bodyMedium" style={styles.detailText}>
                {formatTransactionDate()}
              </Text>
            </View>
            
            {transaction.payment_method && (
              <View style={styles.detailItem}>
                <Icon 
                  name={getPaymentMethodIcon(transaction.payment_method)} 
                  size={20} 
                  color={theme.colors.outline} 
                />
                <Text variant="bodyMedium" style={styles.detailText}>
                  {getPaymentMethodLabel(transaction.payment_method)}
                </Text>
              </View>
            )}
            
            {transaction.due_date && (
              <View style={styles.detailItem}>
                <Icon 
                  name={isOverdueTransaction() ? 'warning' : 'schedule'} 
                  size={20} 
                  color={isOverdueTransaction() ? '#f44336' : theme.colors.outline} 
                />
                <Text variant="bodyMedium" style={[
                  styles.detailText,
                  { color: isOverdueTransaction() ? '#f44336' : theme.colors.onSurface }
                ]}>
                  {isOverdueTransaction() ? 'Venceu em' : 'Vence em'} {format(new Date(transaction.due_date), "dd/MM/yyyy")}
                </Text>
              </View>
            )}
          </View>
        </Card.Content>
      </Card>
    );
  };

  const renderTabContent = () => {
    switch (selectedTab) {
      case 'info':
        return (
          <Card style={styles.card}>
            <Card.Content>
              <Text variant="titleMedium" style={styles.sectionTitle}>
                Informações da Transação
              </Text>
              <Divider style={styles.divider} />
              
              {transaction?.reference_number && (
                <View style={styles.infoSection}>
                  <Text variant="bodySmall" style={styles.infoLabel}>Número de Referência</Text>
                  <Text variant="bodyMedium">{transaction.reference_number}</Text>
                </View>
              )}
              
              <View style={styles.infoSection}>
                <Text variant="bodySmall" style={styles.infoLabel}>Categoria</Text>
                <Text variant="bodyMedium">{transaction?.category.name}</Text>
                {transaction?.category.description && (
                  <Text variant="bodySmall" style={styles.categoryDescription}>
                    {transaction.category.description}
                  </Text>
                )}
              </View>
              
              {transaction?.tags && transaction.tags.length > 0 && (
                <View style={styles.infoSection}>
                  <Text variant="bodySmall" style={styles.infoLabel}>Tags</Text>
                  <View style={styles.tagsContainer}>
                    {transaction.tags.map((tag, index) => (
                      <Chip
                        key={index}
                        style={styles.tagChip}
                        textStyle={styles.tagText}
                        compact
                      >
                        {tag}
                      </Chip>
                    ))}
                  </View>
                </View>
              )}
            </Card.Content>
          </Card>
        );
      
      case 'attachments':
        return (
          <Card style={styles.card}>
            <Card.Content>
              <Text variant="titleMedium" style={styles.sectionTitle}>
                Anexos
              </Text>
              <Divider style={styles.divider} />
              
              <Text variant="bodyMedium" style={styles.emptyMessage}>
                Nenhum anexo encontrado
              </Text>
              
              <Button
                mode="contained"
                onPress={() => console.log('Adicionar anexo')}
                style={styles.actionButton}
                icon="attach-file"
              >
                Adicionar Anexo
              </Button>
            </Card.Content>
          </Card>
        );
      
      case 'history':
        return (
          <Card style={styles.card}>
            <Card.Content>
              <Text variant="titleMedium" style={styles.sectionTitle}>
                Histórico
              </Text>
              <Divider style={styles.divider} />
              
              <View style={styles.historyItem}>
                <Text variant="bodySmall" style={styles.historyDate}>
                  {format(new Date(), "dd/MM/yyyy 'às' HH:mm")}
                </Text>
                <Text variant="bodyMedium">Transação criada</Text>
              </View>
            </Card.Content>
          </Card>
        );
      
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text variant="bodyMedium" style={styles.loadingText}>
          Carregando transação...
        </Text>
      </View>
    );
  }

  if (!transaction) {
    return (
      <View style={styles.errorContainer}>
        <Text variant="titleMedium">Transação não encontrada</Text>
        <Button mode="outlined" onPress={() => navigation.goBack()}>
          Voltar
        </Button>
      </View>
    );
  }

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {renderTransactionHeader()}

      <View style={styles.tabsContainer}>
        <SegmentedButtons
          value={selectedTab}
          onValueChange={setSelectedTab}
          buttons={[
            {
              value: 'info',
              label: 'Info',
              icon: 'info',
            },
            {
              value: 'attachments',
              label: 'Anexos',
              icon: 'attach-file',
            },
            {
              value: 'history',
              label: 'Histórico',
              icon: 'history',
            },
          ]}
          style={styles.segmentedButtons}
        />
      </View>

      {renderTabContent()}

      {/* Ações */}
      <View style={styles.actions}>
        {transaction.status === 'pending' && (
          <Button
            mode="outlined"
            onPress={handleMarkAsPaid}
            style={styles.actionButton}
            icon="check-circle"
          >
            Marcar como Pago
          </Button>
        )}
        
        <Button
          mode="contained"
          onPress={handleEdit}
          style={styles.actionButton}
          icon="edit"
        >
          Editar Transação
        </Button>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: spacing.md,
    opacity: 0.7,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.lg,
  },
  headerCard: {
    margin: spacing.md,
    marginBottom: spacing.sm,
  },
  overdueCard: {
    borderLeftWidth: 4,
    borderLeftColor: '#f44336',
  },
  headerContent: {
    padding: spacing.lg,
  },
  statusContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  statusChip: {
    height: 28,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  amount: {
    fontWeight: 'bold',
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  typeIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  titleText: {
    flex: 1,
  },
  title: {
    fontWeight: 'bold',
    marginBottom: spacing.sm,
  },
  categoryContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: spacing.sm,
  },
  categoryName: {
    fontWeight: '500',
  },
  description: {
    opacity: 0.8,
    lineHeight: 20,
    marginBottom: spacing.lg,
  },
  transactionDetails: {
    gap: spacing.sm,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  detailText: {
    marginLeft: spacing.sm,
  },
  tabsContainer: {
    padding: spacing.md,
    paddingBottom: spacing.sm,
  },
  segmentedButtons: {
    marginBottom: spacing.sm,
  },
  card: {
    margin: spacing.md,
    marginTop: spacing.sm,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: spacing.sm,
  },
  divider: {
    marginBottom: spacing.md,
  },
  infoSection: {
    marginBottom: spacing.lg,
  },
  infoLabel: {
    opacity: 0.7,
    marginBottom: spacing.sm,
    fontWeight: '500',
  },
  categoryDescription: {
    opacity: 0.6,
    marginTop: spacing.xs,
    fontStyle: 'italic',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  tagChip: {
    height: 24,
    backgroundColor: '#f5f5f5',
  },
  tagText: {
    fontSize: 11,
    opacity: 0.8,
  },
  emptyMessage: {
    textAlign: 'center',
    opacity: 0.6,
    fontStyle: 'italic',
    paddingVertical: spacing.lg,
  },
  historyItem: {
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  historyDate: {
    opacity: 0.6,
    marginBottom: spacing.xs,
  },
  actions: {
    padding: spacing.md,
    paddingBottom: spacing.xl,
    gap: spacing.md,
  },
  actionButton: {
    marginTop: spacing.sm,
  },
});
