import React, { useState, useCallback } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  RefreshControl,
  TouchableOpacity,
} from 'react-native';
import {
  Text,
  Searchbar,
  FAB,
  Chip,
  useTheme,
  ActivityIndicator,
  SegmentedButtons,
  Card,
} from 'react-native-paper';
import { useFocusEffect } from '@react-navigation/native';
import { spacing } from '@/theme';
import FinancialCard from '@/components/financial/FinancialCard';
import FinancialFilterBottomSheet from '@/components/financial/FinancialFilterBottomSheet';
import { FinancialTransaction, FinancialFilters, formatCurrency } from '@/types/financial';

interface FinancialListScreenProps {
  navigation: any;
}

export default function FinancialListScreen({ navigation }: FinancialListScreenProps) {
  const theme = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [transactions, setTransactions] = useState<FinancialTransaction[]>([]);
  const [filteredTransactions, setFilteredTransactions] = useState<FinancialTransaction[]>([]);
  const [filters, setFilters] = useState<FinancialFilters>({});
  const [showFilters, setShowFilters] = useState(false);
  const [selectedTab, setSelectedTab] = useState('all');

  // Dados mockados para demonstração
  const mockTransactions: FinancialTransaction[] = [
    {
      id: '1',
      club_id: 'club1',
      type: 'income',
      category_id: 'cat1',
      category: {
        id: 'cat1',
        name: 'Mensalidades',
        type: 'income',
        color: '#4caf50',
        icon: 'school',
        active: true,
        created_at: '2025-01-01',
        updated_at: '2025-01-01',
      },
      title: 'Mensalidade - João Silva',
      description: 'Mensalidade referente ao mês de agosto',
      amount: 150.00,
      date: '2025-08-25',
      due_date: '2025-08-25',
      status: 'paid',
      payment_method: 'pix',
      created_at: '2025-01-01',
      updated_at: '2025-01-01',
    },
    {
      id: '2',
      club_id: 'club1',
      type: 'expense',
      category_id: 'cat2',
      category: {
        id: 'cat2',
        name: 'Material Esportivo',
        type: 'expense',
        color: '#f44336',
        icon: 'sports-soccer',
        active: true,
        created_at: '2025-01-01',
        updated_at: '2025-01-01',
      },
      title: 'Compra de Bolas',
      description: '10 bolas oficiais para treino',
      amount: 500.00,
      date: '2025-08-20',
      status: 'pending',
      payment_method: 'bank_transfer',
      created_at: '2025-01-01',
      updated_at: '2025-01-01',
    },
    {
      id: '3',
      club_id: 'club1',
      type: 'expense',
      category_id: 'cat3',
      category: {
        id: 'cat3',
        name: 'Manutenção',
        type: 'expense',
        color: '#ff9800',
        icon: 'build',
        active: true,
        created_at: '2025-01-01',
        updated_at: '2025-01-01',
      },
      title: 'Manutenção do Campo',
      description: 'Corte de grama e irrigação',
      amount: 300.00,
      date: '2025-08-15',
      due_date: '2025-08-10',
      status: 'overdue',
      payment_method: 'cash',
      created_at: '2025-01-01',
      updated_at: '2025-01-01',
    },
    {
      id: '4',
      club_id: 'club1',
      type: 'income',
      category_id: 'cat4',
      category: {
        id: 'cat4',
        name: 'Patrocínio',
        type: 'income',
        color: '#2196f3',
        icon: 'business',
        active: true,
        created_at: '2025-01-01',
        updated_at: '2025-01-01',
      },
      title: 'Patrocínio - Empresa XYZ',
      description: 'Patrocínio mensal',
      amount: 2000.00,
      date: '2025-08-01',
      status: 'paid',
      payment_method: 'bank_transfer',
      created_at: '2025-01-01',
      updated_at: '2025-01-01',
    },
  ];

  useFocusEffect(
    useCallback(() => {
      loadTransactions();
    }, [])
  );

  const loadTransactions = async () => {
    setLoading(true);
    try {
      // Simular carregamento da API
      await new Promise(resolve => setTimeout(resolve, 1000));
      setTransactions(mockTransactions);
      filterTransactions(searchQuery, filters, selectedTab, mockTransactions);
    } catch (error) {
      console.error('Erro ao carregar transações:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadTransactions();
    setRefreshing(false);
  }, []);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    filterTransactions(query, filters, selectedTab, transactions);
  };

  const handleFilter = (newFilters: FinancialFilters) => {
    setFilters(newFilters);
    filterTransactions(searchQuery, newFilters, selectedTab, transactions);
    setShowFilters(false);
  };

  const handleTabChange = (tab: string) => {
    setSelectedTab(tab);
    filterTransactions(searchQuery, filters, tab, transactions);
  };

  const filterTransactions = (
    query: string, 
    currentFilters: FinancialFilters, 
    tab: string,
    allTransactions: FinancialTransaction[]
  ) => {
    let filtered = [...allTransactions];

    // Filtro por aba
    if (tab === 'income') {
      filtered = filtered.filter(transaction => transaction.type === 'income');
    } else if (tab === 'expense') {
      filtered = filtered.filter(transaction => transaction.type === 'expense');
    }

    // Filtro por busca
    if (query) {
      filtered = filtered.filter(transaction =>
        transaction.title.toLowerCase().includes(query.toLowerCase()) ||
        transaction.description?.toLowerCase().includes(query.toLowerCase()) ||
        transaction.category.name.toLowerCase().includes(query.toLowerCase())
      );
    }

    // Filtros específicos
    if (currentFilters.type) {
      filtered = filtered.filter(transaction => 
        transaction.type === currentFilters.type
      );
    }

    if (currentFilters.category_id) {
      filtered = filtered.filter(transaction => 
        transaction.category_id === currentFilters.category_id
      );
    }

    if (currentFilters.status) {
      filtered = filtered.filter(transaction => 
        transaction.status === currentFilters.status
      );
    }

    if (currentFilters.payment_method) {
      filtered = filtered.filter(transaction => 
        transaction.payment_method === currentFilters.payment_method
      );
    }

    // Ordenar por data (mais recentes primeiro)
    filtered.sort((a, b) => {
      const dateA = new Date(a.date);
      const dateB = new Date(b.date);
      return dateB.getTime() - dateA.getTime();
    });

    setFilteredTransactions(filtered);
  };

  const handleTransactionPress = (transaction: FinancialTransaction) => {
    navigation.navigate('FinancialDetail', { transactionId: transaction.id });
  };

  const handleAddTransaction = () => {
    navigation.navigate('FinancialForm');
  };

  const getActiveFiltersCount = () => {
    return Object.values(filters).filter(value => value !== undefined && value !== '').length;
  };

  const calculateSummary = () => {
    const income = filteredTransactions
      .filter(t => t.type === 'income')
      .reduce((sum, t) => sum + t.amount, 0);
    
    const expenses = filteredTransactions
      .filter(t => t.type === 'expense')
      .reduce((sum, t) => sum + t.amount, 0);
    
    return {
      income,
      expenses,
      balance: income - expenses,
    };
  };

  const renderTransaction = ({ item }: { item: FinancialTransaction }) => (
    <FinancialCard
      transaction={item}
      onPress={() => handleTransactionPress(item)}
    />
  );

  const renderSummary = () => {
    const summary = calculateSummary();
    
    return (
      <Card style={styles.summaryCard}>
        <Card.Content style={styles.summaryContent}>
          <View style={styles.summaryItem}>
            <Text variant="bodySmall" style={styles.summaryLabel}>Receitas</Text>
            <Text variant="titleMedium" style={[styles.summaryValue, { color: '#4caf50' }]}>
              {formatCurrency(summary.income)}
            </Text>
          </View>
          
          <View style={styles.summaryItem}>
            <Text variant="bodySmall" style={styles.summaryLabel}>Despesas</Text>
            <Text variant="titleMedium" style={[styles.summaryValue, { color: '#f44336' }]}>
              {formatCurrency(summary.expenses)}
            </Text>
          </View>
          
          <View style={styles.summaryItem}>
            <Text variant="bodySmall" style={styles.summaryLabel}>Saldo</Text>
            <Text variant="titleMedium" style={[
              styles.summaryValue, 
              { color: summary.balance >= 0 ? '#4caf50' : '#f44336' }
            ]}>
              {formatCurrency(summary.balance)}
            </Text>
          </View>
        </Card.Content>
      </Card>
    );
  };

  const renderHeader = () => (
    <View style={styles.header}>
      {renderSummary()}
      
      <SegmentedButtons
        value={selectedTab}
        onValueChange={handleTabChange}
        buttons={[
          {
            value: 'all',
            label: 'Todas',
            icon: 'list',
          },
          {
            value: 'income',
            label: 'Receitas',
            icon: 'trending-up',
          },
          {
            value: 'expense',
            label: 'Despesas',
            icon: 'trending-down',
          },
        ]}
        style={styles.segmentedButtons}
      />

      <Searchbar
        placeholder="Buscar transações..."
        onChangeText={handleSearch}
        value={searchQuery}
        style={styles.searchbar}
      />
      
      <View style={styles.filtersContainer}>
        <TouchableOpacity onPress={() => setShowFilters(true)}>
          <Chip
            icon="filter-variant"
            style={styles.filterChip}
            textStyle={styles.filterChipText}
          >
            Filtros {getActiveFiltersCount() > 0 && `(${getActiveFiltersCount()})`}
          </Chip>
        </TouchableOpacity>
      </View>

      <View style={styles.resultsContainer}>
        <Text variant="bodyMedium" style={styles.resultsText}>
          {filteredTransactions.length} transação{filteredTransactions.length !== 1 ? 'ões' : ''} encontrada{filteredTransactions.length !== 1 ? 's' : ''}
        </Text>
      </View>
    </View>
  );

  const renderEmpty = () => (
    <View style={styles.emptyContainer}>
      <Text variant="titleMedium" style={styles.emptyTitle}>
        Nenhuma transação encontrada
      </Text>
      <Text variant="bodyMedium" style={styles.emptyMessage}>
        {searchQuery || Object.keys(filters).length > 0
          ? 'Tente ajustar os filtros de busca'
          : 'Adicione a primeira transação financeira'
        }
      </Text>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text variant="bodyMedium" style={styles.loadingText}>
          Carregando transações...
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <FlatList
        data={filteredTransactions}
        renderItem={renderTransaction}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={renderEmpty}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
      />

      <FAB
        icon="plus"
        style={[styles.fab, { backgroundColor: theme.colors.primary }]}
        onPress={handleAddTransaction}
        label="Nova Transação"
      />

      <FinancialFilterBottomSheet
        visible={showFilters}
        onDismiss={() => setShowFilters(false)}
        onApply={handleFilter}
        currentFilters={filters}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: spacing.md,
    opacity: 0.7,
  },
  listContent: {
    flexGrow: 1,
  },
  header: {
    padding: spacing.md,
    paddingBottom: spacing.sm,
  },
  summaryCard: {
    marginBottom: spacing.md,
  },
  summaryContent: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: spacing.sm,
  },
  summaryItem: {
    alignItems: 'center',
  },
  summaryLabel: {
    opacity: 0.7,
    marginBottom: spacing.xs,
  },
  summaryValue: {
    fontWeight: 'bold',
  },
  segmentedButtons: {
    marginBottom: spacing.md,
  },
  searchbar: {
    marginBottom: spacing.md,
  },
  filtersContainer: {
    flexDirection: 'row',
    marginBottom: spacing.md,
  },
  filterChip: {
    marginRight: spacing.sm,
  },
  filterChipText: {
    fontSize: 12,
  },
  resultsContainer: {
    marginBottom: spacing.sm,
  },
  resultsText: {
    opacity: 0.7,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.xxxl,
  },
  emptyTitle: {
    fontWeight: '600',
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  emptyMessage: {
    textAlign: 'center',
    opacity: 0.7,
    lineHeight: 20,
  },
  fab: {
    position: 'absolute',
    margin: spacing.md,
    right: 0,
    bottom: 0,
  },
});
