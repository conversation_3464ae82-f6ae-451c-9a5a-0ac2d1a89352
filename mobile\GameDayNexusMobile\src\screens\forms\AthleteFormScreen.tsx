import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
  Image,
  TouchableOpacity,
} from 'react-native';
import {
  Text,
  Card,
  TextInput,
  Button,
  useTheme,
  ActivityIndicator,
  SegmentedButtons,
  Avatar,
  Chip,
} from 'react-native-paper';
// import { Picker } from '@react-native-picker/picker';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { spacing } from '@/theme';
import { AthleteFormData, validateRequired, validateEmail, validatePhone, validateCPF } from '@/types/forms';
import { Athlete, Category } from '@/types/athletes';

interface AthleteFormScreenProps {
  route: {
    params?: {
      athleteId?: string;
      mode?: 'create' | 'edit';
    };
  };
  navigation: any;
}

export default function AthleteFormScreen({ route, navigation }: AthleteFormScreenProps) {
  const theme = useTheme();
  const { athleteId, mode = 'create' } = route.params || {};
  
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [selectedTab, setSelectedTab] = useState('basic');
  const [formData, setFormData] = useState<AthleteFormData>({
    first_name: '',
    last_name: '',
    birth_date: '',
    gender: 'male',
    nationality: 'Brasil',
    document_type: 'cpf',
    document_number: '',
    category_id: '',
    position: '',
    preferred_foot: 'right',
    status: 'active',
    join_date: new Date().toISOString().split('T')[0],
  });
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [categories, setCategories] = useState<Category[]>([]);

  // Mock data
  const mockCategories: Category[] = [
    { id: '1', name: 'Sub-15', description: 'Categoria Sub-15', min_age: 13, max_age: 15, active: true, created_at: '', updated_at: '' },
    { id: '2', name: 'Sub-17', description: 'Categoria Sub-17', min_age: 15, max_age: 17, active: true, created_at: '', updated_at: '' },
    { id: '3', name: 'Sub-20', description: 'Categoria Sub-20', min_age: 17, max_age: 20, active: true, created_at: '', updated_at: '' },
    { id: '4', name: 'Profissional', description: 'Categoria Profissional', min_age: 18, max_age: 40, active: true, created_at: '', updated_at: '' },
  ];

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      // Carregar categorias
      setCategories(mockCategories);
      
      // Se for edição, carregar dados do atleta
      if (mode === 'edit' && athleteId) {
        // Mock data para edição
        const mockAthlete: AthleteFormData = {
          first_name: 'João',
          last_name: 'Silva',
          birth_date: '2005-03-15',
          gender: 'male',
          nationality: 'Brasil',
          document_type: 'cpf',
          document_number: '123.456.789-00',
          phone: '(11) 99999-1111',
          email: '<EMAIL>',
          category_id: '2',
          position: 'Atacante',
          jersey_number: 10,
          preferred_foot: 'right',
          height: 175,
          weight: 70,
          status: 'active',
          join_date: '2024-01-15',
          notes: 'Jogador promissor com boa técnica.',
        };
        setFormData(mockAthlete);
      }
    } catch (error) {
      console.error('Erro ao carregar dados:', error);
    } finally {
      setLoading(false);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: { [key: string]: string } = {};

    // Validações obrigatórias
    if (!formData.first_name) newErrors.first_name = 'Nome é obrigatório';
    if (!formData.last_name) newErrors.last_name = 'Sobrenome é obrigatório';
    if (!formData.birth_date) newErrors.birth_date = 'Data de nascimento é obrigatória';
    if (!formData.category_id) newErrors.category_id = 'Categoria é obrigatória';
    if (!formData.position) newErrors.position = 'Posição é obrigatória';

    // Validações específicas
    if (formData.email) {
      const emailError = validateEmail(formData.email);
      if (emailError) newErrors.email = emailError;
    }

    if (formData.phone) {
      const phoneError = validatePhone(formData.phone);
      if (phoneError) newErrors.phone = phoneError;
    }

    if (formData.document_type === 'cpf' && formData.document_number) {
      const cpfError = validateCPF(formData.document_number);
      if (cpfError) newErrors.document_number = cpfError;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      Alert.alert('Erro', 'Por favor, corrija os erros no formulário.');
      return;
    }

    setSaving(true);
    try {
      // Simular salvamento na API
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      Alert.alert(
        'Sucesso',
        `Atleta ${mode === 'create' ? 'criado' : 'atualizado'} com sucesso!`,
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error) {
      Alert.alert(
        'Erro',
        'Erro ao salvar atleta. Tente novamente.',
        [{ text: 'OK' }]
      );
    } finally {
      setSaving(false);
    }
  };

  const updateField = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Limpar erro do campo quando o usuário começar a digitar
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handlePhotoChange = () => {
    Alert.alert(
      'Alterar Foto',
      'Escolha uma opção:',
      [
        { text: 'Cancelar', style: 'cancel' },
        { text: 'Câmera', onPress: () => console.log('Abrir câmera') },
        { text: 'Galeria', onPress: () => console.log('Abrir galeria') },
      ]
    );
  };

  const renderBasicInfo = () => (
    <Card style={styles.card}>
      <Card.Content>
        <Text variant="titleMedium" style={styles.sectionTitle}>
          Informações Básicas
        </Text>
        
        {/* Foto */}
        <View style={styles.photoSection}>
          <TouchableOpacity onPress={handlePhotoChange} style={styles.photoContainer}>
            {formData.photo_url ? (
              <Image source={{ uri: formData.photo_url }} style={styles.photo} />
            ) : (
              <Avatar.Icon size={100} icon="person" />
            )}
            <View style={styles.photoOverlay}>
              <Icon name="camera-alt" size={24} color="#ffffff" />
            </View>
          </TouchableOpacity>
          
          <View style={styles.photoInfo}>
            <Text variant="bodySmall" style={styles.photoLabel}>
              Foto do Atleta
            </Text>
            <Text variant="bodySmall" style={styles.photoDescription}>
              Toque para alterar
            </Text>
          </View>
        </View>
        
        {/* Nome */}
        <View style={styles.nameRow}>
          <TextInput
            label="Nome *"
            value={formData.first_name}
            onChangeText={(text) => updateField('first_name', text)}
            style={[styles.input, styles.nameInput]}
            mode="outlined"
            error={!!errors.first_name}
          />
          
          <TextInput
            label="Sobrenome *"
            value={formData.last_name}
            onChangeText={(text) => updateField('last_name', text)}
            style={[styles.input, styles.nameInput]}
            mode="outlined"
            error={!!errors.last_name}
          />
        </View>
        
        {errors.first_name && <Text style={styles.errorText}>{errors.first_name}</Text>}
        {errors.last_name && <Text style={styles.errorText}>{errors.last_name}</Text>}
        
        {/* Data de Nascimento */}
        <TextInput
          label="Data de Nascimento *"
          value={formData.birth_date}
          onChangeText={(text) => updateField('birth_date', text)}
          style={styles.input}
          mode="outlined"
          placeholder="AAAA-MM-DD"
          error={!!errors.birth_date}
        />
        {errors.birth_date && <Text style={styles.errorText}>{errors.birth_date}</Text>}
        
        {/* Gênero */}
        <Text variant="bodyMedium" style={styles.fieldLabel}>Gênero</Text>
        <SegmentedButtons
          value={formData.gender}
          onValueChange={(value) => updateField('gender', value)}
          buttons={[
            { value: 'male', label: 'Masculino' },
            { value: 'female', label: 'Feminino' },
            { value: 'other', label: 'Outro' },
          ]}
          style={styles.segmentedButtons}
        />
        
        {/* Nacionalidade */}
        <TextInput
          label="Nacionalidade"
          value={formData.nationality}
          onChangeText={(text) => updateField('nationality', text)}
          style={styles.input}
          mode="outlined"
        />
        
        {/* Documento */}
        <Text variant="bodyMedium" style={styles.fieldLabel}>Tipo de Documento</Text>
        <SegmentedButtons
          value={formData.document_type}
          onValueChange={(value) => updateField('document_type', value)}
          buttons={[
            { value: 'cpf', label: 'CPF' },
            { value: 'rg', label: 'RG' },
            { value: 'passport', label: 'Passaporte' },
          ]}
          style={styles.segmentedButtons}
        />
        
        <TextInput
          label={`Número do ${formData.document_type.toUpperCase()}`}
          value={formData.document_number}
          onChangeText={(text) => updateField('document_number', text)}
          style={styles.input}
          mode="outlined"
          error={!!errors.document_number}
        />
        {errors.document_number && <Text style={styles.errorText}>{errors.document_number}</Text>}
      </Card.Content>
    </Card>
  );

  const renderContactInfo = () => (
    <Card style={styles.card}>
      <Card.Content>
        <Text variant="titleMedium" style={styles.sectionTitle}>
          Informações de Contato
        </Text>
        
        <TextInput
          label="Telefone"
          value={formData.phone || ''}
          onChangeText={(text) => updateField('phone', text)}
          style={styles.input}
          mode="outlined"
          keyboardType="phone-pad"
          placeholder="(11) 99999-9999"
          error={!!errors.phone}
        />
        {errors.phone && <Text style={styles.errorText}>{errors.phone}</Text>}
        
        <TextInput
          label="E-mail"
          value={formData.email || ''}
          onChangeText={(text) => updateField('email', text)}
          style={styles.input}
          mode="outlined"
          keyboardType="email-address"
          error={!!errors.email}
        />
        {errors.email && <Text style={styles.errorText}>{errors.email}</Text>}
        
        <Text variant="titleSmall" style={styles.subsectionTitle}>
          Contato de Emergência
        </Text>
        
        <TextInput
          label="Nome do Contato"
          value={formData.emergency_contact_name || ''}
          onChangeText={(text) => updateField('emergency_contact_name', text)}
          style={styles.input}
          mode="outlined"
        />
        
        <TextInput
          label="Telefone do Contato"
          value={formData.emergency_contact_phone || ''}
          onChangeText={(text) => updateField('emergency_contact_phone', text)}
          style={styles.input}
          mode="outlined"
          keyboardType="phone-pad"
        />
        
        <TextInput
          label="Parentesco"
          value={formData.emergency_contact_relationship || ''}
          onChangeText={(text) => updateField('emergency_contact_relationship', text)}
          style={styles.input}
          mode="outlined"
          placeholder="Ex: Pai, Mãe, Responsável"
        />
      </Card.Content>
    </Card>
  );

  const renderSportsInfo = () => (
    <Card style={styles.card}>
      <Card.Content>
        <Text variant="titleMedium" style={styles.sectionTitle}>
          Informações Esportivas
        </Text>
        
        {/* Categoria */}
        <TextInput
          label="Categoria *"
          value={categories.find(c => c.id === formData.category_id)?.name || ''}
          onChangeText={() => {}} // Implementar seletor
          style={styles.input}
          mode="outlined"
          right={<TextInput.Icon icon="chevron-down" />}
          editable={false}
          error={!!errors.category_id}
        />
        {errors.category_id && <Text style={styles.errorText}>{errors.category_id}</Text>}
        
        {/* Posição */}
        <TextInput
          label="Posição *"
          value={formData.position}
          onChangeText={(text) => updateField('position', text)}
          style={styles.input}
          mode="outlined"
          placeholder="Ex: Atacante, Meio-campo, Zagueiro"
          error={!!errors.position}
        />
        {errors.position && <Text style={styles.errorText}>{errors.position}</Text>}
        
        {/* Número da Camisa */}
        <TextInput
          label="Número da Camisa"
          value={formData.jersey_number?.toString() || ''}
          onChangeText={(text) => updateField('jersey_number', parseInt(text) || undefined)}
          style={styles.input}
          mode="outlined"
          keyboardType="numeric"
        />
        
        {/* Pé Preferido */}
        <Text variant="bodyMedium" style={styles.fieldLabel}>Pé Preferido</Text>
        <SegmentedButtons
          value={formData.preferred_foot}
          onValueChange={(value) => updateField('preferred_foot', value)}
          buttons={[
            { value: 'left', label: 'Esquerdo' },
            { value: 'right', label: 'Direito' },
            { value: 'both', label: 'Ambos' },
          ]}
          style={styles.segmentedButtons}
        />
        
        {/* Medidas */}
        <View style={styles.measurementsRow}>
          <TextInput
            label="Altura (cm)"
            value={formData.height?.toString() || ''}
            onChangeText={(text) => updateField('height', parseInt(text) || undefined)}
            style={[styles.input, styles.measurementInput]}
            mode="outlined"
            keyboardType="numeric"
          />
          
          <TextInput
            label="Peso (kg)"
            value={formData.weight?.toString() || ''}
            onChangeText={(text) => updateField('weight', parseInt(text) || undefined)}
            style={[styles.input, styles.measurementInput]}
            mode="outlined"
            keyboardType="numeric"
          />
        </View>
        
        {/* Status */}
        <Text variant="bodyMedium" style={styles.fieldLabel}>Status</Text>
        <View style={styles.statusContainer}>
          {[
            { value: 'active', label: 'Ativo', color: '#4caf50' },
            { value: 'inactive', label: 'Inativo', color: '#9e9e9e' },
            { value: 'injured', label: 'Lesionado', color: '#f44336' },
            { value: 'suspended', label: 'Suspenso', color: '#ff9800' },
            { value: 'loaned', label: 'Emprestado', color: '#2196f3' },
          ].map((status) => (
            <Chip
              key={status.value}
              selected={formData.status === status.value}
              onPress={() => updateField('status', status.value)}
              style={[
                styles.statusChip,
                formData.status === status.value && { backgroundColor: `${status.color}20` }
              ]}
              textStyle={[
                styles.statusText,
                formData.status === status.value && { color: status.color }
              ]}
            >
              {status.label}
            </Chip>
          ))}
        </View>
        
        {/* Datas */}
        <TextInput
          label="Data de Ingresso *"
          value={formData.join_date}
          onChangeText={(text) => updateField('join_date', text)}
          style={styles.input}
          mode="outlined"
          placeholder="AAAA-MM-DD"
        />
        
        <TextInput
          label="Fim do Contrato"
          value={formData.contract_end_date || ''}
          onChangeText={(text) => updateField('contract_end_date', text)}
          style={styles.input}
          mode="outlined"
          placeholder="AAAA-MM-DD"
        />
        
        {/* Observações */}
        <TextInput
          label="Observações"
          value={formData.notes || ''}
          onChangeText={(text) => updateField('notes', text)}
          style={styles.input}
          mode="outlined"
          multiline
          numberOfLines={3}
          placeholder="Informações adicionais sobre o atleta..."
        />
      </Card.Content>
    </Card>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text variant="bodyMedium" style={styles.loadingText}>
          Carregando formulário...
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Tabs */}
      <View style={styles.tabsContainer}>
        <SegmentedButtons
          value={selectedTab}
          onValueChange={setSelectedTab}
          buttons={[
            { value: 'basic', label: 'Básico', icon: 'person' },
            { value: 'contact', label: 'Contato', icon: 'phone' },
            { value: 'sports', label: 'Esportivo', icon: 'sports' },
          ]}
          style={styles.tabs}
        />
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {selectedTab === 'basic' && renderBasicInfo()}
        {selectedTab === 'contact' && renderContactInfo()}
        {selectedTab === 'sports' && renderSportsInfo()}
      </ScrollView>

      {/* Botões de Ação */}
      <View style={styles.actions}>
        <Button
          mode="outlined"
          onPress={() => navigation.goBack()}
          style={styles.actionButton}
          disabled={saving}
        >
          Cancelar
        </Button>
        
        <Button
          mode="contained"
          onPress={handleSave}
          style={styles.actionButton}
          loading={saving}
          disabled={saving}
        >
          {mode === 'create' ? 'Criar Atleta' : 'Salvar Alterações'}
        </Button>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: spacing.md,
    opacity: 0.7,
  },
  tabsContainer: {
    padding: spacing.md,
    paddingBottom: spacing.sm,
  },
  tabs: {
    marginBottom: spacing.sm,
  },
  scrollView: {
    flex: 1,
  },
  card: {
    margin: spacing.md,
    marginBottom: spacing.sm,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: spacing.lg,
  },
  subsectionTitle: {
    fontWeight: '500',
    marginTop: spacing.lg,
    marginBottom: spacing.md,
    opacity: 0.8,
  },
  fieldLabel: {
    fontWeight: '500',
    marginBottom: spacing.sm,
    opacity: 0.8,
  },
  photoSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  photoContainer: {
    position: 'relative',
    marginRight: spacing.lg,
  },
  photo: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  photoOverlay: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(0,0,0,0.6)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  photoInfo: {
    flex: 1,
  },
  photoLabel: {
    fontWeight: '500',
    marginBottom: spacing.xs,
  },
  photoDescription: {
    opacity: 0.7,
  },
  nameRow: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  nameInput: {
    flex: 1,
  },
  measurementsRow: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  measurementInput: {
    flex: 1,
  },
  input: {
    marginBottom: spacing.md,
  },
  segmentedButtons: {
    marginBottom: spacing.md,
  },

  statusContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
    marginBottom: spacing.md,
  },
  statusChip: {
    marginBottom: spacing.sm,
  },
  statusText: {
    fontSize: 12,
  },
  errorText: {
    color: '#f44336',
    fontSize: 12,
    marginTop: -spacing.md,
    marginBottom: spacing.md,
    marginLeft: spacing.md,
  },
  actions: {
    flexDirection: 'row',
    padding: spacing.md,
    gap: spacing.md,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  actionButton: {
    flex: 1,
  },
});
