import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import {
  Text,
  Card,
  TextInput,
  Button,
  useTheme,
  ActivityIndicator,
  SegmentedButtons,
  Chip,
  Switch,
  List,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { spacing } from '@/theme';
import { FinancialFormData, validateRequired, formatCurrency } from '@/types/forms';
import { FinancialCategory } from '@/types/financial';

interface FinancialFormScreenProps {
  route: {
    params?: {
      transactionId?: string;
      mode?: 'create' | 'edit';
      type?: 'income' | 'expense';
    };
  };
  navigation: any;
}

export default function FinancialFormScreen({ route, navigation }: FinancialFormScreenProps) {
  const theme = useTheme();
  const { transactionId, mode = 'create', type = 'income' } = route.params || {};
  
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [formData, setFormData] = useState<FinancialFormData>({
    type: type,
    category_id: '',
    amount: 0,
    description: '',
    date: new Date().toISOString().split('T')[0],
    payment_method: 'cash',
    status: 'pending',
    is_recurring: false,
  });
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [categories, setCategories] = useState<FinancialCategory[]>([]);

  // Mock data
  const mockIncomeCategories: FinancialCategory[] = [
    { id: '1', name: 'Mensalidades', type: 'income', description: 'Mensalidades dos atletas', color: '#4caf50', created_at: '', updated_at: '' },
    { id: '2', name: 'Patrocínios', type: 'income', description: 'Receitas de patrocínio', color: '#2196f3', created_at: '', updated_at: '' },
    { id: '3', name: 'Eventos', type: 'income', description: 'Receitas de eventos', color: '#ff9800', created_at: '', updated_at: '' },
    { id: '4', name: 'Vendas', type: 'income', description: 'Vendas de produtos', color: '#9c27b0', created_at: '', updated_at: '' },
  ];

  const mockExpenseCategories: FinancialCategory[] = [
    { id: '5', name: 'Salários', type: 'expense', description: 'Pagamento de funcionários', color: '#f44336', created_at: '', updated_at: '' },
    { id: '6', name: 'Material Esportivo', type: 'expense', description: 'Compra de equipamentos', color: '#607d8b', created_at: '', updated_at: '' },
    { id: '7', name: 'Transporte', type: 'expense', description: 'Despesas de transporte', color: '#795548', created_at: '', updated_at: '' },
    { id: '8', name: 'Manutenção', type: 'expense', description: 'Manutenção de instalações', color: '#9e9e9e', created_at: '', updated_at: '' },
  ];

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      // Carregar categorias baseadas no tipo
      const allCategories = [...mockIncomeCategories, ...mockExpenseCategories];
      setCategories(allCategories.filter(cat => cat.type === formData.type));
      
      // Se for edição, carregar dados da transação
      if (mode === 'edit' && transactionId) {
        // Mock data para edição
        const mockTransaction: FinancialFormData = {
          type: 'income',
          category_id: '1',
          amount: 1500.00,
          description: 'Mensalidade - João Silva',
          date: '2025-08-24',
          due_date: '2025-08-30',
          payment_method: 'pix',
          status: 'paid',
          is_recurring: true,
          recurring_frequency: 'monthly',
          notes: 'Pagamento em dia.',
        };
        setFormData(mockTransaction);
      }
    } catch (error) {
      console.error('Erro ao carregar dados:', error);
    } finally {
      setLoading(false);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: { [key: string]: string } = {};

    // Validações obrigatórias
    if (!formData.category_id) newErrors.category_id = 'Categoria é obrigatória';
    if (!formData.amount || formData.amount <= 0) newErrors.amount = 'Valor deve ser maior que zero';
    if (!formData.description) newErrors.description = 'Descrição é obrigatória';
    if (!formData.date) newErrors.date = 'Data é obrigatória';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      Alert.alert('Erro', 'Por favor, corrija os erros no formulário.');
      return;
    }

    setSaving(true);
    try {
      // Simular salvamento na API
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      Alert.alert(
        'Sucesso',
        `Transação ${mode === 'create' ? 'criada' : 'atualizada'} com sucesso!`,
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error) {
      Alert.alert(
        'Erro',
        'Erro ao salvar transação. Tente novamente.',
        [{ text: 'OK' }]
      );
    } finally {
      setSaving(false);
    }
  };

  const updateField = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Limpar erro do campo quando o usuário começar a digitar
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleTypeChange = (newType: 'income' | 'expense') => {
    updateField('type', newType);
    updateField('category_id', ''); // Reset category
    // Atualizar categorias
    const allCategories = [...mockIncomeCategories, ...mockExpenseCategories];
    setCategories(allCategories.filter(cat => cat.type === newType));
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text variant="bodyMedium" style={styles.loadingText}>
          Carregando formulário...
        </Text>
      </View>
    );
  }

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Tipo de Transação */}
      <Card style={styles.card}>
        <Card.Content>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Tipo de Transação
          </Text>
          
          <SegmentedButtons
            value={formData.type}
            onValueChange={handleTypeChange}
            buttons={[
              { 
                value: 'income', 
                label: 'Receita', 
                icon: 'trending-up',
                style: { backgroundColor: formData.type === 'income' ? '#4caf5020' : undefined }
              },
              { 
                value: 'expense', 
                label: 'Despesa', 
                icon: 'trending-down',
                style: { backgroundColor: formData.type === 'expense' ? '#f4433620' : undefined }
              },
            ]}
            style={styles.segmentedButtons}
          />
        </Card.Content>
      </Card>

      {/* Informações Básicas */}
      <Card style={styles.card}>
        <Card.Content>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Informações da Transação
          </Text>
          
          {/* Categoria */}
          <TextInput
            label="Categoria *"
            value={categories.find(c => c.id === formData.category_id)?.name || ''}
            onChangeText={() => {}} // Implementar seletor
            style={styles.input}
            mode="outlined"
            right={<TextInput.Icon icon="chevron-down" />}
            editable={false}
            error={!!errors.category_id}
          />
          {errors.category_id && <Text style={styles.errorText}>{errors.category_id}</Text>}
          
          {/* Valor */}
          <TextInput
            label="Valor *"
            value={formData.amount.toString()}
            onChangeText={(text) => updateField('amount', parseFloat(text) || 0)}
            style={styles.input}
            mode="outlined"
            keyboardType="numeric"
            left={<TextInput.Icon icon="attach-money" />}
            placeholder="0,00"
            error={!!errors.amount}
          />
          {errors.amount && <Text style={styles.errorText}>{errors.amount}</Text>}
          
          {/* Descrição */}
          <TextInput
            label="Descrição *"
            value={formData.description}
            onChangeText={(text) => updateField('description', text)}
            style={styles.input}
            mode="outlined"
            placeholder="Descreva a transação..."
            error={!!errors.description}
          />
          {errors.description && <Text style={styles.errorText}>{errors.description}</Text>}
        </Card.Content>
      </Card>

      {/* Datas */}
      <Card style={styles.card}>
        <Card.Content>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Datas
          </Text>
          
          <View style={styles.dateRow}>
            <TextInput
              label="Data da Transação *"
              value={formData.date}
              onChangeText={(text) => updateField('date', text)}
              style={[styles.input, styles.dateInput]}
              mode="outlined"
              placeholder="AAAA-MM-DD"
              error={!!errors.date}
            />
            
            <TextInput
              label="Data de Vencimento"
              value={formData.due_date || ''}
              onChangeText={(text) => updateField('due_date', text)}
              style={[styles.input, styles.dateInput]}
              mode="outlined"
              placeholder="AAAA-MM-DD"
            />
          </View>
          
          {errors.date && <Text style={styles.errorText}>{errors.date}</Text>}
        </Card.Content>
      </Card>

      {/* Método de Pagamento e Status */}
      <Card style={styles.card}>
        <Card.Content>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Pagamento
          </Text>
          
          {/* Método de Pagamento */}
          <Text variant="bodyMedium" style={styles.fieldLabel}>Método de Pagamento</Text>
          <View style={styles.paymentMethodContainer}>
            {[
              { value: 'cash', label: 'Dinheiro', icon: 'payments' },
              { value: 'credit_card', label: 'Cartão de Crédito', icon: 'credit-card' },
              { value: 'debit_card', label: 'Cartão de Débito', icon: 'credit-card' },
              { value: 'bank_transfer', label: 'Transferência', icon: 'account-balance' },
              { value: 'pix', label: 'PIX', icon: 'qr-code' },
              { value: 'check', label: 'Cheque', icon: 'receipt' },
            ].map((method) => (
              <Chip
                key={method.value}
                selected={formData.payment_method === method.value}
                onPress={() => updateField('payment_method', method.value)}
                style={styles.paymentChip}
                icon={method.icon}
              >
                {method.label}
              </Chip>
            ))}
          </View>
          
          {/* Status */}
          <Text variant="bodyMedium" style={styles.fieldLabel}>Status</Text>
          <View style={styles.statusContainer}>
            {[
              { value: 'pending', label: 'Pendente', color: '#ff9800' },
              { value: 'paid', label: 'Pago', color: '#4caf50' },
              { value: 'overdue', label: 'Vencido', color: '#f44336' },
              { value: 'cancelled', label: 'Cancelado', color: '#9e9e9e' },
            ].map((status) => (
              <Chip
                key={status.value}
                selected={formData.status === status.value}
                onPress={() => updateField('status', status.value)}
                style={[
                  styles.statusChip,
                  formData.status === status.value && { backgroundColor: `${status.color}20` }
                ]}
                textStyle={[
                  styles.statusText,
                  formData.status === status.value && { color: status.color }
                ]}
              >
                {status.label}
              </Chip>
            ))}
          </View>
        </Card.Content>
      </Card>

      {/* Recorrência */}
      <Card style={styles.card}>
        <Card.Content>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Recorrência
          </Text>
          
          <List.Item
            title="Transação Recorrente"
            description="Esta transação se repete automaticamente"
            left={(props) => <List.Icon {...props} icon="repeat" />}
            right={() => (
              <Switch
                value={formData.is_recurring || false}
                onValueChange={(value) => updateField('is_recurring', value)}
              />
            )}
          />
          
          {formData.is_recurring && (
            <>
              <Text variant="bodyMedium" style={styles.fieldLabel}>Frequência</Text>
              <SegmentedButtons
                value={formData.recurring_frequency || 'monthly'}
                onValueChange={(value) => updateField('recurring_frequency', value)}
                buttons={[
                  { value: 'weekly', label: 'Semanal' },
                  { value: 'monthly', label: 'Mensal' },
                  { value: 'quarterly', label: 'Trimestral' },
                  { value: 'yearly', label: 'Anual' },
                ]}
                style={styles.segmentedButtons}
              />
              
              <TextInput
                label="Data de Término"
                value={formData.recurring_end_date || ''}
                onChangeText={(text) => updateField('recurring_end_date', text)}
                style={styles.input}
                mode="outlined"
                placeholder="AAAA-MM-DD (opcional)"
              />
            </>
          )}
        </Card.Content>
      </Card>

      {/* Observações */}
      <Card style={styles.card}>
        <Card.Content>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Informações Adicionais
          </Text>
          
          <TextInput
            label="Observações"
            value={formData.notes || ''}
            onChangeText={(text) => updateField('notes', text)}
            style={styles.input}
            mode="outlined"
            multiline
            numberOfLines={3}
            placeholder="Informações adicionais sobre a transação..."
          />
        </Card.Content>
      </Card>

      {/* Botões de Ação */}
      <View style={styles.actions}>
        <Button
          mode="outlined"
          onPress={() => navigation.goBack()}
          style={styles.actionButton}
          disabled={saving}
        >
          Cancelar
        </Button>
        
        <Button
          mode="contained"
          onPress={handleSave}
          style={styles.actionButton}
          loading={saving}
          disabled={saving}
        >
          {mode === 'create' ? 'Criar Transação' : 'Salvar Alterações'}
        </Button>
      </View>

      <View style={styles.bottomSpacing} />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: spacing.md,
    opacity: 0.7,
  },
  card: {
    margin: spacing.md,
    marginBottom: spacing.sm,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: spacing.lg,
  },
  fieldLabel: {
    fontWeight: '500',
    marginBottom: spacing.sm,
    opacity: 0.8,
  },
  input: {
    marginBottom: spacing.md,
  },
  segmentedButtons: {
    marginBottom: spacing.md,
  },
  dateRow: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  dateInput: {
    flex: 1,
  },
  paymentMethodContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
    marginBottom: spacing.md,
  },
  paymentChip: {
    marginBottom: spacing.sm,
  },
  statusContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
    marginBottom: spacing.md,
  },
  statusChip: {
    marginBottom: spacing.sm,
  },
  statusText: {
    fontSize: 12,
  },
  errorText: {
    color: '#f44336',
    fontSize: 12,
    marginTop: -spacing.md,
    marginBottom: spacing.md,
    marginLeft: spacing.md,
  },
  actions: {
    flexDirection: 'row',
    padding: spacing.md,
    gap: spacing.md,
  },
  actionButton: {
    flex: 1,
  },
  bottomSpacing: {
    height: spacing.xl,
  },
});
