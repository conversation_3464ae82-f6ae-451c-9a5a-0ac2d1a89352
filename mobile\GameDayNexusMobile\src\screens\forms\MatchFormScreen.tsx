import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import {
  Text,
  Card,
  TextInput,
  Button,
  useTheme,
  ActivityIndicator,
  SegmentedButtons,
  Chip,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { spacing } from '@/theme';
import { MatchFormData, validateRequired } from '@/types/forms';
import { Category } from '@/types/athletes';

interface MatchFormScreenProps {
  route: {
    params?: {
      matchId?: string;
      mode?: 'create' | 'edit';
    };
  };
  navigation: any;
}

export default function MatchFormScreen({ route, navigation }: MatchFormScreenProps) {
  const theme = useTheme();
  const { matchId, mode = 'create' } = route.params || {};
  
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [formData, setFormData] = useState<MatchFormData>({
    opponent: '',
    competition: '',
    match_type: 'friendly',
    home_away: 'home',
    date: '',
    time: '',
    venue: '',
    category_id: '',
    status: 'scheduled',
  });
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [categories, setCategories] = useState<Category[]>([]);

  // Mock data
  const mockCategories: Category[] = [
    { id: '1', name: 'Sub-15', description: 'Categoria Sub-15', min_age: 13, max_age: 15, active: true, created_at: '', updated_at: '' },
    { id: '2', name: 'Sub-17', description: 'Categoria Sub-17', min_age: 15, max_age: 17, active: true, created_at: '', updated_at: '' },
    { id: '3', name: 'Sub-20', description: 'Categoria Sub-20', min_age: 17, max_age: 20, active: true, created_at: '', updated_at: '' },
    { id: '4', name: 'Profissional', description: 'Categoria Profissional', min_age: 18, max_age: 40, active: true, created_at: '', updated_at: '' },
  ];

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      // Carregar categorias
      setCategories(mockCategories);
      
      // Se for edição, carregar dados da partida
      if (mode === 'edit' && matchId) {
        // Mock data para edição
        const mockMatch: MatchFormData = {
          opponent: 'Santos FC',
          competition: 'Campeonato Paulista',
          match_type: 'championship',
          home_away: 'home',
          date: '2025-09-15',
          time: '15:30',
          venue: 'Estádio Municipal',
          venue_address: 'Rua do Estádio, 123 - Centro',
          category_id: '2',
          status: 'scheduled',
          referee: 'João Arbitro',
          notes: 'Partida importante para classificação.',
        };
        setFormData(mockMatch);
      }
    } catch (error) {
      console.error('Erro ao carregar dados:', error);
    } finally {
      setLoading(false);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: { [key: string]: string } = {};

    // Validações obrigatórias
    if (!formData.opponent) newErrors.opponent = 'Adversário é obrigatório';
    if (!formData.competition) newErrors.competition = 'Competição é obrigatória';
    if (!formData.date) newErrors.date = 'Data é obrigatória';
    if (!formData.time) newErrors.time = 'Horário é obrigatório';
    if (!formData.venue) newErrors.venue = 'Local é obrigatório';
    if (!formData.category_id) newErrors.category_id = 'Categoria é obrigatória';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      Alert.alert('Erro', 'Por favor, corrija os erros no formulário.');
      return;
    }

    setSaving(true);
    try {
      // Simular salvamento na API
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      Alert.alert(
        'Sucesso',
        `Partida ${mode === 'create' ? 'criada' : 'atualizada'} com sucesso!`,
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error) {
      Alert.alert(
        'Erro',
        'Erro ao salvar partida. Tente novamente.',
        [{ text: 'OK' }]
      );
    } finally {
      setSaving(false);
    }
  };

  const updateField = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Limpar erro do campo quando o usuário começar a digitar
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text variant="bodyMedium" style={styles.loadingText}>
          Carregando formulário...
        </Text>
      </View>
    );
  }

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Informações Básicas */}
      <Card style={styles.card}>
        <Card.Content>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Informações da Partida
          </Text>
          
          <TextInput
            label="Adversário *"
            value={formData.opponent}
            onChangeText={(text) => updateField('opponent', text)}
            style={styles.input}
            mode="outlined"
            error={!!errors.opponent}
          />
          {errors.opponent && <Text style={styles.errorText}>{errors.opponent}</Text>}
          
          <TextInput
            label="Competição *"
            value={formData.competition}
            onChangeText={(text) => updateField('competition', text)}
            style={styles.input}
            mode="outlined"
            placeholder="Ex: Campeonato Paulista, Copa do Brasil"
            error={!!errors.competition}
          />
          {errors.competition && <Text style={styles.errorText}>{errors.competition}</Text>}
          
          {/* Tipo de Partida */}
          <Text variant="bodyMedium" style={styles.fieldLabel}>Tipo de Partida</Text>
          <SegmentedButtons
            value={formData.match_type}
            onValueChange={(value) => updateField('match_type', value)}
            buttons={[
              { value: 'friendly', label: 'Amistoso' },
              { value: 'championship', label: 'Campeonato' },
              { value: 'cup', label: 'Copa' },
              { value: 'playoff', label: 'Playoff' },
            ]}
            style={styles.segmentedButtons}
          />
          
          {/* Casa/Visitante */}
          <Text variant="bodyMedium" style={styles.fieldLabel}>Local da Partida</Text>
          <SegmentedButtons
            value={formData.home_away}
            onValueChange={(value) => updateField('home_away', value)}
            buttons={[
              { value: 'home', label: 'Casa', icon: 'home' },
              { value: 'away', label: 'Visitante', icon: 'flight-takeoff' },
            ]}
            style={styles.segmentedButtons}
          />
          
          {/* Categoria */}
          <TextInput
            label="Categoria *"
            value={categories.find(c => c.id === formData.category_id)?.name || ''}
            onChangeText={() => {}} // Implementar seletor
            style={styles.input}
            mode="outlined"
            right={<TextInput.Icon icon="chevron-down" />}
            editable={false}
            error={!!errors.category_id}
          />
          {errors.category_id && <Text style={styles.errorText}>{errors.category_id}</Text>}
        </Card.Content>
      </Card>

      {/* Data e Horário */}
      <Card style={styles.card}>
        <Card.Content>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Data e Horário
          </Text>
          
          <View style={styles.dateTimeRow}>
            <TextInput
              label="Data *"
              value={formData.date}
              onChangeText={(text) => updateField('date', text)}
              style={[styles.input, styles.dateInput]}
              mode="outlined"
              placeholder="AAAA-MM-DD"
              error={!!errors.date}
            />
            
            <TextInput
              label="Horário *"
              value={formData.time}
              onChangeText={(text) => updateField('time', text)}
              style={[styles.input, styles.timeInput]}
              mode="outlined"
              placeholder="HH:MM"
              error={!!errors.time}
            />
          </View>
          
          {errors.date && <Text style={styles.errorText}>{errors.date}</Text>}
          {errors.time && <Text style={styles.errorText}>{errors.time}</Text>}
        </Card.Content>
      </Card>

      {/* Local */}
      <Card style={styles.card}>
        <Card.Content>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Local da Partida
          </Text>
          
          <TextInput
            label="Nome do Local *"
            value={formData.venue}
            onChangeText={(text) => updateField('venue', text)}
            style={styles.input}
            mode="outlined"
            placeholder="Ex: Estádio Municipal, Campo do Clube"
            error={!!errors.venue}
          />
          {errors.venue && <Text style={styles.errorText}>{errors.venue}</Text>}
          
          <TextInput
            label="Endereço do Local"
            value={formData.venue_address || ''}
            onChangeText={(text) => updateField('venue_address', text)}
            style={styles.input}
            mode="outlined"
            placeholder="Endereço completo do local"
          />
        </Card.Content>
      </Card>

      {/* Status e Resultado */}
      <Card style={styles.card}>
        <Card.Content>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Status da Partida
          </Text>
          
          <Text variant="bodyMedium" style={styles.fieldLabel}>Status</Text>
          <View style={styles.statusContainer}>
            {[
              { value: 'scheduled', label: 'Agendada', color: '#2196f3' },
              { value: 'live', label: 'Ao Vivo', color: '#4caf50' },
              { value: 'finished', label: 'Finalizada', color: '#9e9e9e' },
              { value: 'cancelled', label: 'Cancelada', color: '#f44336' },
              { value: 'postponed', label: 'Adiada', color: '#ff9800' },
            ].map((status) => (
              <Chip
                key={status.value}
                selected={formData.status === status.value}
                onPress={() => updateField('status', status.value)}
                style={[
                  styles.statusChip,
                  formData.status === status.value && { backgroundColor: `${status.color}20` }
                ]}
                textStyle={[
                  styles.statusText,
                  formData.status === status.value && { color: status.color }
                ]}
              >
                {status.label}
              </Chip>
            ))}
          </View>
          
          {/* Resultado (se finalizada) */}
          {formData.status === 'finished' && (
            <>
              <Text variant="bodyMedium" style={styles.fieldLabel}>Resultado</Text>
              <View style={styles.scoreRow}>
                <View style={styles.scoreTeam}>
                  <Text variant="bodySmall" style={styles.teamLabel}>
                    {formData.home_away === 'home' ? 'Nós' : formData.opponent}
                  </Text>
                  <TextInput
                    value={formData.home_score?.toString() || ''}
                    onChangeText={(text) => updateField('home_score', parseInt(text) || 0)}
                    style={styles.scoreInput}
                    mode="outlined"
                    keyboardType="numeric"
                  />
                </View>
                
                <Text variant="headlineMedium" style={styles.scoreSeparator}>×</Text>
                
                <View style={styles.scoreTeam}>
                  <Text variant="bodySmall" style={styles.teamLabel}>
                    {formData.home_away === 'away' ? 'Nós' : formData.opponent}
                  </Text>
                  <TextInput
                    value={formData.away_score?.toString() || ''}
                    onChangeText={(text) => updateField('away_score', parseInt(text) || 0)}
                    style={styles.scoreInput}
                    mode="outlined"
                    keyboardType="numeric"
                  />
                </View>
              </View>
            </>
          )}
        </Card.Content>
      </Card>

      {/* Informações Adicionais */}
      <Card style={styles.card}>
        <Card.Content>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Informações Adicionais
          </Text>
          
          <TextInput
            label="Árbitro"
            value={formData.referee || ''}
            onChangeText={(text) => updateField('referee', text)}
            style={styles.input}
            mode="outlined"
            placeholder="Nome do árbitro principal"
          />
          
          <TextInput
            label="Condições Climáticas"
            value={formData.weather_conditions || ''}
            onChangeText={(text) => updateField('weather_conditions', text)}
            style={styles.input}
            mode="outlined"
            placeholder="Ex: Ensolarado, Chuva, Nublado"
          />
          
          <TextInput
            label="Observações"
            value={formData.notes || ''}
            onChangeText={(text) => updateField('notes', text)}
            style={styles.input}
            mode="outlined"
            multiline
            numberOfLines={3}
            placeholder="Informações adicionais sobre a partida..."
          />
        </Card.Content>
      </Card>

      {/* Botões de Ação */}
      <View style={styles.actions}>
        <Button
          mode="outlined"
          onPress={() => navigation.goBack()}
          style={styles.actionButton}
          disabled={saving}
        >
          Cancelar
        </Button>
        
        <Button
          mode="contained"
          onPress={handleSave}
          style={styles.actionButton}
          loading={saving}
          disabled={saving}
        >
          {mode === 'create' ? 'Criar Partida' : 'Salvar Alterações'}
        </Button>
      </View>

      <View style={styles.bottomSpacing} />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: spacing.md,
    opacity: 0.7,
  },
  card: {
    margin: spacing.md,
    marginBottom: spacing.sm,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: spacing.lg,
  },
  fieldLabel: {
    fontWeight: '500',
    marginBottom: spacing.sm,
    opacity: 0.8,
  },
  input: {
    marginBottom: spacing.md,
  },
  segmentedButtons: {
    marginBottom: spacing.md,
  },
  dateTimeRow: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  dateInput: {
    flex: 2,
  },
  timeInput: {
    flex: 1,
  },
  statusContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
    marginBottom: spacing.md,
  },
  statusChip: {
    marginBottom: spacing.sm,
  },
  statusText: {
    fontSize: 12,
  },
  scoreRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.md,
  },
  scoreTeam: {
    alignItems: 'center',
    flex: 1,
  },
  teamLabel: {
    marginBottom: spacing.sm,
    opacity: 0.7,
    textAlign: 'center',
  },
  scoreInput: {
    width: 80,
    textAlign: 'center',
  },
  scoreSeparator: {
    marginHorizontal: spacing.lg,
    opacity: 0.5,
  },
  errorText: {
    color: '#f44336',
    fontSize: 12,
    marginTop: -spacing.md,
    marginBottom: spacing.md,
    marginLeft: spacing.md,
  },
  actions: {
    flexDirection: 'row',
    padding: spacing.md,
    gap: spacing.md,
  },
  actionButton: {
    flex: 1,
  },
  bottomSpacing: {
    height: spacing.xl,
  },
});
