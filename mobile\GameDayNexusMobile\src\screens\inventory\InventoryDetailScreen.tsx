import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
} from 'react-native';
import {
  Text,
  Card,
  Chip,
  Button,
  useTheme,
  ActivityIndicator,
  Divider,
  SegmentedButtons,
  ProgressBar,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { spacing } from '@/theme';
import { 
  InventoryItem, 
  getItemConditionLabel, 
  getItemConditionColor,
  getItemStatusLabel,
  getItemStatusColor,
  getStockUnitLabel,
  formatCurrency,
  isLowStock,
  isOutOfStock,
  calculateStockPercentage
} from '@/types/inventory';

interface InventoryDetailScreenProps {
  route: {
    params: {
      itemId: string;
    };
  };
  navigation: any;
}

export default function InventoryDetailScreen({ route, navigation }: InventoryDetailScreenProps) {
  const theme = useTheme();
  const { itemId } = route.params;
  const [item, setItem] = useState<InventoryItem | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedTab, setSelectedTab] = useState('info');

  // Mock data - em produção viria da API
  const mockItem: InventoryItem = {
    id: itemId,
    club_id: 'club1',
    name: 'Bolas de Futebol Nike Premier League',
    description: 'Bolas oficiais FIFA para treinos e jogos da categoria profissional. Material de alta qualidade com tecnologia Nike Aerowsculpt para melhor aerodinâmica.',
    category_id: 'cat1',
    category: {
      id: 'cat1',
      name: 'Material Esportivo',
      description: 'Equipamentos e materiais para treino e jogos',
      color: '#2196f3',
      icon: 'sports-soccer',
      active: true,
      created_at: '2025-01-01',
      updated_at: '2025-01-01',
    },
    brand: 'Nike',
    model: 'Premier League Official',
    serial_number: 'NK-PL-2025-001',
    barcode: '1234567890123',
    current_stock: 15,
    min_stock: 10,
    max_stock: 30,
    unit: 'unit',
    unit_cost: 50.00,
    total_value: 750.00,
    location: 'Almoxarifado Principal - Prateleira A3',
    supplier: 'Nike Brasil Ltda',
    purchase_date: '2025-01-15',
    warranty_expiry: '2026-01-15',
    condition: 'good',
    status: 'active',
    tags: ['FIFA', 'Oficial', 'Treino', 'Jogo'],
    notes: 'Bolas em excelente estado. Algumas com pequenos desgastes naturais do uso em treinos.',
    created_at: '2025-01-01',
    updated_at: '2025-01-01',
  };

  useEffect(() => {
    loadItem();
  }, [itemId]);

  const loadItem = async () => {
    setLoading(true);
    try {
      // Simular carregamento da API
      await new Promise(resolve => setTimeout(resolve, 1000));
      setItem(mockItem);
    } catch (error) {
      console.error('Erro ao carregar item:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStockStatusColor = () => {
    if (!item) return theme.colors.outline;
    if (isOutOfStock(item)) return '#f44336';
    if (isLowStock(item)) return '#ff9800';
    return '#4caf50';
  };

  const getStockStatusLabel = () => {
    if (!item) return '';
    if (isOutOfStock(item)) return 'Sem Estoque';
    if (isLowStock(item)) return 'Estoque Baixo';
    return 'Estoque OK';
  };

  const getStockPercentage = () => {
    if (!item || !item.max_stock) return 0;
    return calculateStockPercentage(item.current_stock, item.max_stock);
  };

  const handleEdit = () => {
    navigation.navigate('InventoryForm', { itemId: item?.id });
  };

  const handleAddMovement = () => {
    navigation.navigate('StockMovementForm', { itemId: item?.id });
  };

  const renderItemHeader = () => {
    if (!item) return null;

    const stockPercentage = getStockPercentage();

    return (
      <Card style={[
        styles.headerCard,
        (isOutOfStock(item) || isLowStock(item)) && styles.alertCard
      ]}>
        <Card.Content style={styles.headerContent}>
          <View style={styles.statusContainer}>
            <View style={styles.statusChips}>
              <Chip
                style={[styles.statusChip, { backgroundColor: `${getItemStatusColor(item.status)}20` }]}
                textStyle={[styles.statusText, { color: getItemStatusColor(item.status) }]}
              >
                {getItemStatusLabel(item.status)}
              </Chip>
              
              <Chip
                style={[styles.stockChip, { backgroundColor: `${getStockStatusColor()}20` }]}
                textStyle={[styles.stockText, { color: getStockStatusColor() }]}
              >
                {getStockStatusLabel()}
              </Chip>
            </View>
            
            <Text variant="headlineMedium" style={styles.value}>
              {formatCurrency(item.total_value)}
            </Text>
          </View>

          <View style={styles.titleContainer}>
            <View style={[styles.categoryIcon, { backgroundColor: `${item.category.color}20` }]}>
              <Icon 
                name={item.category.icon} 
                size={32} 
                color={item.category.color} 
              />
            </View>
            
            <View style={styles.titleText}>
              <Text variant="headlineSmall" style={styles.title}>
                {item.name}
              </Text>
              
              <View style={styles.categoryContainer}>
                <View style={[styles.categoryDot, { backgroundColor: item.category.color }]} />
                <Text variant="titleSmall" style={styles.categoryName}>
                  {item.category.name}
                </Text>
              </View>
            </View>
          </View>

          {/* Informações de Estoque */}
          <View style={styles.stockContainer}>
            <View style={styles.stockHeader}>
              <Text variant="titleMedium" style={styles.stockTitle}>
                Estoque Atual: {item.current_stock} {getStockUnitLabel(item.unit)}
              </Text>
              {item.unit_cost && (
                <Text variant="bodyMedium" style={styles.unitCost}>
                  {formatCurrency(item.unit_cost)}/{getStockUnitLabel(item.unit)}
                </Text>
              )}
            </View>
            
            {item.max_stock && (
              <View style={styles.progressContainer}>
                <ProgressBar
                  progress={stockPercentage / 100}
                  color={getStockStatusColor()}
                  style={styles.progressBar}
                />
                <Text variant="bodySmall" style={styles.progressText}>
                  {stockPercentage.toFixed(0)}% da capacidade máxima
                </Text>
              </View>
            )}
            
            <View style={styles.stockLimits}>
              <Text variant="bodySmall" style={styles.stockLimit}>
                Mínimo: {item.min_stock}
              </Text>
              {item.max_stock && (
                <Text variant="bodySmall" style={styles.stockLimit}>
                  Máximo: {item.max_stock}
                </Text>
              )}
            </View>
          </View>

          {item.description && (
            <Text variant="bodyMedium" style={styles.description}>
              {item.description}
            </Text>
          )}
        </Card.Content>
      </Card>
    );
  };

  const renderTabContent = () => {
    switch (selectedTab) {
      case 'info':
        return (
          <Card style={styles.card}>
            <Card.Content>
              <Text variant="titleMedium" style={styles.sectionTitle}>
                Informações do Item
              </Text>
              <Divider style={styles.divider} />
              
              <View style={styles.infoGrid}>
                {item?.brand && (
                  <View style={styles.infoItem}>
                    <Text variant="bodySmall" style={styles.infoLabel}>Marca</Text>
                    <Text variant="bodyMedium">{item.brand}</Text>
                  </View>
                )}
                
                {item?.model && (
                  <View style={styles.infoItem}>
                    <Text variant="bodySmall" style={styles.infoLabel}>Modelo</Text>
                    <Text variant="bodyMedium">{item.model}</Text>
                  </View>
                )}
                
                {item?.serial_number && (
                  <View style={styles.infoItem}>
                    <Text variant="bodySmall" style={styles.infoLabel}>Número de Série</Text>
                    <Text variant="bodyMedium">{item.serial_number}</Text>
                  </View>
                )}
                
                {item?.location && (
                  <View style={styles.infoItem}>
                    <Text variant="bodySmall" style={styles.infoLabel}>Localização</Text>
                    <Text variant="bodyMedium">{item.location}</Text>
                  </View>
                )}
                
                {item?.supplier && (
                  <View style={styles.infoItem}>
                    <Text variant="bodySmall" style={styles.infoLabel}>Fornecedor</Text>
                    <Text variant="bodyMedium">{item.supplier}</Text>
                  </View>
                )}
                
                <View style={styles.infoItem}>
                  <Text variant="bodySmall" style={styles.infoLabel}>Condição</Text>
                  <Chip
                    style={[styles.conditionChip, { backgroundColor: `${getItemConditionColor(item?.condition || 'good')}20` }]}
                    textStyle={[styles.conditionText, { color: getItemConditionColor(item?.condition || 'good') }]}
                    compact
                  >
                    {getItemConditionLabel(item?.condition || 'good')}
                  </Chip>
                </View>
              </View>
              
              {item?.tags && item.tags.length > 0 && (
                <View style={styles.tagsSection}>
                  <Text variant="bodySmall" style={styles.infoLabel}>Tags</Text>
                  <View style={styles.tagsContainer}>
                    {item.tags.map((tag, index) => (
                      <Chip
                        key={index}
                        style={styles.tagChip}
                        textStyle={styles.tagText}
                        compact
                      >
                        {tag}
                      </Chip>
                    ))}
                  </View>
                </View>
              )}
              
              {item?.notes && (
                <View style={styles.notesSection}>
                  <Text variant="bodySmall" style={styles.infoLabel}>Observações</Text>
                  <Text variant="bodyMedium">{item.notes}</Text>
                </View>
              )}
            </Card.Content>
          </Card>
        );
      
      case 'movements':
        return (
          <Card style={styles.card}>
            <Card.Content>
              <Text variant="titleMedium" style={styles.sectionTitle}>
                Movimentações
              </Text>
              <Divider style={styles.divider} />
              
              <Text variant="bodyMedium" style={styles.emptyMessage}>
                Nenhuma movimentação registrada
              </Text>
              
              <Button
                mode="contained"
                onPress={handleAddMovement}
                style={styles.actionButton}
                icon="add-circle"
              >
                Registrar Movimentação
              </Button>
            </Card.Content>
          </Card>
        );
      
      case 'history':
        return (
          <Card style={styles.card}>
            <Card.Content>
              <Text variant="titleMedium" style={styles.sectionTitle}>
                Histórico
              </Text>
              <Divider style={styles.divider} />
              
              <View style={styles.historyItem}>
                <Text variant="bodySmall" style={styles.historyDate}>
                  {format(new Date(), "dd/MM/yyyy 'às' HH:mm")}
                </Text>
                <Text variant="bodyMedium">Item criado no sistema</Text>
              </View>
            </Card.Content>
          </Card>
        );
      
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text variant="bodyMedium" style={styles.loadingText}>
          Carregando item...
        </Text>
      </View>
    );
  }

  if (!item) {
    return (
      <View style={styles.errorContainer}>
        <Text variant="titleMedium">Item não encontrado</Text>
        <Button mode="outlined" onPress={() => navigation.goBack()}>
          Voltar
        </Button>
      </View>
    );
  }

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {renderItemHeader()}

      <View style={styles.tabsContainer}>
        <SegmentedButtons
          value={selectedTab}
          onValueChange={setSelectedTab}
          buttons={[
            {
              value: 'info',
              label: 'Info',
              icon: 'info',
            },
            {
              value: 'movements',
              label: 'Movimentações',
              icon: 'swap-horiz',
            },
            {
              value: 'history',
              label: 'Histórico',
              icon: 'history',
            },
          ]}
          style={styles.segmentedButtons}
        />
      </View>

      {renderTabContent()}

      {/* Ações */}
      <View style={styles.actions}>
        <Button
          mode="outlined"
          onPress={handleAddMovement}
          style={styles.actionButton}
          icon="swap-horiz"
        >
          Nova Movimentação
        </Button>
        
        <Button
          mode="contained"
          onPress={handleEdit}
          style={styles.actionButton}
          icon="edit"
        >
          Editar Item
        </Button>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: spacing.md,
    opacity: 0.7,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.lg,
  },
  headerCard: {
    margin: spacing.md,
    marginBottom: spacing.sm,
  },
  alertCard: {
    borderLeftWidth: 4,
    borderLeftColor: '#ff9800',
  },
  headerContent: {
    padding: spacing.lg,
  },
  statusContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  statusChips: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  statusChip: {
    height: 28,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  stockChip: {
    height: 28,
  },
  stockText: {
    fontSize: 12,
    fontWeight: '600',
  },
  value: {
    fontWeight: 'bold',
    color: '#4caf50',
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  categoryIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  titleText: {
    flex: 1,
  },
  title: {
    fontWeight: 'bold',
    marginBottom: spacing.sm,
  },
  categoryContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: spacing.sm,
  },
  categoryName: {
    fontWeight: '500',
  },
  stockContainer: {
    marginBottom: spacing.lg,
    padding: spacing.md,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
  },
  stockHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  stockTitle: {
    fontWeight: '600',
  },
  unitCost: {
    opacity: 0.7,
  },
  progressContainer: {
    marginBottom: spacing.sm,
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
    marginBottom: spacing.xs,
  },
  progressText: {
    opacity: 0.7,
    fontSize: 11,
  },
  stockLimits: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  stockLimit: {
    opacity: 0.7,
    fontSize: 12,
  },
  description: {
    opacity: 0.8,
    lineHeight: 20,
  },
  tabsContainer: {
    padding: spacing.md,
    paddingBottom: spacing.sm,
  },
  segmentedButtons: {
    marginBottom: spacing.sm,
  },
  card: {
    margin: spacing.md,
    marginTop: spacing.sm,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: spacing.sm,
  },
  divider: {
    marginBottom: spacing.md,
  },
  infoGrid: {
    gap: spacing.md,
  },
  infoItem: {
    marginBottom: spacing.md,
  },
  infoLabel: {
    opacity: 0.7,
    marginBottom: spacing.sm,
    fontWeight: '500',
  },
  conditionChip: {
    height: 24,
    alignSelf: 'flex-start',
  },
  conditionText: {
    fontSize: 11,
    fontWeight: '600',
  },
  tagsSection: {
    marginTop: spacing.lg,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  tagChip: {
    height: 24,
    backgroundColor: '#e3f2fd',
  },
  tagText: {
    fontSize: 11,
    opacity: 0.8,
  },
  notesSection: {
    marginTop: spacing.lg,
  },
  emptyMessage: {
    textAlign: 'center',
    opacity: 0.6,
    fontStyle: 'italic',
    paddingVertical: spacing.lg,
  },
  historyItem: {
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  historyDate: {
    opacity: 0.6,
    marginBottom: spacing.xs,
  },
  actions: {
    padding: spacing.md,
    paddingBottom: spacing.xl,
    gap: spacing.md,
  },
  actionButton: {
    marginTop: spacing.sm,
  },
});
