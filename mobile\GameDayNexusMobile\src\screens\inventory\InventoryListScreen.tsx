import React, { useState, useCallback } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  RefreshControl,
  TouchableOpacity,
} from 'react-native';
import {
  Text,
  Searchbar,
  FAB,
  Chip,
  useTheme,
  ActivityIndicator,
  SegmentedButtons,
  Card,
} from 'react-native-paper';
import { useFocusEffect } from '@react-navigation/native';
import { spacing } from '@/theme';
import InventoryCard from '@/components/inventory/InventoryCard';
import InventoryFilterBottomSheet from '@/components/inventory/InventoryFilterBottomSheet';
import { InventoryItem, InventoryFilters, formatCurrency, isLowStock, isOutOfStock } from '@/types/inventory';

interface InventoryListScreenProps {
  navigation: any;
}

export default function InventoryListScreen({ navigation }: InventoryListScreenProps) {
  const theme = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [items, setItems] = useState<InventoryItem[]>([]);
  const [filteredItems, setFilteredItems] = useState<InventoryItem[]>([]);
  const [filters, setFilters] = useState<InventoryFilters>({});
  const [showFilters, setShowFilters] = useState(false);
  const [selectedTab, setSelectedTab] = useState('all');

  // Dados mockados para demonstração
  const mockItems: InventoryItem[] = [
    {
      id: '1',
      club_id: 'club1',
      name: 'Bolas de Futebol Nike',
      description: 'Bolas oficiais FIFA para treinos e jogos',
      category_id: 'cat1',
      category: {
        id: 'cat1',
        name: 'Material Esportivo',
        color: '#2196f3',
        icon: 'sports-soccer',
        active: true,
        created_at: '2025-01-01',
        updated_at: '2025-01-01',
      },
      brand: 'Nike',
      model: 'Premier League',
      current_stock: 15,
      min_stock: 10,
      max_stock: 30,
      unit: 'unit',
      unit_cost: 50.00,
      total_value: 750.00,
      location: 'Almoxarifado Principal',
      condition: 'good',
      status: 'active',
      created_at: '2025-01-01',
      updated_at: '2025-01-01',
    },
    {
      id: '2',
      club_id: 'club1',
      name: 'Cones de Treinamento',
      description: 'Cones para marcação de exercícios',
      category_id: 'cat1',
      category: {
        id: 'cat1',
        name: 'Material Esportivo',
        color: '#2196f3',
        icon: 'sports-soccer',
        active: true,
        created_at: '2025-01-01',
        updated_at: '2025-01-01',
      },
      brand: 'Penalty',
      current_stock: 5,
      min_stock: 20,
      max_stock: 50,
      unit: 'unit',
      unit_cost: 8.00,
      total_value: 40.00,
      location: 'Almoxarifado Principal',
      condition: 'fair',
      status: 'active',
      created_at: '2025-01-01',
      updated_at: '2025-01-01',
    },
    {
      id: '3',
      club_id: 'club1',
      name: 'Uniformes Completos',
      description: 'Uniformes oficiais do clube',
      category_id: 'cat2',
      category: {
        id: 'cat2',
        name: 'Uniformes',
        color: '#4caf50',
        icon: 'checkroom',
        active: true,
        created_at: '2025-01-01',
        updated_at: '2025-01-01',
      },
      brand: 'Umbro',
      current_stock: 25,
      min_stock: 15,
      max_stock: 40,
      unit: 'set',
      unit_cost: 120.00,
      total_value: 3000.00,
      location: 'Vestiário',
      condition: 'new',
      status: 'active',
      created_at: '2025-01-01',
      updated_at: '2025-01-01',
    },
    {
      id: '4',
      club_id: 'club1',
      name: 'Kit Médico',
      description: 'Kit completo de primeiros socorros',
      category_id: 'cat3',
      category: {
        id: 'cat3',
        name: 'Médico',
        color: '#f44336',
        icon: 'medical-services',
        active: true,
        created_at: '2025-01-01',
        updated_at: '2025-01-01',
      },
      current_stock: 0,
      min_stock: 2,
      max_stock: 5,
      unit: 'unit',
      unit_cost: 200.00,
      total_value: 0.00,
      location: 'Departamento Médico',
      condition: 'good',
      status: 'active',
      created_at: '2025-01-01',
      updated_at: '2025-01-01',
    },
  ];

  useFocusEffect(
    useCallback(() => {
      loadItems();
    }, [])
  );

  const loadItems = async () => {
    setLoading(true);
    try {
      // Simular carregamento da API
      await new Promise(resolve => setTimeout(resolve, 1000));
      setItems(mockItems);
      filterItems(searchQuery, filters, selectedTab, mockItems);
    } catch (error) {
      console.error('Erro ao carregar itens:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadItems();
    setRefreshing(false);
  }, []);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    filterItems(query, filters, selectedTab, items);
  };

  const handleFilter = (newFilters: InventoryFilters) => {
    setFilters(newFilters);
    filterItems(searchQuery, newFilters, selectedTab, items);
    setShowFilters(false);
  };

  const handleTabChange = (tab: string) => {
    setSelectedTab(tab);
    filterItems(searchQuery, filters, tab, items);
  };

  const filterItems = (
    query: string, 
    currentFilters: InventoryFilters, 
    tab: string,
    allItems: InventoryItem[]
  ) => {
    let filtered = [...allItems];

    // Filtro por aba
    if (tab === 'low_stock') {
      filtered = filtered.filter(item => isLowStock(item));
    } else if (tab === 'out_of_stock') {
      filtered = filtered.filter(item => isOutOfStock(item));
    }

    // Filtro por busca
    if (query) {
      filtered = filtered.filter(item =>
        item.name.toLowerCase().includes(query.toLowerCase()) ||
        item.description?.toLowerCase().includes(query.toLowerCase()) ||
        item.brand?.toLowerCase().includes(query.toLowerCase()) ||
        item.category.name.toLowerCase().includes(query.toLowerCase())
      );
    }

    // Filtros específicos
    if (currentFilters.category_id) {
      filtered = filtered.filter(item => 
        item.category_id === currentFilters.category_id
      );
    }

    if (currentFilters.condition) {
      filtered = filtered.filter(item => 
        item.condition === currentFilters.condition
      );
    }

    if (currentFilters.status) {
      filtered = filtered.filter(item => 
        item.status === currentFilters.status
      );
    }

    if (currentFilters.location) {
      filtered = filtered.filter(item => 
        item.location?.toLowerCase().includes(currentFilters.location!.toLowerCase())
      );
    }

    if (currentFilters.low_stock_only) {
      filtered = filtered.filter(item => isLowStock(item));
    }

    if (currentFilters.out_of_stock_only) {
      filtered = filtered.filter(item => isOutOfStock(item));
    }

    // Ordenar por nome
    filtered.sort((a, b) => a.name.localeCompare(b.name));

    setFilteredItems(filtered);
  };

  const handleItemPress = (item: InventoryItem) => {
    navigation.navigate('InventoryDetail', { itemId: item.id });
  };

  const handleAddItem = () => {
    navigation.navigate('InventoryForm');
  };

  const getActiveFiltersCount = () => {
    return Object.values(filters).filter(value => value !== undefined && value !== '').length;
  };

  const calculateSummary = () => {
    const totalValue = filteredItems.reduce((sum, item) => sum + item.total_value, 0);
    const lowStockCount = filteredItems.filter(item => isLowStock(item)).length;
    const outOfStockCount = filteredItems.filter(item => isOutOfStock(item)).length;
    
    return {
      totalItems: filteredItems.length,
      totalValue,
      lowStockCount,
      outOfStockCount,
    };
  };

  const renderItem = ({ item }: { item: InventoryItem }) => (
    <InventoryCard
      item={item}
      onPress={() => handleItemPress(item)}
    />
  );

  const renderSummary = () => {
    const summary = calculateSummary();
    
    return (
      <Card style={styles.summaryCard}>
        <Card.Content style={styles.summaryContent}>
          <View style={styles.summaryItem}>
            <Text variant="bodySmall" style={styles.summaryLabel}>Total de Itens</Text>
            <Text variant="titleMedium" style={styles.summaryValue}>
              {summary.totalItems}
            </Text>
          </View>
          
          <View style={styles.summaryItem}>
            <Text variant="bodySmall" style={styles.summaryLabel}>Valor Total</Text>
            <Text variant="titleMedium" style={styles.summaryValue}>
              {formatCurrency(summary.totalValue)}
            </Text>
          </View>
          
          <View style={styles.summaryItem}>
            <Text variant="bodySmall" style={styles.summaryLabel}>Estoque Baixo</Text>
            <Text variant="titleMedium" style={[
              styles.summaryValue, 
              { color: summary.lowStockCount > 0 ? '#ff9800' : '#4caf50' }
            ]}>
              {summary.lowStockCount}
            </Text>
          </View>
          
          <View style={styles.summaryItem}>
            <Text variant="bodySmall" style={styles.summaryLabel}>Sem Estoque</Text>
            <Text variant="titleMedium" style={[
              styles.summaryValue, 
              { color: summary.outOfStockCount > 0 ? '#f44336' : '#4caf50' }
            ]}>
              {summary.outOfStockCount}
            </Text>
          </View>
        </Card.Content>
      </Card>
    );
  };

  const renderHeader = () => (
    <View style={styles.header}>
      {renderSummary()}
      
      <SegmentedButtons
        value={selectedTab}
        onValueChange={handleTabChange}
        buttons={[
          {
            value: 'all',
            label: 'Todos',
            icon: 'inventory',
          },
          {
            value: 'low_stock',
            label: 'Baixo',
            icon: 'warning',
          },
          {
            value: 'out_of_stock',
            label: 'Zerado',
            icon: 'error',
          },
        ]}
        style={styles.segmentedButtons}
      />

      <Searchbar
        placeholder="Buscar itens..."
        onChangeText={handleSearch}
        value={searchQuery}
        style={styles.searchbar}
      />
      
      <View style={styles.filtersContainer}>
        <TouchableOpacity onPress={() => setShowFilters(true)}>
          <Chip
            icon="filter-variant"
            style={styles.filterChip}
            textStyle={styles.filterChipText}
          >
            Filtros {getActiveFiltersCount() > 0 && `(${getActiveFiltersCount()})`}
          </Chip>
        </TouchableOpacity>
      </View>

      <View style={styles.resultsContainer}>
        <Text variant="bodyMedium" style={styles.resultsText}>
          {filteredItems.length} ite{filteredItems.length !== 1 ? 'ns' : 'm'} encontrado{filteredItems.length !== 1 ? 's' : ''}
        </Text>
      </View>
    </View>
  );

  const renderEmpty = () => (
    <View style={styles.emptyContainer}>
      <Text variant="titleMedium" style={styles.emptyTitle}>
        Nenhum item encontrado
      </Text>
      <Text variant="bodyMedium" style={styles.emptyMessage}>
        {searchQuery || Object.keys(filters).length > 0
          ? 'Tente ajustar os filtros de busca'
          : 'Adicione o primeiro item ao inventário'
        }
      </Text>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text variant="bodyMedium" style={styles.loadingText}>
          Carregando inventário...
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <FlatList
        data={filteredItems}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={renderEmpty}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
      />

      <FAB
        icon="plus"
        style={[styles.fab, { backgroundColor: theme.colors.primary }]}
        onPress={handleAddItem}
        label="Novo Item"
      />

      <InventoryFilterBottomSheet
        visible={showFilters}
        onDismiss={() => setShowFilters(false)}
        onApply={handleFilter}
        currentFilters={filters}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: spacing.md,
    opacity: 0.7,
  },
  listContent: {
    flexGrow: 1,
  },
  header: {
    padding: spacing.md,
    paddingBottom: spacing.sm,
  },
  summaryCard: {
    marginBottom: spacing.md,
  },
  summaryContent: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: spacing.sm,
  },
  summaryItem: {
    alignItems: 'center',
  },
  summaryLabel: {
    opacity: 0.7,
    marginBottom: spacing.xs,
    textAlign: 'center',
  },
  summaryValue: {
    fontWeight: 'bold',
  },
  segmentedButtons: {
    marginBottom: spacing.md,
  },
  searchbar: {
    marginBottom: spacing.md,
  },
  filtersContainer: {
    flexDirection: 'row',
    marginBottom: spacing.md,
  },
  filterChip: {
    marginRight: spacing.sm,
  },
  filterChipText: {
    fontSize: 12,
  },
  resultsContainer: {
    marginBottom: spacing.sm,
  },
  resultsText: {
    opacity: 0.7,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.xxxl,
  },
  emptyTitle: {
    fontWeight: '600',
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  emptyMessage: {
    textAlign: 'center',
    opacity: 0.7,
    lineHeight: 20,
  },
  fab: {
    position: 'absolute',
    margin: spacing.md,
    right: 0,
    bottom: 0,
  },
});
