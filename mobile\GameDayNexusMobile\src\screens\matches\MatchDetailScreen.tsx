import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Image,
} from 'react-native';
import {
  Text,
  Card,
  Chip,
  Button,
  useTheme,
  ActivityIndicator,
  Avatar,
  Divider,
  SegmentedButtons,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { spacing } from '@/theme';
import { Match, getMatchStatusLabel, getMatchStatusColor } from '@/types/matches';

interface MatchDetailScreenProps {
  route: {
    params: {
      matchId: string;
    };
  };
  navigation: any;
}

export default function MatchDetailScreen({ route, navigation }: MatchDetailScreenProps) {
  const theme = useTheme();
  const { matchId } = route.params;
  const [match, setMatch] = useState<Match | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedTab, setSelectedTab] = useState('info');

  // Mock data - em produção viria da API
  const mockMatch: Match = {
    id: matchId,
    club_id: 'club1',
    opponent_name: 'Rival FC',
    opponent_logo: 'https://via.placeholder.com/100',
    competition_name: 'Campeonato Estadual',
    date: '2025-08-27',
    time: '16:00',
    location: 'Estádio Municipal',
    is_home: true,
    status: 'scheduled',
    created_at: '2025-01-01',
    updated_at: '2025-01-01',
  };

  useEffect(() => {
    loadMatch();
  }, [matchId]);

  const loadMatch = async () => {
    setLoading(true);
    try {
      // Simular carregamento da API
      await new Promise(resolve => setTimeout(resolve, 1000));
      setMatch(mockMatch);
    } catch (error) {
      console.error('Erro ao carregar partida:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatMatchDate = () => {
    if (!match) return '';
    try {
      const matchDate = new Date(match.date);
      return format(matchDate, "EEEE, dd 'de' MMMM 'de' yyyy", { locale: ptBR });
    } catch {
      return match.date;
    }
  };

  const getStatusColor = () => {
    if (!match) return theme.colors.outline;
    return getMatchStatusColor(match.status);
  };

  const isLiveMatch = () => {
    return match?.status === 'live' || match?.status === 'halftime';
  };

  const isFinishedMatch = () => {
    return match?.status === 'finished';
  };

  const handleEdit = () => {
    navigation.navigate('MatchForm', { matchId: match?.id });
  };

  const handleLineup = () => {
    navigation.navigate('TacticalFormation', { matchId: match?.id });
  };

  const handleCallup = () => {
    navigation.navigate('Callup', { matchId: match?.id });
  };

  const renderOpponentLogo = () => {
    if (!match) return null;

    if (match.opponent_logo) {
      return (
        <Image
          source={{ uri: match.opponent_logo }}
          style={styles.opponentLogo}
        />
      );
    }

    return (
      <Avatar.Text
        size={80}
        label={match.opponent_name.substring(0, 2)}
        style={[styles.opponentAvatar, { backgroundColor: theme.colors.primary }]}
      />
    );
  };

  const renderMatchHeader = () => {
    if (!match) return null;

    return (
      <Card style={styles.headerCard}>
        <Card.Content style={styles.headerContent}>
          <View style={styles.statusContainer}>
            <Chip
              style={[styles.statusChip, { backgroundColor: `${getStatusColor()}20` }]}
              textStyle={[styles.statusText, { color: getStatusColor() }]}
              icon={isLiveMatch() ? 'circle' : undefined}
            >
              {getMatchStatusLabel(match.status)}
            </Chip>
            <Text variant="bodyMedium" style={styles.competition}>
              {match.competition_name}
            </Text>
          </View>

          <View style={styles.teamsContainer}>
            <View style={styles.homeTeam}>
              <Avatar.Text
                size={80}
                label="FC"
                style={[styles.clubAvatar, { backgroundColor: theme.colors.primary }]}
              />
              <Text variant="titleMedium" style={styles.teamName}>
                Nosso Clube
              </Text>
              <Text variant="bodySmall" style={styles.teamLabel}>
                {match.is_home ? 'Casa' : 'Visitante'}
              </Text>
            </View>

            <View style={styles.vsContainer}>
              {isFinishedMatch() && match.result ? (
                <View style={styles.resultContainer}>
                  <Text variant="headlineLarge" style={styles.result}>
                    {match.is_home 
                      ? `${match.result.home_score} x ${match.result.away_score}`
                      : `${match.result.away_score} x ${match.result.home_score}`
                    }
                  </Text>
                </View>
              ) : (
                <Text variant="headlineMedium" style={styles.vs}>
                  VS
                </Text>
              )}
            </View>

            <View style={styles.awayTeam}>
              {renderOpponentLogo()}
              <Text variant="titleMedium" style={styles.teamName}>
                {match.opponent_name}
              </Text>
              <Text variant="bodySmall" style={styles.teamLabel}>
                {match.is_home ? 'Visitante' : 'Casa'}
              </Text>
            </View>
          </View>

          <View style={styles.matchDetails}>
            <View style={styles.detailItem}>
              <Icon name="event" size={20} color={theme.colors.outline} />
              <Text variant="bodyMedium" style={styles.detailText}>
                {formatMatchDate()}
              </Text>
            </View>
            
            <View style={styles.detailItem}>
              <Icon name="access-time" size={20} color={theme.colors.outline} />
              <Text variant="bodyMedium" style={styles.detailText}>
                {match.time}
              </Text>
            </View>
            
            <View style={styles.detailItem}>
              <Icon name="location-on" size={20} color={theme.colors.outline} />
              <Text variant="bodyMedium" style={styles.detailText}>
                {match.location}
              </Text>
            </View>
          </View>
        </Card.Content>
      </Card>
    );
  };

  const renderTabContent = () => {
    switch (selectedTab) {
      case 'info':
        return (
          <Card style={styles.card}>
            <Card.Content>
              <Text variant="titleMedium" style={styles.sectionTitle}>
                Informações da Partida
              </Text>
              <Divider style={styles.divider} />
              
              <View style={styles.infoGrid}>
                <View style={styles.infoItem}>
                  <Text variant="bodySmall" style={styles.infoLabel}>Competição</Text>
                  <Text variant="bodyMedium">{match?.competition_name}</Text>
                </View>
                
                <View style={styles.infoItem}>
                  <Text variant="bodySmall" style={styles.infoLabel}>Local</Text>
                  <Text variant="bodyMedium">{match?.location}</Text>
                </View>
                
                <View style={styles.infoItem}>
                  <Text variant="bodySmall" style={styles.infoLabel}>Mando de Campo</Text>
                  <Text variant="bodyMedium">{match?.is_home ? 'Casa' : 'Fora'}</Text>
                </View>
                
                <View style={styles.infoItem}>
                  <Text variant="bodySmall" style={styles.infoLabel}>Status</Text>
                  <Text variant="bodyMedium">{getMatchStatusLabel(match?.status || 'scheduled')}</Text>
                </View>
              </View>
            </Card.Content>
          </Card>
        );
      
      case 'lineup':
        return (
          <Card style={styles.card}>
            <Card.Content>
              <Text variant="titleMedium" style={styles.sectionTitle}>
                Escalação
              </Text>
              <Divider style={styles.divider} />
              
              <Text variant="bodyMedium" style={styles.emptyMessage}>
                Escalação ainda não definida
              </Text>
              
              <Button
                mode="contained"
                onPress={handleLineup}
                style={styles.actionButton}
                icon="sports-soccer"
              >
                Definir Escalação
              </Button>
            </Card.Content>
          </Card>
        );
      
      case 'events':
        return (
          <Card style={styles.card}>
            <Card.Content>
              <Text variant="titleMedium" style={styles.sectionTitle}>
                Eventos da Partida
              </Text>
              <Divider style={styles.divider} />
              
              <Text variant="bodyMedium" style={styles.emptyMessage}>
                Nenhum evento registrado
              </Text>
            </Card.Content>
          </Card>
        );
      
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text variant="bodyMedium" style={styles.loadingText}>
          Carregando partida...
        </Text>
      </View>
    );
  }

  if (!match) {
    return (
      <View style={styles.errorContainer}>
        <Text variant="titleMedium">Partida não encontrada</Text>
        <Button mode="outlined" onPress={() => navigation.goBack()}>
          Voltar
        </Button>
      </View>
    );
  }

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {renderMatchHeader()}

      <View style={styles.tabsContainer}>
        <SegmentedButtons
          value={selectedTab}
          onValueChange={setSelectedTab}
          buttons={[
            {
              value: 'info',
              label: 'Info',
              icon: 'info',
            },
            {
              value: 'lineup',
              label: 'Escalação',
              icon: 'people',
            },
            {
              value: 'events',
              label: 'Eventos',
              icon: 'timeline',
            },
          ]}
          style={styles.segmentedButtons}
        />
      </View>

      {renderTabContent()}

      {/* Ações */}
      <View style={styles.actions}>
        <Button
          mode="outlined"
          onPress={handleCallup}
          style={styles.actionButton}
          icon="group-add"
        >
          Convocação
        </Button>
        
        <Button
          mode="contained"
          onPress={handleEdit}
          style={styles.actionButton}
          icon="edit"
        >
          Editar Partida
        </Button>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: spacing.md,
    opacity: 0.7,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.lg,
  },
  headerCard: {
    margin: spacing.md,
    marginBottom: spacing.sm,
  },
  headerContent: {
    padding: spacing.lg,
  },
  statusContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  statusChip: {
    height: 28,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  competition: {
    opacity: 0.7,
    fontStyle: 'italic',
  },
  teamsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: spacing.lg,
  },
  homeTeam: {
    alignItems: 'center',
    flex: 1,
  },
  awayTeam: {
    alignItems: 'center',
    flex: 1,
  },
  clubAvatar: {
    marginBottom: spacing.sm,
  },
  opponentLogo: {
    width: 80,
    height: 80,
    borderRadius: 40,
    marginBottom: spacing.sm,
  },
  opponentAvatar: {
    marginBottom: spacing.sm,
  },
  teamName: {
    textAlign: 'center',
    fontWeight: '600',
    marginBottom: spacing.xs,
  },
  teamLabel: {
    opacity: 0.7,
  },
  vsContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  vs: {
    opacity: 0.5,
    fontWeight: 'bold',
  },
  resultContainer: {
    alignItems: 'center',
  },
  result: {
    fontWeight: 'bold',
    color: '#4caf50',
  },
  matchDetails: {
    gap: spacing.sm,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  detailText: {
    marginLeft: spacing.sm,
  },
  tabsContainer: {
    padding: spacing.md,
    paddingBottom: spacing.sm,
  },
  segmentedButtons: {
    marginBottom: spacing.sm,
  },
  card: {
    margin: spacing.md,
    marginTop: spacing.sm,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: spacing.sm,
  },
  divider: {
    marginBottom: spacing.md,
  },
  infoGrid: {
    gap: spacing.md,
  },
  infoItem: {
    marginBottom: spacing.sm,
  },
  infoLabel: {
    opacity: 0.7,
    marginBottom: spacing.xs,
  },
  emptyMessage: {
    textAlign: 'center',
    opacity: 0.6,
    fontStyle: 'italic',
    paddingVertical: spacing.lg,
  },
  actions: {
    padding: spacing.md,
    paddingBottom: spacing.xl,
    gap: spacing.md,
  },
  actionButton: {
    marginTop: spacing.sm,
  },
});
