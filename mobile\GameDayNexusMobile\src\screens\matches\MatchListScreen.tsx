import React, { useState, useCallback } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  RefreshControl,
  TouchableOpacity,
} from 'react-native';
import {
  Text,
  Searchbar,
  FAB,
  Chip,
  useTheme,
  ActivityIndicator,
  SegmentedButtons,
} from 'react-native-paper';
import { useFocusEffect } from '@react-navigation/native';
import { spacing } from '@/theme';
import MatchCard from '@/components/matches/MatchCard';
import MatchFilterBottomSheet from '@/components/matches/MatchFilterBottomSheet';
import { Match, MatchFilters, MatchStatus } from '@/types/matches';

interface MatchListScreenProps {
  navigation: any;
}

export default function MatchListScreen({ navigation }: MatchListScreenProps) {
  const theme = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [matches, setMatches] = useState<Match[]>([]);
  const [filteredMatches, setFilteredMatches] = useState<Match[]>([]);
  const [filters, setFilters] = useState<MatchFilters>({});
  const [showFilters, setShowFilters] = useState(false);
  const [selectedTab, setSelectedTab] = useState('upcoming');

  // Dados mockados para demonstração
  const mockMatches: Match[] = [
    {
      id: '1',
      club_id: 'club1',
      opponent_name: 'Rival FC',
      opponent_logo: 'https://via.placeholder.com/50',
      competition_name: 'Campeonato Estadual',
      date: '2025-08-27',
      time: '16:00',
      location: 'Estádio Municipal',
      is_home: true,
      status: 'scheduled',
      created_at: '2025-01-01',
      updated_at: '2025-01-01',
    },
    {
      id: '2',
      club_id: 'club1',
      opponent_name: 'Unidos FC',
      competition_name: 'Copa Regional',
      date: '2025-08-30',
      time: '15:30',
      location: 'Arena do Adversário',
      is_home: false,
      status: 'scheduled',
      created_at: '2025-01-01',
      updated_at: '2025-01-01',
    },
    {
      id: '3',
      club_id: 'club1',
      opponent_name: 'Atlético Local',
      competition_name: 'Campeonato Estadual',
      date: '2025-08-20',
      time: '19:00',
      location: 'Estádio Municipal',
      is_home: true,
      status: 'finished',
      result: {
        home_score: 2,
        away_score: 1,
        winner: 'home',
      },
      created_at: '2025-01-01',
      updated_at: '2025-01-01',
    },
    {
      id: '4',
      club_id: 'club1',
      opponent_name: 'Esporte Clube',
      competition_name: 'Amistoso',
      date: '2025-08-15',
      time: '14:00',
      location: 'Campo de Treinamento',
      is_home: true,
      status: 'finished',
      result: {
        home_score: 0,
        away_score: 3,
        winner: 'away',
      },
      created_at: '2025-01-01',
      updated_at: '2025-01-01',
    },
  ];

  useFocusEffect(
    useCallback(() => {
      loadMatches();
    }, [])
  );

  const loadMatches = async () => {
    setLoading(true);
    try {
      // Simular carregamento da API
      await new Promise(resolve => setTimeout(resolve, 1000));
      setMatches(mockMatches);
      filterMatches(searchQuery, filters, selectedTab, mockMatches);
    } catch (error) {
      console.error('Erro ao carregar partidas:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadMatches();
    setRefreshing(false);
  }, []);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    filterMatches(query, filters, selectedTab, matches);
  };

  const handleFilter = (newFilters: MatchFilters) => {
    setFilters(newFilters);
    filterMatches(searchQuery, newFilters, selectedTab, matches);
    setShowFilters(false);
  };

  const handleTabChange = (tab: string) => {
    setSelectedTab(tab);
    filterMatches(searchQuery, filters, tab, matches);
  };

  const filterMatches = (
    query: string, 
    currentFilters: MatchFilters, 
    tab: string,
    allMatches: Match[]
  ) => {
    let filtered = [...allMatches];

    // Filtro por aba (próximas/passadas)
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (tab === 'upcoming') {
      filtered = filtered.filter(match => {
        const matchDate = new Date(match.date);
        return matchDate >= today || match.status === 'live' || match.status === 'halftime';
      });
    } else if (tab === 'past') {
      filtered = filtered.filter(match => {
        const matchDate = new Date(match.date);
        return matchDate < today && match.status === 'finished';
      });
    }

    // Filtro por busca
    if (query) {
      filtered = filtered.filter(match =>
        match.opponent_name.toLowerCase().includes(query.toLowerCase()) ||
        match.competition_name.toLowerCase().includes(query.toLowerCase()) ||
        match.location.toLowerCase().includes(query.toLowerCase())
      );
    }

    // Filtros específicos
    if (currentFilters.competition_id) {
      filtered = filtered.filter(match => 
        match.competition_id === currentFilters.competition_id
      );
    }

    if (currentFilters.status) {
      filtered = filtered.filter(match => 
        match.status === currentFilters.status
      );
    }

    if (currentFilters.is_home !== undefined) {
      filtered = filtered.filter(match => 
        match.is_home === currentFilters.is_home
      );
    }

    // Ordenar por data
    filtered.sort((a, b) => {
      const dateA = new Date(`${a.date} ${a.time}`);
      const dateB = new Date(`${b.date} ${b.time}`);
      
      if (tab === 'upcoming') {
        return dateA.getTime() - dateB.getTime(); // Próximas: mais próximas primeiro
      } else {
        return dateB.getTime() - dateA.getTime(); // Passadas: mais recentes primeiro
      }
    });

    setFilteredMatches(filtered);
  };

  const handleMatchPress = (match: Match) => {
    navigation.navigate('MatchDetail', { matchId: match.id });
  };

  const handleAddMatch = () => {
    navigation.navigate('MatchForm');
  };

  const getActiveFiltersCount = () => {
    return Object.values(filters).filter(value => value !== undefined && value !== '').length;
  };

  const renderMatch = ({ item }: { item: Match }) => (
    <MatchCard
      match={item}
      onPress={() => handleMatchPress(item)}
    />
  );

  const renderHeader = () => (
    <View style={styles.header}>
      <SegmentedButtons
        value={selectedTab}
        onValueChange={handleTabChange}
        buttons={[
          {
            value: 'upcoming',
            label: 'Próximas',
            icon: 'calendar-clock',
          },
          {
            value: 'past',
            label: 'Passadas',
            icon: 'history',
          },
        ]}
        style={styles.segmentedButtons}
      />

      <Searchbar
        placeholder="Buscar partidas..."
        onChangeText={handleSearch}
        value={searchQuery}
        style={styles.searchbar}
      />
      
      <View style={styles.filtersContainer}>
        <TouchableOpacity onPress={() => setShowFilters(true)}>
          <Chip
            icon="filter-variant"
            style={styles.filterChip}
            textStyle={styles.filterChipText}
          >
            Filtros {getActiveFiltersCount() > 0 && `(${getActiveFiltersCount()})`}
          </Chip>
        </TouchableOpacity>
      </View>

      <View style={styles.resultsContainer}>
        <Text variant="bodyMedium" style={styles.resultsText}>
          {filteredMatches.length} partida{filteredMatches.length !== 1 ? 's' : ''} encontrada{filteredMatches.length !== 1 ? 's' : ''}
        </Text>
      </View>
    </View>
  );

  const renderEmpty = () => (
    <View style={styles.emptyContainer}>
      <Text variant="titleMedium" style={styles.emptyTitle}>
        {selectedTab === 'upcoming' ? 'Nenhuma partida agendada' : 'Nenhuma partida encontrada'}
      </Text>
      <Text variant="bodyMedium" style={styles.emptyMessage}>
        {searchQuery || Object.keys(filters).length > 0
          ? 'Tente ajustar os filtros de busca'
          : selectedTab === 'upcoming' 
            ? 'Agende a primeira partida do clube'
            : 'Não há partidas finalizadas'
        }
      </Text>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text variant="bodyMedium" style={styles.loadingText}>
          Carregando partidas...
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <FlatList
        data={filteredMatches}
        renderItem={renderMatch}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={renderEmpty}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
      />

      <FAB
        icon="plus"
        style={[styles.fab, { backgroundColor: theme.colors.primary }]}
        onPress={handleAddMatch}
        label="Nova Partida"
      />

      <MatchFilterBottomSheet
        visible={showFilters}
        onDismiss={() => setShowFilters(false)}
        onApply={handleFilter}
        currentFilters={filters}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: spacing.md,
    opacity: 0.7,
  },
  listContent: {
    flexGrow: 1,
  },
  header: {
    padding: spacing.md,
    paddingBottom: spacing.sm,
  },
  segmentedButtons: {
    marginBottom: spacing.md,
  },
  searchbar: {
    marginBottom: spacing.md,
  },
  filtersContainer: {
    flexDirection: 'row',
    marginBottom: spacing.md,
  },
  filterChip: {
    marginRight: spacing.sm,
  },
  filterChipText: {
    fontSize: 12,
  },
  resultsContainer: {
    marginBottom: spacing.sm,
  },
  resultsText: {
    opacity: 0.7,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.xxxl,
  },
  emptyTitle: {
    fontWeight: '600',
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  emptyMessage: {
    textAlign: 'center',
    opacity: 0.7,
    lineHeight: 20,
  },
  fab: {
    position: 'absolute',
    margin: spacing.md,
    right: 0,
    bottom: 0,
  },
});
