import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Image,
} from 'react-native';
import {
  Text,
  Card,
  Chip,
  Button,
  useTheme,
  ActivityIndicator,
  Avatar,
  Divider,
  SegmentedButtons,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { spacing } from '@/theme';
import { 
  MedicalRecord, 
  getMedicalRecordTypeLabel, 
  getMedicalRecordTypeColor,
  getMedicalStatusLabel,
  getMedicalStatusColor,
  getMedicalPriorityLabel,
  getMedicalPriorityColor
} from '@/types/medical';

interface MedicalDetailScreenProps {
  route: {
    params: {
      recordId: string;
    };
  };
  navigation: any;
}

export default function MedicalDetailScreen({ route, navigation }: MedicalDetailScreenProps) {
  const theme = useTheme();
  const { recordId } = route.params;
  const [record, setRecord] = useState<MedicalRecord | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedTab, setSelectedTab] = useState('info');

  // Mock data - em produção viria da API
  const mockRecord: MedicalRecord = {
    id: recordId,
    athlete_id: 'athlete1',
    athlete: {
      id: 'athlete1',
      name: 'João Silva',
      jersey_number: 10,
      position: { name: 'Meio-campo', abbreviation: 'MC', type: 'midfielder', color: '#4caf50' },
      photo_url: 'https://via.placeholder.com/100',
    } as any,
    type: 'injury',
    title: 'Lesão Muscular - Coxa',
    description: 'Distensão muscular grau 1 no quadríceps direito durante treino. Atleta relatou dor súbita durante sprint.',
    date: '2025-08-20',
    professional_name: 'Dr. Ana Costa',
    professional_specialty: 'Medicina Esportiva',
    status: 'in_progress',
    priority: 'high',
    diagnosis: 'Distensão muscular grau 1 no quadríceps direito',
    treatment: 'Repouso relativo por 7-10 dias, fisioterapia diária, aplicação de gelo 3x ao dia, anti-inflamatório conforme prescrição.',
    follow_up_date: '2025-08-30',
    notes: 'Atleta apresentou boa evolução nas primeiras 48h. Dor diminuiu significativamente. Manter protocolo atual.',
    medications: [
      {
        id: '1',
        name: 'Ibuprofeno 600mg',
        dosage: '600mg',
        frequency: '8/8 horas',
        duration: '5 dias',
        instructions: 'Tomar após as refeições',
        start_date: '2025-08-20',
        end_date: '2025-08-25',
        active: true,
      }
    ],
    created_at: '2025-01-01',
    updated_at: '2025-01-01',
  };

  useEffect(() => {
    loadRecord();
  }, [recordId]);

  const loadRecord = async () => {
    setLoading(true);
    try {
      // Simular carregamento da API
      await new Promise(resolve => setTimeout(resolve, 1000));
      setRecord(mockRecord);
    } catch (error) {
      console.error('Erro ao carregar registro médico:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatRecordDate = () => {
    if (!record) return '';
    try {
      const recordDate = new Date(record.date);
      return format(recordDate, "EEEE, dd 'de' MMMM 'de' yyyy", { locale: ptBR });
    } catch {
      return record.date;
    }
  };

  const getTypeColor = () => {
    if (!record) return theme.colors.outline;
    return getMedicalRecordTypeColor(record.type);
  };

  const getStatusColor = () => {
    if (!record) return theme.colors.outline;
    return getMedicalStatusColor(record.status);
  };

  const getPriorityColor = () => {
    if (!record) return theme.colors.outline;
    return getMedicalPriorityColor(record.priority);
  };

  const isUrgentRecord = () => {
    return record?.priority === 'urgent' || record?.priority === 'high';
  };

  const isActiveRecord = () => {
    return record?.status === 'in_progress' || record?.status === 'scheduled';
  };

  const handleEdit = () => {
    navigation.navigate('MedicalForm', { recordId: record?.id });
  };

  const handleAddFollowUp = () => {
    navigation.navigate('MedicalForm', { 
      athleteId: record?.athlete_id,
      parentRecordId: record?.id 
    });
  };

  const renderRecordHeader = () => {
    if (!record) return null;

    return (
      <Card style={[
        styles.headerCard,
        isUrgentRecord() && styles.urgentCard
      ]}>
        <Card.Content style={styles.headerContent}>
          <View style={styles.statusContainer}>
            <View style={styles.statusChips}>
              <Chip
                style={[styles.statusChip, { backgroundColor: `${getStatusColor()}20` }]}
                textStyle={[styles.statusText, { color: getStatusColor() }]}
                icon={isActiveRecord() ? 'circle' : undefined}
              >
                {getMedicalStatusLabel(record.status)}
              </Chip>
              
              <Chip
                style={[styles.priorityChip, { backgroundColor: `${getPriorityColor()}20` }]}
                textStyle={[styles.priorityText, { color: getPriorityColor() }]}
              >
                {getMedicalPriorityLabel(record.priority)}
              </Chip>
            </View>
          </View>

          <View style={styles.titleContainer}>
            <View style={[styles.typeIcon, { backgroundColor: `${getTypeColor()}20` }]}>
              <Icon 
                name="healing" 
                size={32} 
                color={getTypeColor()} 
              />
            </View>
            
            <View style={styles.titleText}>
              <Text variant="headlineSmall" style={styles.title}>
                {record.title}
              </Text>
              
              <Chip
                style={[styles.typeChip, { backgroundColor: `${getTypeColor()}20` }]}
                textStyle={[styles.typeText, { color: getTypeColor() }]}
                compact
              >
                {getMedicalRecordTypeLabel(record.type)}
              </Chip>
            </View>
          </View>

          {/* Informações do Atleta */}
          <View style={styles.athleteContainer}>
            {record.athlete.photo_url ? (
              <Image
                source={{ uri: record.athlete.photo_url }}
                style={styles.athletePhoto}
              />
            ) : (
              <Avatar.Text
                size={60}
                label={record.athlete.name.split(' ').map(n => n[0]).join('').substring(0, 2)}
                style={[styles.athleteAvatar, { backgroundColor: record.athlete.position?.color || theme.colors.primary }]}
              />
            )}
            
            <View style={styles.athleteInfo}>
              <Text variant="titleMedium" style={styles.athleteName}>
                {record.athlete.name}
              </Text>
              {record.athlete.jersey_number && (
                <Text variant="bodyMedium" style={styles.athleteDetails}>
                  #{record.athlete.jersey_number} • {record.athlete.position?.name}
                </Text>
              )}
            </View>
          </View>

          {record.description && (
            <Text variant="bodyMedium" style={styles.description}>
              {record.description}
            </Text>
          )}

          <View style={styles.recordDetails}>
            <View style={styles.detailItem}>
              <Icon name="event" size={20} color={theme.colors.outline} />
              <Text variant="bodyMedium" style={styles.detailText}>
                {formatRecordDate()}
              </Text>
            </View>
            
            {record.professional_name && (
              <View style={styles.detailItem}>
                <Icon name="person" size={20} color={theme.colors.outline} />
                <Text variant="bodyMedium" style={styles.detailText}>
                  {record.professional_name}
                  {record.professional_specialty && ` - ${record.professional_specialty}`}
                </Text>
              </View>
            )}
            
            {record.follow_up_date && (
              <View style={styles.detailItem}>
                <Icon name="event-repeat" size={20} color={theme.colors.outline} />
                <Text variant="bodyMedium" style={styles.detailText}>
                  Retorno: {format(new Date(record.follow_up_date), "dd/MM/yyyy")}
                </Text>
              </View>
            )}
          </View>
        </Card.Content>
      </Card>
    );
  };

  const renderTabContent = () => {
    switch (selectedTab) {
      case 'info':
        return (
          <Card style={styles.card}>
            <Card.Content>
              <Text variant="titleMedium" style={styles.sectionTitle}>
                Informações Médicas
              </Text>
              <Divider style={styles.divider} />
              
              {record?.diagnosis && (
                <View style={styles.infoSection}>
                  <Text variant="bodySmall" style={styles.infoLabel}>Diagnóstico</Text>
                  <Text variant="bodyMedium">{record.diagnosis}</Text>
                </View>
              )}
              
              {record?.treatment && (
                <View style={styles.infoSection}>
                  <Text variant="bodySmall" style={styles.infoLabel}>Tratamento</Text>
                  <Text variant="bodyMedium">{record.treatment}</Text>
                </View>
              )}
              
              {record?.notes && (
                <View style={styles.infoSection}>
                  <Text variant="bodySmall" style={styles.infoLabel}>Observações</Text>
                  <Text variant="bodyMedium">{record.notes}</Text>
                </View>
              )}
            </Card.Content>
          </Card>
        );
      
      case 'medications':
        return (
          <Card style={styles.card}>
            <Card.Content>
              <Text variant="titleMedium" style={styles.sectionTitle}>
                Medicamentos
              </Text>
              <Divider style={styles.divider} />
              
              {record?.medications && record.medications.length > 0 ? (
                record.medications.map((medication) => (
                  <View key={medication.id} style={styles.medicationItem}>
                    <View style={styles.medicationHeader}>
                      <Text variant="titleSmall" style={styles.medicationName}>
                        {medication.name}
                      </Text>
                      <Chip
                        style={[
                          styles.medicationStatus,
                          { backgroundColor: medication.active ? '#4caf5020' : '#f4433620' }
                        ]}
                        textStyle={[
                          styles.medicationStatusText,
                          { color: medication.active ? '#4caf50' : '#f44336' }
                        ]}
                        compact
                      >
                        {medication.active ? 'Ativo' : 'Inativo'}
                      </Chip>
                    </View>
                    
                    <Text variant="bodySmall" style={styles.medicationDetails}>
                      {medication.dosage} • {medication.frequency} • {medication.duration}
                    </Text>
                    
                    {medication.instructions && (
                      <Text variant="bodySmall" style={styles.medicationInstructions}>
                        {medication.instructions}
                      </Text>
                    )}
                  </View>
                ))
              ) : (
                <Text variant="bodyMedium" style={styles.emptyMessage}>
                  Nenhum medicamento prescrito
                </Text>
              )}
            </Card.Content>
          </Card>
        );
      
      case 'files':
        return (
          <Card style={styles.card}>
            <Card.Content>
              <Text variant="titleMedium" style={styles.sectionTitle}>
                Arquivos e Exames
              </Text>
              <Divider style={styles.divider} />
              
              <Text variant="bodyMedium" style={styles.emptyMessage}>
                Nenhum arquivo anexado
              </Text>
              
              <Button
                mode="contained"
                onPress={() => console.log('Adicionar arquivo')}
                style={styles.actionButton}
                icon="attach-file"
              >
                Adicionar Arquivo
              </Button>
            </Card.Content>
          </Card>
        );
      
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text variant="bodyMedium" style={styles.loadingText}>
          Carregando registro médico...
        </Text>
      </View>
    );
  }

  if (!record) {
    return (
      <View style={styles.errorContainer}>
        <Text variant="titleMedium">Registro médico não encontrado</Text>
        <Button mode="outlined" onPress={() => navigation.goBack()}>
          Voltar
        </Button>
      </View>
    );
  }

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {renderRecordHeader()}

      <View style={styles.tabsContainer}>
        <SegmentedButtons
          value={selectedTab}
          onValueChange={setSelectedTab}
          buttons={[
            {
              value: 'info',
              label: 'Info',
              icon: 'info',
            },
            {
              value: 'medications',
              label: 'Medicamentos',
              icon: 'medication',
            },
            {
              value: 'files',
              label: 'Arquivos',
              icon: 'attach-file',
            },
          ]}
          style={styles.segmentedButtons}
        />
      </View>

      {renderTabContent()}

      {/* Ações */}
      <View style={styles.actions}>
        <Button
          mode="outlined"
          onPress={handleAddFollowUp}
          style={styles.actionButton}
          icon="add-circle"
        >
          Adicionar Retorno
        </Button>
        
        <Button
          mode="contained"
          onPress={handleEdit}
          style={styles.actionButton}
          icon="edit"
        >
          Editar Registro
        </Button>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: spacing.md,
    opacity: 0.7,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.lg,
  },
  headerCard: {
    margin: spacing.md,
    marginBottom: spacing.sm,
  },
  urgentCard: {
    borderLeftWidth: 4,
    borderLeftColor: '#f44336',
  },
  headerContent: {
    padding: spacing.lg,
  },
  statusContainer: {
    marginBottom: spacing.lg,
  },
  statusChips: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  statusChip: {
    height: 28,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  priorityChip: {
    height: 28,
  },
  priorityText: {
    fontSize: 12,
    fontWeight: '600',
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  typeIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  titleText: {
    flex: 1,
  },
  title: {
    fontWeight: 'bold',
    marginBottom: spacing.sm,
  },
  typeChip: {
    height: 28,
    alignSelf: 'flex-start',
  },
  typeText: {
    fontSize: 12,
    fontWeight: '600',
  },
  athleteContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.lg,
    paddingTop: spacing.md,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  athletePhoto: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginRight: spacing.md,
  },
  athleteAvatar: {
    marginRight: spacing.md,
  },
  athleteInfo: {
    flex: 1,
  },
  athleteName: {
    fontWeight: '600',
    marginBottom: spacing.xs,
  },
  athleteDetails: {
    opacity: 0.7,
  },
  description: {
    opacity: 0.8,
    lineHeight: 20,
    marginBottom: spacing.lg,
  },
  recordDetails: {
    gap: spacing.sm,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  detailText: {
    marginLeft: spacing.sm,
  },
  tabsContainer: {
    padding: spacing.md,
    paddingBottom: spacing.sm,
  },
  segmentedButtons: {
    marginBottom: spacing.sm,
  },
  card: {
    margin: spacing.md,
    marginTop: spacing.sm,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: spacing.sm,
  },
  divider: {
    marginBottom: spacing.md,
  },
  infoSection: {
    marginBottom: spacing.lg,
  },
  infoLabel: {
    opacity: 0.7,
    marginBottom: spacing.sm,
    fontWeight: '500',
  },
  medicationItem: {
    marginBottom: spacing.lg,
    paddingBottom: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  medicationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  medicationName: {
    fontWeight: '600',
    flex: 1,
  },
  medicationStatus: {
    height: 24,
  },
  medicationStatusText: {
    fontSize: 10,
    fontWeight: '600',
  },
  medicationDetails: {
    opacity: 0.7,
    marginBottom: spacing.xs,
  },
  medicationInstructions: {
    opacity: 0.6,
    fontStyle: 'italic',
  },
  emptyMessage: {
    textAlign: 'center',
    opacity: 0.6,
    fontStyle: 'italic',
    paddingVertical: spacing.lg,
  },
  actions: {
    padding: spacing.md,
    paddingBottom: spacing.xl,
    gap: spacing.md,
  },
  actionButton: {
    marginTop: spacing.sm,
  },
});
