import React, { useState, useCallback } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  RefreshControl,
  TouchableOpacity,
} from 'react-native';
import {
  Text,
  Searchbar,
  FAB,
  Chip,
  useTheme,
  ActivityIndicator,
  SegmentedButtons,
} from 'react-native-paper';
import { useFocusEffect } from '@react-navigation/native';
import { spacing } from '@/theme';
import MedicalCard from '@/components/medical/MedicalCard';
import MedicalFilterBottomSheet from '@/components/medical/MedicalFilterBottomSheet';
import { MedicalRecord, MedicalFilters, MedicalStatus } from '@/types/medical';

interface MedicalListScreenProps {
  navigation: any;
}

export default function MedicalListScreen({ navigation }: MedicalListScreenProps) {
  const theme = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [medicalRecords, setMedicalRecords] = useState<MedicalRecord[]>([]);
  const [filteredRecords, setFilteredRecords] = useState<MedicalRecord[]>([]);
  const [filters, setFilters] = useState<MedicalFilters>({});
  const [showFilters, setShowFilters] = useState(false);
  const [selectedTab, setSelectedTab] = useState('upcoming');

  // Dados mockados para demonstração
  const mockMedicalRecords: MedicalRecord[] = [
    {
      id: '1',
      athlete_id: 'athlete1',
      athlete: {
        id: 'athlete1',
        name: 'João Silva',
        jersey_number: 10,
        position: { name: 'Meio-campo', abbreviation: 'MC', type: 'midfielder', color: '#4caf50' },
        photo_url: 'https://via.placeholder.com/50',
      } as any,
      type: 'consultation',
      title: 'Consulta Ortopédica',
      description: 'Avaliação de dor no joelho direito',
      date: '2025-08-26',
      professional_name: 'Dr. Carlos Mendes',
      professional_specialty: 'Ortopedia',
      status: 'scheduled',
      priority: 'medium',
      created_at: '2025-01-01',
      updated_at: '2025-01-01',
    },
    {
      id: '2',
      athlete_id: 'athlete2',
      athlete: {
        id: 'athlete2',
        name: 'Pedro Santos',
        jersey_number: 9,
        position: { name: 'Atacante', abbreviation: 'ATA', type: 'forward', color: '#f44336' },
        photo_url: 'https://via.placeholder.com/50',
      } as any,
      type: 'injury',
      title: 'Lesão Muscular - Coxa',
      description: 'Distensão muscular grau 1 no quadríceps',
      date: '2025-08-20',
      professional_name: 'Dr. Ana Costa',
      professional_specialty: 'Medicina Esportiva',
      status: 'in_progress',
      priority: 'high',
      diagnosis: 'Distensão muscular grau 1',
      treatment: 'Repouso, fisioterapia e anti-inflamatório',
      follow_up_date: '2025-08-30',
      created_at: '2025-01-01',
      updated_at: '2025-01-01',
    },
    {
      id: '3',
      athlete_id: 'athlete3',
      athlete: {
        id: 'athlete3',
        name: 'Carlos Oliveira',
        jersey_number: 1,
        position: { name: 'Goleiro', abbreviation: 'GOL', type: 'goalkeeper', color: '#ffc107' },
        photo_url: 'https://via.placeholder.com/50',
      } as any,
      type: 'exam',
      title: 'Exames de Sangue',
      description: 'Hemograma completo e perfil lipídico',
      date: '2025-08-15',
      professional_name: 'Lab. Central',
      status: 'completed',
      priority: 'low',
      created_at: '2025-01-01',
      updated_at: '2025-01-01',
    },
    {
      id: '4',
      athlete_id: 'athlete1',
      athlete: {
        id: 'athlete1',
        name: 'João Silva',
        jersey_number: 10,
        position: { name: 'Meio-campo', abbreviation: 'MC', type: 'midfielder', color: '#4caf50' },
        photo_url: 'https://via.placeholder.com/50',
      } as any,
      type: 'physiotherapy',
      title: 'Fisioterapia - Joelho',
      description: 'Sessão de fisioterapia para fortalecimento',
      date: '2025-08-28',
      professional_name: 'Fisio. Maria Silva',
      professional_specialty: 'Fisioterapia Esportiva',
      status: 'scheduled',
      priority: 'medium',
      created_at: '2025-01-01',
      updated_at: '2025-01-01',
    },
  ];

  useFocusEffect(
    useCallback(() => {
      loadMedicalRecords();
    }, [])
  );

  const loadMedicalRecords = async () => {
    setLoading(true);
    try {
      // Simular carregamento da API
      await new Promise(resolve => setTimeout(resolve, 1000));
      setMedicalRecords(mockMedicalRecords);
      filterRecords(searchQuery, filters, selectedTab, mockMedicalRecords);
    } catch (error) {
      console.error('Erro ao carregar registros médicos:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadMedicalRecords();
    setRefreshing(false);
  }, []);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    filterRecords(query, filters, selectedTab, medicalRecords);
  };

  const handleFilter = (newFilters: MedicalFilters) => {
    setFilters(newFilters);
    filterRecords(searchQuery, newFilters, selectedTab, medicalRecords);
    setShowFilters(false);
  };

  const handleTabChange = (tab: string) => {
    setSelectedTab(tab);
    filterRecords(searchQuery, filters, tab, medicalRecords);
  };

  const filterRecords = (
    query: string, 
    currentFilters: MedicalFilters, 
    tab: string,
    allRecords: MedicalRecord[]
  ) => {
    let filtered = [...allRecords];

    // Filtro por aba (próximos/passados)
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (tab === 'upcoming') {
      filtered = filtered.filter(record => {
        const recordDate = new Date(record.date);
        return recordDate >= today || record.status === 'in_progress' || record.status === 'scheduled';
      });
    } else if (tab === 'past') {
      filtered = filtered.filter(record => {
        const recordDate = new Date(record.date);
        return recordDate < today && record.status === 'completed';
      });
    }

    // Filtro por busca
    if (query) {
      filtered = filtered.filter(record =>
        record.title.toLowerCase().includes(query.toLowerCase()) ||
        record.description?.toLowerCase().includes(query.toLowerCase()) ||
        record.athlete.name.toLowerCase().includes(query.toLowerCase()) ||
        record.professional_name?.toLowerCase().includes(query.toLowerCase())
      );
    }

    // Filtros específicos
    if (currentFilters.athlete_id) {
      filtered = filtered.filter(record => 
        record.athlete_id === currentFilters.athlete_id
      );
    }

    if (currentFilters.type) {
      filtered = filtered.filter(record => 
        record.type === currentFilters.type
      );
    }

    if (currentFilters.status) {
      filtered = filtered.filter(record => 
        record.status === currentFilters.status
      );
    }

    if (currentFilters.priority) {
      filtered = filtered.filter(record => 
        record.priority === currentFilters.priority
      );
    }

    // Ordenar por data e prioridade
    filtered.sort((a, b) => {
      // Primeiro por prioridade (urgente primeiro)
      const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
      const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
      
      if (priorityDiff !== 0) return priorityDiff;
      
      // Depois por data
      const dateA = new Date(a.date);
      const dateB = new Date(b.date);
      
      if (tab === 'upcoming') {
        return dateA.getTime() - dateB.getTime(); // Próximos: mais próximos primeiro
      } else {
        return dateB.getTime() - dateA.getTime(); // Passados: mais recentes primeiro
      }
    });

    setFilteredRecords(filtered);
  };

  const handleRecordPress = (record: MedicalRecord) => {
    navigation.navigate('MedicalDetail', { recordId: record.id });
  };

  const handleAddRecord = () => {
    navigation.navigate('MedicalForm');
  };

  const getActiveFiltersCount = () => {
    return Object.values(filters).filter(value => value !== undefined && value !== '').length;
  };

  const renderRecord = ({ item }: { item: MedicalRecord }) => (
    <MedicalCard
      record={item}
      onPress={() => handleRecordPress(item)}
    />
  );

  const renderHeader = () => (
    <View style={styles.header}>
      <SegmentedButtons
        value={selectedTab}
        onValueChange={handleTabChange}
        buttons={[
          {
            value: 'upcoming',
            label: 'Próximos',
            icon: 'calendar-clock',
          },
          {
            value: 'past',
            label: 'Histórico',
            icon: 'history',
          },
        ]}
        style={styles.segmentedButtons}
      />

      <Searchbar
        placeholder="Buscar registros médicos..."
        onChangeText={handleSearch}
        value={searchQuery}
        style={styles.searchbar}
      />
      
      <View style={styles.filtersContainer}>
        <TouchableOpacity onPress={() => setShowFilters(true)}>
          <Chip
            icon="filter-variant"
            style={styles.filterChip}
            textStyle={styles.filterChipText}
          >
            Filtros {getActiveFiltersCount() > 0 && `(${getActiveFiltersCount()})`}
          </Chip>
        </TouchableOpacity>
      </View>

      <View style={styles.resultsContainer}>
        <Text variant="bodyMedium" style={styles.resultsText}>
          {filteredRecords.length} registro{filteredRecords.length !== 1 ? 's' : ''} encontrado{filteredRecords.length !== 1 ? 's' : ''}
        </Text>
      </View>
    </View>
  );

  const renderEmpty = () => (
    <View style={styles.emptyContainer}>
      <Text variant="titleMedium" style={styles.emptyTitle}>
        {selectedTab === 'upcoming' ? 'Nenhum registro agendado' : 'Nenhum registro encontrado'}
      </Text>
      <Text variant="bodyMedium" style={styles.emptyMessage}>
        {searchQuery || Object.keys(filters).length > 0
          ? 'Tente ajustar os filtros de busca'
          : selectedTab === 'upcoming' 
            ? 'Agende a primeira consulta médica'
            : 'Não há registros médicos finalizados'
        }
      </Text>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text variant="bodyMedium" style={styles.loadingText}>
          Carregando registros médicos...
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <FlatList
        data={filteredRecords}
        renderItem={renderRecord}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={renderEmpty}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
      />

      <FAB
        icon="plus"
        style={[styles.fab, { backgroundColor: theme.colors.primary }]}
        onPress={handleAddRecord}
        label="Novo Registro"
      />

      <MedicalFilterBottomSheet
        visible={showFilters}
        onDismiss={() => setShowFilters(false)}
        onApply={handleFilter}
        currentFilters={filters}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: spacing.md,
    opacity: 0.7,
  },
  listContent: {
    flexGrow: 1,
  },
  header: {
    padding: spacing.md,
    paddingBottom: spacing.sm,
  },
  segmentedButtons: {
    marginBottom: spacing.md,
  },
  searchbar: {
    marginBottom: spacing.md,
  },
  filtersContainer: {
    flexDirection: 'row',
    marginBottom: spacing.md,
  },
  filterChip: {
    marginRight: spacing.sm,
  },
  filterChipText: {
    fontSize: 12,
  },
  resultsContainer: {
    marginBottom: spacing.sm,
  },
  resultsText: {
    opacity: 0.7,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.xxxl,
  },
  emptyTitle: {
    fontWeight: '600',
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  emptyMessage: {
    textAlign: 'center',
    opacity: 0.7,
    lineHeight: 20,
  },
  fab: {
    position: 'absolute',
    margin: spacing.md,
    right: 0,
    bottom: 0,
  },
});
