import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
} from 'react-native';
import {
  Text,
  Card,
  Chip,
  Button,
  useTheme,
  ActivityIndicator,
  Divider,
  SegmentedButtons,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { spacing } from '@/theme';
import { 
  Report, 
  getReportTypeLabel,
  getReportCategoryLabel,
  getReportCategoryColor,
  getReportCategoryIcon,
  getReportStatusLabel,
  getReportStatusColor,
  formatReportPeriod
} from '@/types/reports';

interface ReportDetailScreenProps {
  route: {
    params: {
      reportId: string;
    };
  };
  navigation: any;
}

export default function ReportDetailScreen({ route, navigation }: ReportDetailScreenProps) {
  const theme = useTheme();
  const { reportId } = route.params;
  const [report, setReport] = useState<Report | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedTab, setSelectedTab] = useState('summary');

  // Mock data - em produção viria da API
  const mockReport: Report = {
    id: reportId,
    club_id: 'club1',
    title: 'Performance dos Atletas - Agosto 2025',
    description: 'Relatório detalhado da performance dos atletas durante o mês de agosto, incluindo análises técnicas, físicas e táticas.',
    type: 'athletes_performance',
    category: 'athletes',
    period_start: '2025-08-01',
    period_end: '2025-08-31',
    data: {
      summary: {
        total_records: 25,
        period_description: 'Agosto 2025',
        key_metrics: [
          { label: 'Atletas Avaliados', value: 25, unit: 'atletas', change: 3, change_type: 'increase' },
          { label: 'Média Geral', value: 8.2, unit: '/10', change: 0.5, change_type: 'increase' },
          { label: 'Melhoria', value: 12, unit: '%', change: 5, change_type: 'increase' },
          { label: 'Presença Média', value: 92, unit: '%', change: -2, change_type: 'decrease' },
        ],
        highlights: [
          'Melhoria significativa na categoria sub-20',
          'Destaque para João Silva com nota 9.5',
          'Aumento de 15% no condicionamento físico',
          'Redução de lesões em 30%'
        ],
        insights: [
          'Necessário foco em condicionamento físico para categoria sub-17',
          'Implementar treinos específicos de finalização',
          'Aumentar trabalho de força para defensores'
        ],
      },
      details: [
        {
          id: '1',
          name: 'João Silva',
          category: 'Sub-20',
          metrics: { nota: 9.5, presenca: 95, gols: 8, assistencias: 5 },
          rank: 1,
          trend: 'up'
        },
        {
          id: '2',
          name: 'Pedro Santos',
          category: 'Sub-20',
          metrics: { nota: 8.8, presenca: 90, gols: 6, assistencias: 7 },
          rank: 2,
          trend: 'up'
        },
        {
          id: '3',
          name: 'Carlos Lima',
          category: 'Sub-17',
          metrics: { nota: 8.5, presenca: 88, gols: 4, assistencias: 3 },
          rank: 3,
          trend: 'stable'
        },
      ],
      totals: {
        total_gols: 45,
        total_assistencias: 32,
        media_presenca: 92,
        media_nota: 8.2
      },
    },
    status: 'completed',
    generated_by: 'user1',
    generated_at: '2025-08-24T10:00:00Z',
    file_url: 'https://example.com/report1.pdf',
    file_size: 2048000, // 2MB
    expires_at: '2025-09-24T10:00:00Z',
    created_at: '2025-08-24T10:00:00Z',
    updated_at: '2025-08-24T10:00:00Z',
  };

  useEffect(() => {
    loadReport();
  }, [reportId]);

  const loadReport = async () => {
    setLoading(true);
    try {
      // Simular carregamento da API
      await new Promise(resolve => setTimeout(resolve, 1000));
      setReport(mockReport);
    } catch (error) {
      console.error('Erro ao carregar relatório:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatGeneratedDate = () => {
    if (!report) return '';
    try {
      const generatedDate = new Date(report.generated_at);
      return format(generatedDate, "dd/MM/yyyy 'às' HH:mm", { locale: ptBR });
    } catch {
      return 'Data inválida';
    }
  };

  const getCategoryColor = () => {
    if (!report) return theme.colors.outline;
    return getReportCategoryColor(report.category);
  };

  const getStatusColor = () => {
    if (!report) return theme.colors.outline;
    return getReportStatusColor(report.status);
  };

  const isCompletedReport = () => {
    return report?.status === 'completed';
  };

  const getFileSize = () => {
    if (!report?.file_size) return '';
    const sizeInMB = report.file_size / (1024 * 1024);
    return `${sizeInMB.toFixed(1)} MB`;
  };

  const handleDownload = () => {
    // Implementar lógica para download do PDF
    console.log('Download PDF');
  };

  const handleShare = () => {
    // Implementar lógica para compartilhar relatório
    console.log('Compartilhar relatório');
  };

  const handleRegenerate = () => {
    // Implementar lógica para regenerar relatório
    console.log('Regenerar relatório');
  };

  const renderReportHeader = () => {
    if (!report) return null;

    return (
      <Card style={styles.headerCard}>
        <Card.Content style={styles.headerContent}>
          <View style={styles.statusContainer}>
            <Chip
              style={[styles.statusChip, { backgroundColor: `${getStatusColor()}20` }]}
              textStyle={[styles.statusText, { color: getStatusColor() }]}
              icon={isCompletedReport() ? 'check-circle' : 'hourglass-empty'}
            >
              {getReportStatusLabel(report.status)}
            </Chip>
          </View>

          <View style={styles.titleContainer}>
            <View style={[styles.categoryIcon, { backgroundColor: `${getCategoryColor()}20` }]}>
              <Icon 
                name={getReportCategoryIcon(report.category)} 
                size={32} 
                color={getCategoryColor()} 
              />
            </View>
            
            <View style={styles.titleText}>
              <Text variant="headlineSmall" style={styles.title}>
                {report.title}
              </Text>
              
              <View style={styles.categoryContainer}>
                <View style={[styles.categoryDot, { backgroundColor: getCategoryColor() }]} />
                <Text variant="titleSmall" style={styles.categoryName}>
                  {getReportCategoryLabel(report.category)}
                </Text>
              </View>
            </View>
          </View>

          {report.description && (
            <Text variant="bodyMedium" style={styles.description}>
              {report.description}
            </Text>
          )}

          <View style={styles.reportInfo}>
            <View style={styles.infoItem}>
              <Icon name="date-range" size={20} color={theme.colors.outline} />
              <Text variant="bodyMedium" style={styles.infoText}>
                {formatReportPeriod(report.period_start, report.period_end)}
              </Text>
            </View>
            
            <View style={styles.infoItem}>
              <Icon name="schedule" size={20} color={theme.colors.outline} />
              <Text variant="bodyMedium" style={styles.infoText}>
                Gerado em {formatGeneratedDate()}
              </Text>
            </View>
            
            {isCompletedReport() && report.file_url && (
              <View style={styles.infoItem}>
                <Icon name="description" size={20} color={theme.colors.outline} />
                <Text variant="bodyMedium" style={styles.infoText}>
                  PDF - {getFileSize()}
                </Text>
              </View>
            )}
          </View>
        </Card.Content>
      </Card>
    );
  };

  const renderTabContent = () => {
    switch (selectedTab) {
      case 'summary':
        return (
          <Card style={styles.card}>
            <Card.Content>
              <Text variant="titleMedium" style={styles.sectionTitle}>
                Resumo Executivo
              </Text>
              <Divider style={styles.divider} />
              
              {/* Métricas Principais */}
              <View style={styles.metricsContainer}>
                <Text variant="bodyMedium" style={styles.subsectionTitle}>
                  Métricas Principais
                </Text>
                <View style={styles.metricsGrid}>
                  {report?.data.summary.key_metrics.map((metric, index) => (
                    <View key={index} style={styles.metricCard}>
                      <Text variant="bodySmall" style={styles.metricLabel}>
                        {metric.label}
                      </Text>
                      <Text variant="headlineSmall" style={styles.metricValue}>
                        {typeof metric.value === 'number' ? metric.value.toLocaleString('pt-BR') : metric.value}
                        {metric.unit && <Text variant="bodyMedium"> {metric.unit}</Text>}
                      </Text>
                      {metric.change && (
                        <View style={styles.changeContainer}>
                          <Icon 
                            name={metric.change_type === 'increase' ? 'trending-up' : 'trending-down'} 
                            size={16} 
                            color={metric.change_type === 'increase' ? '#4caf50' : '#f44336'} 
                          />
                          <Text variant="bodySmall" style={[
                            styles.changeText,
                            { color: metric.change_type === 'increase' ? '#4caf50' : '#f44336' }
                          ]}>
                            {Math.abs(metric.change)}%
                          </Text>
                        </View>
                      )}
                    </View>
                  ))}
                </View>
              </View>
              
              {/* Destaques */}
              {report?.data.summary.highlights && report.data.summary.highlights.length > 0 && (
                <View style={styles.highlightsContainer}>
                  <Text variant="bodyMedium" style={styles.subsectionTitle}>
                    Principais Destaques
                  </Text>
                  {report.data.summary.highlights.map((highlight, index) => (
                    <View key={index} style={styles.highlightItem}>
                      <Icon name="star" size={16} color="#ff9800" />
                      <Text variant="bodyMedium" style={styles.highlightText}>
                        {highlight}
                      </Text>
                    </View>
                  ))}
                </View>
              )}
              
              {/* Insights */}
              {report?.data.summary.insights && report.data.summary.insights.length > 0 && (
                <View style={styles.insightsContainer}>
                  <Text variant="bodyMedium" style={styles.subsectionTitle}>
                    Insights e Recomendações
                  </Text>
                  {report.data.summary.insights.map((insight, index) => (
                    <View key={index} style={styles.insightItem}>
                      <Icon name="lightbulb" size={16} color="#2196f3" />
                      <Text variant="bodyMedium" style={styles.insightText}>
                        {insight}
                      </Text>
                    </View>
                  ))}
                </View>
              )}
            </Card.Content>
          </Card>
        );
      
      case 'details':
        return (
          <Card style={styles.card}>
            <Card.Content>
              <Text variant="titleMedium" style={styles.sectionTitle}>
                Detalhes dos Dados
              </Text>
              <Divider style={styles.divider} />
              
              <Text variant="bodyMedium" style={styles.recordsCount}>
                {report?.data.summary.total_records} registro{report?.data.summary.total_records !== 1 ? 's' : ''} analisado{report?.data.summary.total_records !== 1 ? 's' : ''}
              </Text>
              
              {report?.data.details && report.data.details.length > 0 && (
                <View style={styles.detailsList}>
                  {report.data.details.slice(0, 5).map((detail, index) => (
                    <View key={detail.id} style={styles.detailItem}>
                      <View style={styles.detailHeader}>
                        <Text variant="titleSmall" style={styles.detailName}>
                          #{detail.rank} {detail.name}
                        </Text>
                        {detail.trend && (
                          <Icon 
                            name={
                              detail.trend === 'up' ? 'trending-up' : 
                              detail.trend === 'down' ? 'trending-down' : 
                              'trending-flat'
                            } 
                            size={16} 
                            color={
                              detail.trend === 'up' ? '#4caf50' : 
                              detail.trend === 'down' ? '#f44336' : 
                              '#9e9e9e'
                            } 
                          />
                        )}
                      </View>
                      <Text variant="bodySmall" style={styles.detailCategory}>
                        {detail.category}
                      </Text>
                      <View style={styles.detailMetrics}>
                        {Object.entries(detail.metrics).map(([key, value]) => (
                          <Text key={key} variant="bodySmall" style={styles.detailMetric}>
                            {key}: {value}
                          </Text>
                        ))}
                      </View>
                    </View>
                  ))}
                  
                  {report.data.details.length > 5 && (
                    <Text variant="bodySmall" style={styles.moreDetails}>
                      +{report.data.details.length - 5} registros adicionais no PDF completo
                    </Text>
                  )}
                </View>
              )}
            </Card.Content>
          </Card>
        );
      
      case 'charts':
        return (
          <Card style={styles.card}>
            <Card.Content>
              <Text variant="titleMedium" style={styles.sectionTitle}>
                Gráficos e Visualizações
              </Text>
              <Divider style={styles.divider} />
              
              <Text variant="bodyMedium" style={styles.emptyMessage}>
                Gráficos disponíveis apenas no PDF completo
              </Text>
              
              <Button
                mode="outlined"
                onPress={handleDownload}
                style={styles.actionButton}
                icon="download"
                disabled={!isCompletedReport()}
              >
                Baixar PDF com Gráficos
              </Button>
            </Card.Content>
          </Card>
        );
      
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text variant="bodyMedium" style={styles.loadingText}>
          Carregando relatório...
        </Text>
      </View>
    );
  }

  if (!report) {
    return (
      <View style={styles.errorContainer}>
        <Text variant="titleMedium">Relatório não encontrado</Text>
        <Button mode="outlined" onPress={() => navigation.goBack()}>
          Voltar
        </Button>
      </View>
    );
  }

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {renderReportHeader()}

      <View style={styles.tabsContainer}>
        <SegmentedButtons
          value={selectedTab}
          onValueChange={setSelectedTab}
          buttons={[
            {
              value: 'summary',
              label: 'Resumo',
              icon: 'summarize',
            },
            {
              value: 'details',
              label: 'Detalhes',
              icon: 'list',
            },
            {
              value: 'charts',
              label: 'Gráficos',
              icon: 'bar-chart',
            },
          ]}
          style={styles.segmentedButtons}
        />
      </View>

      {renderTabContent()}

      {/* Ações */}
      {isCompletedReport() && (
        <View style={styles.actions}>
          <Button
            mode="outlined"
            onPress={handleShare}
            style={styles.actionButton}
            icon="share"
          >
            Compartilhar
          </Button>
          
          <Button
            mode="contained"
            onPress={handleDownload}
            style={styles.actionButton}
            icon="download"
          >
            Baixar PDF
          </Button>
        </View>
      )}
      
      <View style={styles.bottomActions}>
        <Button
          mode="text"
          onPress={handleRegenerate}
          style={styles.actionButton}
          icon="refresh"
        >
          Regenerar Relatório
        </Button>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: spacing.md,
    opacity: 0.7,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.lg,
  },
  headerCard: {
    margin: spacing.md,
    marginBottom: spacing.sm,
  },
  headerContent: {
    padding: spacing.lg,
  },
  statusContainer: {
    alignItems: 'flex-end',
    marginBottom: spacing.lg,
  },
  statusChip: {
    height: 28,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  categoryIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  titleText: {
    flex: 1,
  },
  title: {
    fontWeight: 'bold',
    marginBottom: spacing.sm,
  },
  categoryContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: spacing.sm,
  },
  categoryName: {
    fontWeight: '500',
  },
  description: {
    opacity: 0.8,
    lineHeight: 20,
    marginBottom: spacing.lg,
  },
  reportInfo: {
    gap: spacing.sm,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  infoText: {
    marginLeft: spacing.sm,
    opacity: 0.8,
  },
  tabsContainer: {
    padding: spacing.md,
    paddingBottom: spacing.sm,
  },
  segmentedButtons: {
    marginBottom: spacing.sm,
  },
  card: {
    margin: spacing.md,
    marginTop: spacing.sm,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: spacing.sm,
  },
  divider: {
    marginBottom: spacing.md,
  },
  subsectionTitle: {
    fontWeight: '500',
    marginBottom: spacing.md,
    opacity: 0.8,
  },
  metricsContainer: {
    marginBottom: spacing.lg,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.md,
  },
  metricCard: {
    flex: 1,
    minWidth: '45%',
    padding: spacing.md,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    alignItems: 'center',
  },
  metricLabel: {
    opacity: 0.7,
    marginBottom: spacing.sm,
    textAlign: 'center',
  },
  metricValue: {
    fontWeight: 'bold',
    marginBottom: spacing.sm,
    textAlign: 'center',
  },
  changeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  changeText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: spacing.xs,
  },
  highlightsContainer: {
    marginBottom: spacing.lg,
  },
  highlightItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: spacing.md,
  },
  highlightText: {
    marginLeft: spacing.sm,
    flex: 1,
    lineHeight: 20,
  },
  insightsContainer: {
    marginBottom: spacing.lg,
  },
  insightItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: spacing.md,
  },
  insightText: {
    marginLeft: spacing.sm,
    flex: 1,
    lineHeight: 20,
  },
  recordsCount: {
    marginBottom: spacing.lg,
    opacity: 0.7,
    fontStyle: 'italic',
  },
  detailsList: {
    gap: spacing.md,
  },
  detailItem: {
    padding: spacing.md,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
  },
  detailHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  detailName: {
    fontWeight: '600',
  },
  detailCategory: {
    opacity: 0.7,
    marginBottom: spacing.sm,
  },
  detailMetrics: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.md,
  },
  detailMetric: {
    opacity: 0.8,
  },
  moreDetails: {
    textAlign: 'center',
    opacity: 0.6,
    fontStyle: 'italic',
    marginTop: spacing.md,
  },
  emptyMessage: {
    textAlign: 'center',
    opacity: 0.6,
    fontStyle: 'italic',
    paddingVertical: spacing.lg,
  },
  actions: {
    flexDirection: 'row',
    padding: spacing.md,
    gap: spacing.md,
  },
  bottomActions: {
    padding: spacing.md,
    paddingBottom: spacing.xl,
  },
  actionButton: {
    flex: 1,
    marginTop: spacing.sm,
  },
});
