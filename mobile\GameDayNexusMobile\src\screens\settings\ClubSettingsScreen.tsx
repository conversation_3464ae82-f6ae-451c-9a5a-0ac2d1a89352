import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
  Image,
  TouchableOpacity,
} from 'react-native';
import {
  Text,
  Card,
  TextInput,
  Button,
  useTheme,
  ActivityIndicator,
  Chip,
  Divider,
  Avatar,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { spacing } from '@/theme';
import { ClubSettings } from '@/types/settings';

interface ClubSettingsScreenProps {
  route: {
    params?: {
      clubId?: string;
    };
  };
  navigation: any;
}

export default function ClubSettingsScreen({ route, navigation }: ClubSettingsScreenProps) {
  const theme = useTheme();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [clubSettings, setClubSettings] = useState<ClubSettings | null>(null);

  // Dados mockados para demonstração
  const mockClubSettings: ClubSettings = {
    id: '1',
    club_id: 'club1',
    name: 'FC Exemplo',
    full_name: 'Futebol Clube Exemplo',
    founded_year: 1995,
    logo_url: 'https://via.placeholder.com/150',
    colors: {
      primary: '#2196f3',
      secondary: '#ffffff',
      accent: '#ff9800',
      home_kit: { shirt: '#2196f3', shorts: '#ffffff', socks: '#2196f3' },
      away_kit: { shirt: '#ffffff', shorts: '#2196f3', socks: '#ffffff' },
    },
    address: {
      street: 'Rua do Futebol',
      number: '123',
      complement: 'Centro Esportivo',
      neighborhood: 'Vila Olímpica',
      city: 'São Paulo',
      state: 'SP',
      zip_code: '01234-567',
      country: 'Brasil',
    },
    contact: {
      phone: '(11) 99999-0000',
      email: '<EMAIL>',
      website: 'www.fcexemplo.com',
      whatsapp: '(11) 99999-0000',
    },
    social_media: {
      facebook: 'fcexemplo',
      instagram: '@fcexemplo',
      twitter: '@fcexemplo',
      youtube: 'FCExemplo',
    },
    preferences: {
      timezone: 'America/Sao_Paulo',
      language: 'pt-BR',
      currency: 'BRL',
      date_format: 'DD/MM/YYYY',
      time_format: '24h',
      week_start: 'monday',
      fiscal_year_start: 1,
      season_start: 2,
      auto_backup: true,
      backup_frequency: 'weekly',
      data_retention_days: 365,
    },
    subscription: {
      plan: 'premium',
      status: 'active',
      started_at: '2025-01-01',
      expires_at: '2025-12-31',
      features: {
        max_athletes: 100,
        max_users: 20,
        max_storage_gb: 50,
        advanced_reports: true,
        api_access: true,
        custom_branding: true,
        priority_support: true,
        backup_retention_days: 90,
      },
      limits: {
        current_athletes: 45,
        current_users: 8,
        current_storage_gb: 12.5,
      },
    },
    created_at: '2025-01-01',
    updated_at: '2025-08-24',
  };

  useEffect(() => {
    loadClubSettings();
  }, []);

  const loadClubSettings = async () => {
    setLoading(true);
    try {
      // Simular carregamento da API
      await new Promise(resolve => setTimeout(resolve, 1000));
      setClubSettings(mockClubSettings);
    } catch (error) {
      console.error('Erro ao carregar configurações do clube:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!clubSettings) return;

    setSaving(true);
    try {
      // Simular salvamento na API
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      Alert.alert(
        'Sucesso',
        'Configurações do clube salvas com sucesso!',
        [{ text: 'OK' }]
      );
    } catch (error) {
      Alert.alert(
        'Erro',
        'Erro ao salvar configurações. Tente novamente.',
        [{ text: 'OK' }]
      );
    } finally {
      setSaving(false);
    }
  };

  const handleLogoChange = () => {
    Alert.alert(
      'Alterar Logo',
      'Escolha uma opção:',
      [
        { text: 'Cancelar', style: 'cancel' },
        { text: 'Câmera', onPress: () => console.log('Abrir câmera') },
        { text: 'Galeria', onPress: () => console.log('Abrir galeria') },
      ]
    );
  };

  const handleColorChange = (colorType: string) => {
    // Implementar seletor de cores
    console.log('Alterar cor:', colorType);
  };

  const updateClubSettings = (field: string, value: any) => {
    if (!clubSettings) return;
    
    setClubSettings(prev => {
      if (!prev) return prev;
      
      if (field.includes('.')) {
        const [parent, child] = field.split('.');
        return {
          ...prev,
          [parent]: {
            ...prev[parent as keyof ClubSettings],
            [child]: value,
          },
        };
      }
      
      return {
        ...prev,
        [field]: value,
      };
    });
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text variant="bodyMedium" style={styles.loadingText}>
          Carregando configurações...
        </Text>
      </View>
    );
  }

  if (!clubSettings) {
    return (
      <View style={styles.errorContainer}>
        <Text variant="titleMedium">Erro ao carregar configurações</Text>
        <Button mode="outlined" onPress={() => navigation.goBack()}>
          Voltar
        </Button>
      </View>
    );
  }

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Logo e Informações Básicas */}
      <Card style={styles.card}>
        <Card.Content>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Informações Básicas
          </Text>
          
          <View style={styles.logoSection}>
            <TouchableOpacity onPress={handleLogoChange} style={styles.logoContainer}>
              {clubSettings.logo_url ? (
                <Image source={{ uri: clubSettings.logo_url }} style={styles.logo} />
              ) : (
                <Avatar.Icon size={100} icon="business" />
              )}
              <View style={styles.logoOverlay}>
                <Icon name="camera-alt" size={24} color="#ffffff" />
              </View>
            </TouchableOpacity>
            
            <View style={styles.logoInfo}>
              <Text variant="bodySmall" style={styles.logoLabel}>
                Logo do Clube
              </Text>
              <Text variant="bodySmall" style={styles.logoDescription}>
                Toque para alterar
              </Text>
            </View>
          </View>
          
          <TextInput
            label="Nome do Clube"
            value={clubSettings.name}
            onChangeText={(text) => updateClubSettings('name', text)}
            style={styles.input}
            mode="outlined"
          />
          
          <TextInput
            label="Nome Completo"
            value={clubSettings.full_name}
            onChangeText={(text) => updateClubSettings('full_name', text)}
            style={styles.input}
            mode="outlined"
          />
          
          <TextInput
            label="Ano de Fundação"
            value={clubSettings.founded_year?.toString() || ''}
            onChangeText={(text) => updateClubSettings('founded_year', parseInt(text) || undefined)}
            style={styles.input}
            mode="outlined"
            keyboardType="numeric"
          />
        </Card.Content>
      </Card>

      {/* Cores do Clube */}
      <Card style={styles.card}>
        <Card.Content>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Cores do Clube
          </Text>
          
          <View style={styles.colorsGrid}>
            <TouchableOpacity 
              style={styles.colorItem}
              onPress={() => handleColorChange('primary')}
            >
              <View style={[styles.colorCircle, { backgroundColor: clubSettings.colors.primary }]} />
              <Text variant="bodySmall" style={styles.colorLabel}>Primária</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.colorItem}
              onPress={() => handleColorChange('secondary')}
            >
              <View style={[styles.colorCircle, { backgroundColor: clubSettings.colors.secondary, borderWidth: 1, borderColor: '#e0e0e0' }]} />
              <Text variant="bodySmall" style={styles.colorLabel}>Secundária</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.colorItem}
              onPress={() => handleColorChange('accent')}
            >
              <View style={[styles.colorCircle, { backgroundColor: clubSettings.colors.accent }]} />
              <Text variant="bodySmall" style={styles.colorLabel}>Destaque</Text>
            </TouchableOpacity>
          </View>
          
          <Divider style={styles.divider} />
          
          <Text variant="bodyMedium" style={styles.subsectionTitle}>
            Uniformes
          </Text>
          
          <View style={styles.kitsContainer}>
            <View style={styles.kitItem}>
              <Text variant="bodySmall" style={styles.kitLabel}>Casa</Text>
              <View style={styles.kitColors}>
                <View style={[styles.kitColor, { backgroundColor: clubSettings.colors.home_kit.shirt }]} />
                <View style={[styles.kitColor, { backgroundColor: clubSettings.colors.home_kit.shorts }]} />
                <View style={[styles.kitColor, { backgroundColor: clubSettings.colors.home_kit.socks }]} />
              </View>
            </View>
            
            <View style={styles.kitItem}>
              <Text variant="bodySmall" style={styles.kitLabel}>Visitante</Text>
              <View style={styles.kitColors}>
                <View style={[styles.kitColor, { backgroundColor: clubSettings.colors.away_kit.shirt, borderWidth: 1, borderColor: '#e0e0e0' }]} />
                <View style={[styles.kitColor, { backgroundColor: clubSettings.colors.away_kit.shorts }]} />
                <View style={[styles.kitColor, { backgroundColor: clubSettings.colors.away_kit.socks, borderWidth: 1, borderColor: '#e0e0e0' }]} />
              </View>
            </View>
          </View>
        </Card.Content>
      </Card>

      {/* Contato */}
      <Card style={styles.card}>
        <Card.Content>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Informações de Contato
          </Text>
          
          <TextInput
            label="Telefone"
            value={clubSettings.contact.phone || ''}
            onChangeText={(text) => updateClubSettings('contact.phone', text)}
            style={styles.input}
            mode="outlined"
            keyboardType="phone-pad"
          />
          
          <TextInput
            label="E-mail"
            value={clubSettings.contact.email || ''}
            onChangeText={(text) => updateClubSettings('contact.email', text)}
            style={styles.input}
            mode="outlined"
            keyboardType="email-address"
          />
          
          <TextInput
            label="Website"
            value={clubSettings.contact.website || ''}
            onChangeText={(text) => updateClubSettings('contact.website', text)}
            style={styles.input}
            mode="outlined"
          />
          
          <TextInput
            label="WhatsApp"
            value={clubSettings.contact.whatsapp || ''}
            onChangeText={(text) => updateClubSettings('contact.whatsapp', text)}
            style={styles.input}
            mode="outlined"
            keyboardType="phone-pad"
          />
        </Card.Content>
      </Card>

      {/* Redes Sociais */}
      <Card style={styles.card}>
        <Card.Content>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Redes Sociais
          </Text>
          
          <TextInput
            label="Facebook"
            value={clubSettings.social_media?.facebook || ''}
            onChangeText={(text) => updateClubSettings('social_media.facebook', text)}
            style={styles.input}
            mode="outlined"
            left={<TextInput.Icon icon="facebook" />}
          />
          
          <TextInput
            label="Instagram"
            value={clubSettings.social_media?.instagram || ''}
            onChangeText={(text) => updateClubSettings('social_media.instagram', text)}
            style={styles.input}
            mode="outlined"
            left={<TextInput.Icon icon="instagram" />}
          />
          
          <TextInput
            label="Twitter"
            value={clubSettings.social_media?.twitter || ''}
            onChangeText={(text) => updateClubSettings('social_media.twitter', text)}
            style={styles.input}
            mode="outlined"
            left={<TextInput.Icon icon="twitter" />}
          />
          
          <TextInput
            label="YouTube"
            value={clubSettings.social_media?.youtube || ''}
            onChangeText={(text) => updateClubSettings('social_media.youtube', text)}
            style={styles.input}
            mode="outlined"
            left={<TextInput.Icon icon="youtube" />}
          />
        </Card.Content>
      </Card>

      {/* Endereço */}
      <Card style={styles.card}>
        <Card.Content>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Endereço
          </Text>
          
          <View style={styles.addressRow}>
            <TextInput
              label="Rua"
              value={clubSettings.address?.street || ''}
              onChangeText={(text) => updateClubSettings('address.street', text)}
              style={[styles.input, styles.addressStreet]}
              mode="outlined"
            />
            
            <TextInput
              label="Número"
              value={clubSettings.address?.number || ''}
              onChangeText={(text) => updateClubSettings('address.number', text)}
              style={[styles.input, styles.addressNumber]}
              mode="outlined"
            />
          </View>
          
          <TextInput
            label="Complemento"
            value={clubSettings.address?.complement || ''}
            onChangeText={(text) => updateClubSettings('address.complement', text)}
            style={styles.input}
            mode="outlined"
          />
          
          <TextInput
            label="Bairro"
            value={clubSettings.address?.neighborhood || ''}
            onChangeText={(text) => updateClubSettings('address.neighborhood', text)}
            style={styles.input}
            mode="outlined"
          />
          
          <View style={styles.addressRow}>
            <TextInput
              label="Cidade"
              value={clubSettings.address?.city || ''}
              onChangeText={(text) => updateClubSettings('address.city', text)}
              style={[styles.input, styles.addressCity]}
              mode="outlined"
            />
            
            <TextInput
              label="Estado"
              value={clubSettings.address?.state || ''}
              onChangeText={(text) => updateClubSettings('address.state', text)}
              style={[styles.input, styles.addressState]}
              mode="outlined"
            />
          </View>
          
          <TextInput
            label="CEP"
            value={clubSettings.address?.zip_code || ''}
            onChangeText={(text) => updateClubSettings('address.zip_code', text)}
            style={styles.input}
            mode="outlined"
            keyboardType="numeric"
          />
        </Card.Content>
      </Card>

      {/* Botões de Ação */}
      <View style={styles.actions}>
        <Button
          mode="outlined"
          onPress={() => navigation.goBack()}
          style={styles.actionButton}
          disabled={saving}
        >
          Cancelar
        </Button>
        
        <Button
          mode="contained"
          onPress={handleSave}
          style={styles.actionButton}
          loading={saving}
          disabled={saving}
        >
          Salvar Alterações
        </Button>
      </View>

      <View style={styles.bottomSpacing} />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: spacing.md,
    opacity: 0.7,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.lg,
  },
  card: {
    margin: spacing.md,
    marginBottom: spacing.sm,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: spacing.lg,
  },
  subsectionTitle: {
    fontWeight: '500',
    marginBottom: spacing.md,
    opacity: 0.8,
  },
  logoSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  logoContainer: {
    position: 'relative',
    marginRight: spacing.lg,
  },
  logo: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  logoOverlay: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(0,0,0,0.6)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoInfo: {
    flex: 1,
  },
  logoLabel: {
    fontWeight: '500',
    marginBottom: spacing.xs,
  },
  logoDescription: {
    opacity: 0.7,
  },
  input: {
    marginBottom: spacing.md,
  },
  colorsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: spacing.lg,
  },
  colorItem: {
    alignItems: 'center',
  },
  colorCircle: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginBottom: spacing.sm,
  },
  colorLabel: {
    opacity: 0.8,
  },
  divider: {
    marginVertical: spacing.lg,
  },
  kitsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  kitItem: {
    alignItems: 'center',
  },
  kitLabel: {
    marginBottom: spacing.sm,
    fontWeight: '500',
  },
  kitColors: {
    flexDirection: 'row',
    gap: spacing.xs,
  },
  kitColor: {
    width: 20,
    height: 20,
    borderRadius: 10,
  },
  addressRow: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  addressStreet: {
    flex: 3,
  },
  addressNumber: {
    flex: 1,
  },
  addressCity: {
    flex: 2,
  },
  addressState: {
    flex: 1,
  },
  actions: {
    flexDirection: 'row',
    padding: spacing.md,
    gap: spacing.md,
  },
  actionButton: {
    flex: 1,
  },
  bottomSpacing: {
    height: spacing.xl,
  },
});
