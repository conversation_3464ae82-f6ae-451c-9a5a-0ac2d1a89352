import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import {
  Text,
  Card,
  List,
  Switch,
  useTheme,
  ActivityIndicator,
  But<PERSON>,
  Divider,
  TimePicker,
} from 'react-native-paper';
import { spacing } from '@/theme';
import { NotificationSettings } from '@/types/settings';

interface NotificationSettingsScreenProps {
  navigation: any;
}

export default function NotificationSettingsScreen({ navigation }: NotificationSettingsScreenProps) {
  const theme = useTheme();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [settings, setSettings] = useState<NotificationSettings | null>(null);

  // Dados mockados para demonstração
  const mockSettings: NotificationSettings = {
    enabled: true,
    push_notifications: true,
    email_notifications: true,
    sms_notifications: false,
    sound_enabled: true,
    vibration_enabled: true,
    quiet_hours: {
      enabled: true,
      start_time: '22:00',
      end_time: '07:00',
    },
    categories: {
      matches: true,
      training: true,
      medical: true,
      financial: false,
      inventory: false,
      reports: true,
      system: true,
    },
  };

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    setLoading(true);
    try {
      // Simular carregamento da API
      await new Promise(resolve => setTimeout(resolve, 1000));
      setSettings(mockSettings);
    } catch (error) {
      console.error('Erro ao carregar configurações:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!settings) return;

    setSaving(true);
    try {
      // Simular salvamento na API
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      Alert.alert(
        'Sucesso',
        'Configurações de notificação salvas com sucesso!',
        [{ text: 'OK' }]
      );
    } catch (error) {
      Alert.alert(
        'Erro',
        'Erro ao salvar configurações. Tente novamente.',
        [{ text: 'OK' }]
      );
    } finally {
      setSaving(false);
    }
  };

  const updateSetting = (field: string, value: any) => {
    if (!settings) return;
    
    setSettings(prev => {
      if (!prev) return prev;
      
      if (field.includes('.')) {
        const [parent, child] = field.split('.');
        return {
          ...prev,
          [parent]: {
            ...prev[parent as keyof NotificationSettings],
            [child]: value,
          },
        };
      }
      
      return {
        ...prev,
        [field]: value,
      };
    });
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text variant="bodyMedium" style={styles.loadingText}>
          Carregando configurações...
        </Text>
      </View>
    );
  }

  if (!settings) {
    return (
      <View style={styles.errorContainer}>
        <Text variant="titleMedium">Erro ao carregar configurações</Text>
        <Button mode="outlined" onPress={() => navigation.goBack()}>
          Voltar
        </Button>
      </View>
    );
  }

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Configurações Gerais */}
      <Card style={styles.card}>
        <Card.Content>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Configurações Gerais
          </Text>
          
          <List.Item
            title="Notificações Ativadas"
            description="Ativar ou desativar todas as notificações"
            left={(props) => <List.Icon {...props} icon="notifications" />}
            right={() => (
              <Switch
                value={settings.enabled}
                onValueChange={(value) => updateSetting('enabled', value)}
              />
            )}
          />
          
          <List.Item
            title="Notificações Push"
            description="Receber notificações no dispositivo"
            left={(props) => <List.Icon {...props} icon="phone-android" />}
            right={() => (
              <Switch
                value={settings.push_notifications}
                onValueChange={(value) => updateSetting('push_notifications', value)}
                disabled={!settings.enabled}
              />
            )}
          />
          
          <List.Item
            title="Notificações por E-mail"
            description="Receber notificações por e-mail"
            left={(props) => <List.Icon {...props} icon="email" />}
            right={() => (
              <Switch
                value={settings.email_notifications}
                onValueChange={(value) => updateSetting('email_notifications', value)}
                disabled={!settings.enabled}
              />
            )}
          />
          
          <List.Item
            title="Notificações por SMS"
            description="Receber notificações por SMS"
            left={(props) => <List.Icon {...props} icon="sms" />}
            right={() => (
              <Switch
                value={settings.sms_notifications}
                onValueChange={(value) => updateSetting('sms_notifications', value)}
                disabled={!settings.enabled}
              />
            )}
          />
        </Card.Content>
      </Card>

      {/* Configurações de Som e Vibração */}
      <Card style={styles.card}>
        <Card.Content>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Som e Vibração
          </Text>
          
          <List.Item
            title="Som das Notificações"
            description="Reproduzir som ao receber notificações"
            left={(props) => <List.Icon {...props} icon="volume-up" />}
            right={() => (
              <Switch
                value={settings.sound_enabled}
                onValueChange={(value) => updateSetting('sound_enabled', value)}
                disabled={!settings.enabled || !settings.push_notifications}
              />
            )}
          />
          
          <List.Item
            title="Vibração"
            description="Vibrar ao receber notificações"
            left={(props) => <List.Icon {...props} icon="vibration" />}
            right={() => (
              <Switch
                value={settings.vibration_enabled}
                onValueChange={(value) => updateSetting('vibration_enabled', value)}
                disabled={!settings.enabled || !settings.push_notifications}
              />
            )}
          />
        </Card.Content>
      </Card>

      {/* Horário Silencioso */}
      <Card style={styles.card}>
        <Card.Content>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Horário Silencioso
          </Text>
          
          <List.Item
            title="Ativar Horário Silencioso"
            description="Não receber notificações em horários específicos"
            left={(props) => <List.Icon {...props} icon="do-not-disturb" />}
            right={() => (
              <Switch
                value={settings.quiet_hours.enabled}
                onValueChange={(value) => updateSetting('quiet_hours.enabled', value)}
                disabled={!settings.enabled}
              />
            )}
          />
          
          {settings.quiet_hours.enabled && (
            <>
              <Divider style={styles.divider} />
              
              <View style={styles.timeContainer}>
                <View style={styles.timeItem}>
                  <Text variant="bodyMedium" style={styles.timeLabel}>
                    Início
                  </Text>
                  <Text variant="titleMedium" style={styles.timeValue}>
                    {settings.quiet_hours.start_time}
                  </Text>
                </View>
                
                <View style={styles.timeItem}>
                  <Text variant="bodyMedium" style={styles.timeLabel}>
                    Fim
                  </Text>
                  <Text variant="titleMedium" style={styles.timeValue}>
                    {settings.quiet_hours.end_time}
                  </Text>
                </View>
              </View>
              
              <Text variant="bodySmall" style={styles.quietHoursNote}>
                Durante este período, você não receberá notificações push, mas ainda poderá ver as notificações no aplicativo.
              </Text>
            </>
          )}
        </Card.Content>
      </Card>

      {/* Categorias de Notificação */}
      <Card style={styles.card}>
        <Card.Content>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Categorias de Notificação
          </Text>
          
          <Text variant="bodySmall" style={styles.categoryDescription}>
            Escolha quais tipos de notificação você deseja receber:
          </Text>
          
          <List.Item
            title="Partidas"
            description="Lembretes de jogos, resultados e convocações"
            left={(props) => <List.Icon {...props} icon="sports-soccer" />}
            right={() => (
              <Switch
                value={settings.categories.matches}
                onValueChange={(value) => updateSetting('categories.matches', value)}
                disabled={!settings.enabled}
              />
            )}
          />
          
          <List.Item
            title="Treinamentos"
            description="Horários de treino e atividades"
            left={(props) => <List.Icon {...props} icon="fitness-center" />}
            right={() => (
              <Switch
                value={settings.categories.training}
                onValueChange={(value) => updateSetting('categories.training', value)}
                disabled={!settings.enabled}
              />
            )}
          />
          
          <List.Item
            title="Médico"
            description="Consultas, exames e lembretes de saúde"
            left={(props) => <List.Icon {...props} icon="medical-services" />}
            right={() => (
              <Switch
                value={settings.categories.medical}
                onValueChange={(value) => updateSetting('categories.medical', value)}
                disabled={!settings.enabled}
              />
            )}
          />
          
          <List.Item
            title="Financeiro"
            description="Pagamentos, mensalidades e transações"
            left={(props) => <List.Icon {...props} icon="attach-money" />}
            right={() => (
              <Switch
                value={settings.categories.financial}
                onValueChange={(value) => updateSetting('categories.financial', value)}
                disabled={!settings.enabled}
              />
            )}
          />
          
          <List.Item
            title="Inventário"
            description="Estoque baixo e movimentações de material"
            left={(props) => <List.Icon {...props} icon="inventory" />}
            right={() => (
              <Switch
                value={settings.categories.inventory}
                onValueChange={(value) => updateSetting('categories.inventory', value)}
                disabled={!settings.enabled}
              />
            )}
          />
          
          <List.Item
            title="Relatórios"
            description="Relatórios prontos e análises"
            left={(props) => <List.Icon {...props} icon="assessment" />}
            right={() => (
              <Switch
                value={settings.categories.reports}
                onValueChange={(value) => updateSetting('categories.reports', value)}
                disabled={!settings.enabled}
              />
            )}
          />
          
          <List.Item
            title="Sistema"
            description="Atualizações do app e manutenções"
            left={(props) => <List.Icon {...props} icon="settings" />}
            right={() => (
              <Switch
                value={settings.categories.system}
                onValueChange={(value) => updateSetting('categories.system', value)}
                disabled={!settings.enabled}
              />
            )}
          />
        </Card.Content>
      </Card>

      {/* Botões de Ação */}
      <View style={styles.actions}>
        <Button
          mode="outlined"
          onPress={() => navigation.goBack()}
          style={styles.actionButton}
          disabled={saving}
        >
          Cancelar
        </Button>
        
        <Button
          mode="contained"
          onPress={handleSave}
          style={styles.actionButton}
          loading={saving}
          disabled={saving}
        >
          Salvar
        </Button>
      </View>

      <View style={styles.bottomSpacing} />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: spacing.md,
    opacity: 0.7,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.lg,
  },
  card: {
    margin: spacing.md,
    marginBottom: spacing.sm,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: spacing.lg,
  },
  categoryDescription: {
    opacity: 0.7,
    marginBottom: spacing.lg,
    lineHeight: 18,
  },
  divider: {
    marginVertical: spacing.md,
  },
  timeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginVertical: spacing.lg,
  },
  timeItem: {
    alignItems: 'center',
  },
  timeLabel: {
    opacity: 0.7,
    marginBottom: spacing.sm,
  },
  timeValue: {
    fontWeight: '600',
  },
  quietHoursNote: {
    opacity: 0.6,
    fontStyle: 'italic',
    lineHeight: 16,
    textAlign: 'center',
    marginTop: spacing.md,
  },
  actions: {
    flexDirection: 'row',
    padding: spacing.md,
    gap: spacing.md,
  },
  actionButton: {
    flex: 1,
  },
  bottomSpacing: {
    height: spacing.xl,
  },
});
