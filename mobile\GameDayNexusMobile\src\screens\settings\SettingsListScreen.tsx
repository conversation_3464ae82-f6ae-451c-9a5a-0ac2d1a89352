import React, { useState, useCallback } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import {
  Text,
  Card,
  List,
  Switch,
  useTheme,
  ActivityIndicator,
  Chip,
  Button,
  Divider,
} from 'react-native-paper';
import { useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { spacing } from '@/theme';
import { 
  ClubSettings, 
  AppSettings, 
  SystemInfo,
  getSubscriptionPlanLabel,
  getSubscriptionStatusLabel,
  getSubscriptionStatusColor,
  getThemeLabel,
  getSyncStatusLabel,
  getSyncStatusColor,
  formatStorageSize
} from '@/types/settings';

interface SettingsListScreenProps {
  navigation: any;
}

export default function SettingsListScreen({ navigation }: SettingsListScreenProps) {
  const theme = useTheme();
  const [loading, setLoading] = useState(false);
  const [clubSettings, setClubSettings] = useState<ClubSettings | null>(null);
  const [appSettings, setAppSettings] = useState<AppSettings | null>(null);
  const [systemInfo, setSystemInfo] = useState<SystemInfo | null>(null);

  // Dados mockados para demonstração
  const mockClubSettings: ClubSettings = {
    id: '1',
    club_id: 'club1',
    name: 'FC Exemplo',
    full_name: 'Futebol Clube Exemplo',
    founded_year: 1995,
    logo_url: 'https://via.placeholder.com/100',
    colors: {
      primary: '#2196f3',
      secondary: '#ffffff',
      accent: '#ff9800',
      home_kit: { shirt: '#2196f3', shorts: '#ffffff', socks: '#2196f3' },
      away_kit: { shirt: '#ffffff', shorts: '#2196f3', socks: '#ffffff' },
    },
    contact: {
      phone: '(11) 99999-0000',
      email: '<EMAIL>',
      website: 'www.fcexemplo.com',
    },
    preferences: {
      timezone: 'America/Sao_Paulo',
      language: 'pt-BR',
      currency: 'BRL',
      date_format: 'DD/MM/YYYY',
      time_format: '24h',
      week_start: 'monday',
      fiscal_year_start: 1,
      season_start: 2,
      auto_backup: true,
      backup_frequency: 'weekly',
      data_retention_days: 365,
    },
    subscription: {
      plan: 'premium',
      status: 'active',
      started_at: '2025-01-01',
      expires_at: '2025-12-31',
      features: {
        max_athletes: 100,
        max_users: 20,
        max_storage_gb: 50,
        advanced_reports: true,
        api_access: true,
        custom_branding: true,
        priority_support: true,
        backup_retention_days: 90,
      },
      limits: {
        current_athletes: 45,
        current_users: 8,
        current_storage_gb: 12.5,
      },
    },
    created_at: '2025-01-01',
    updated_at: '2025-08-24',
  };

  const mockAppSettings: AppSettings = {
    user_id: 'user1',
    theme: 'auto',
    language: 'pt-BR',
    notifications: {
      enabled: true,
      push_notifications: true,
      email_notifications: true,
      sms_notifications: false,
      sound_enabled: true,
      vibration_enabled: true,
      quiet_hours: {
        enabled: true,
        start_time: '22:00',
        end_time: '07:00',
      },
      categories: {
        matches: true,
        training: true,
        medical: true,
        financial: false,
        inventory: false,
        reports: true,
        system: true,
      },
    },
    privacy: {
      analytics_enabled: true,
      crash_reporting_enabled: true,
      location_sharing_enabled: false,
      contact_sync_enabled: true,
      data_sharing_enabled: false,
    },
    accessibility: {
      font_size: 'medium',
      high_contrast: false,
      reduce_motion: false,
      screen_reader_enabled: false,
      voice_commands_enabled: false,
    },
    performance: {
      image_quality: 'high',
      animation_enabled: true,
      auto_sync_enabled: true,
      cache_size_mb: 500,
      offline_mode_enabled: true,
    },
    sync: {
      auto_sync: true,
      sync_frequency: 'every_15min',
      wifi_only: false,
      background_sync: true,
      last_sync: '2025-08-24T14:30:00Z',
      sync_status: 'synced',
    },
    created_at: '2025-01-01',
    updated_at: '2025-08-24',
  };

  const mockSystemInfo: SystemInfo = {
    app_version: '1.0.0',
    build_number: '100',
    platform: 'android',
    os_version: '14',
    device_model: 'Samsung Galaxy S24',
    device_id: 'device123',
    install_date: '2025-01-01',
    last_update: '2025-08-20',
    storage_used_mb: 250,
    storage_available_mb: 15750,
  };

  useFocusEffect(
    useCallback(() => {
      loadSettings();
    }, [])
  );

  const loadSettings = async () => {
    setLoading(true);
    try {
      // Simular carregamento da API
      await new Promise(resolve => setTimeout(resolve, 1000));
      setClubSettings(mockClubSettings);
      setAppSettings(mockAppSettings);
      setSystemInfo(mockSystemInfo);
    } catch (error) {
      console.error('Erro ao carregar configurações:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleClubSettings = () => {
    navigation.navigate('ClubSettings');
  };

  const handleNotifications = () => {
    navigation.navigate('NotificationSettings');
  };

  const handlePrivacy = () => {
    navigation.navigate('PrivacySettings');
  };

  const handleSecurity = () => {
    navigation.navigate('SecuritySettings');
  };

  const handleBackup = () => {
    navigation.navigate('BackupSettings');
  };

  const handleAbout = () => {
    navigation.navigate('AboutScreen');
  };

  const handleSync = () => {
    Alert.alert(
      'Sincronizar Dados',
      'Deseja sincronizar todos os dados agora?',
      [
        { text: 'Cancelar', style: 'cancel' },
        { text: 'Sincronizar', onPress: () => console.log('Sincronizando...') },
      ]
    );
  };

  const handleThemeToggle = (value: boolean) => {
    // Implementar lógica para alternar tema
    console.log('Alternar tema:', value);
  };

  const renderClubInfo = () => {
    if (!clubSettings) return null;

    return (
      <Card style={styles.card}>
        <Card.Content style={styles.cardContent}>
          <View style={styles.clubHeader}>
            <View style={styles.clubInfo}>
              <Text variant="titleLarge" style={styles.clubName}>
                {clubSettings.name}
              </Text>
              <Text variant="bodyMedium" style={styles.clubFullName}>
                {clubSettings.full_name}
              </Text>
              {clubSettings.founded_year && (
                <Text variant="bodySmall" style={styles.foundedYear}>
                  Fundado em {clubSettings.founded_year}
                </Text>
              )}
            </View>
            
            <View style={styles.subscriptionInfo}>
              <Chip
                style={[
                  styles.subscriptionChip,
                  { backgroundColor: `${getSubscriptionStatusColor(clubSettings.subscription.status)}20` }
                ]}
                textStyle={[
                  styles.subscriptionText,
                  { color: getSubscriptionStatusColor(clubSettings.subscription.status) }
                ]}
                compact
              >
                {getSubscriptionPlanLabel(clubSettings.subscription.plan)}
              </Chip>
            </View>
          </View>
          
          <Divider style={styles.divider} />
          
          <View style={styles.usageStats}>
            <View style={styles.statItem}>
              <Text variant="bodySmall" style={styles.statLabel}>Atletas</Text>
              <Text variant="titleSmall" style={styles.statValue}>
                {clubSettings.subscription.limits.current_athletes}/{clubSettings.subscription.features.max_athletes}
              </Text>
            </View>
            
            <View style={styles.statItem}>
              <Text variant="bodySmall" style={styles.statLabel}>Usuários</Text>
              <Text variant="titleSmall" style={styles.statValue}>
                {clubSettings.subscription.limits.current_users}/{clubSettings.subscription.features.max_users}
              </Text>
            </View>
            
            <View style={styles.statItem}>
              <Text variant="bodySmall" style={styles.statLabel}>Armazenamento</Text>
              <Text variant="titleSmall" style={styles.statValue}>
                {formatStorageSize(clubSettings.subscription.limits.current_storage_gb * 1024)}/
                {formatStorageSize(clubSettings.subscription.features.max_storage_gb * 1024)}
              </Text>
            </View>
          </View>
        </Card.Content>
      </Card>
    );
  };

  const renderSyncStatus = () => {
    if (!appSettings) return null;

    return (
      <Card style={styles.syncCard}>
        <Card.Content style={styles.syncContent}>
          <View style={styles.syncHeader}>
            <Icon 
              name="sync" 
              size={24} 
              color={getSyncStatusColor(appSettings.sync.sync_status)} 
            />
            <View style={styles.syncInfo}>
              <Text variant="titleSmall" style={styles.syncTitle}>
                Sincronização
              </Text>
              <Text variant="bodySmall" style={styles.syncSubtitle}>
                {getSyncStatusLabel(appSettings.sync.sync_status)}
              </Text>
            </View>
            
            <Button
              mode="outlined"
              onPress={handleSync}
              compact
              disabled={appSettings.sync.sync_status === 'syncing'}
            >
              Sincronizar
            </Button>
          </View>
          
          {appSettings.sync.last_sync && (
            <Text variant="bodySmall" style={styles.lastSync}>
              Última sincronização: {new Date(appSettings.sync.last_sync).toLocaleString('pt-BR')}
            </Text>
          )}
        </Card.Content>
      </Card>
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text variant="bodyMedium" style={styles.loadingText}>
          Carregando configurações...
        </Text>
      </View>
    );
  }

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {renderClubInfo()}
      {renderSyncStatus()}

      {/* Configurações do Clube */}
      <Card style={styles.card}>
        <Card.Content>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Configurações do Clube
          </Text>
          
          <List.Item
            title="Informações do Clube"
            description="Nome, logo, cores e contatos"
            left={(props) => <List.Icon {...props} icon="business" />}
            right={(props) => <List.Icon {...props} icon="chevron-right" />}
            onPress={handleClubSettings}
          />
          
          <List.Item
            title="Assinatura e Planos"
            description={`Plano ${getSubscriptionPlanLabel(clubSettings?.subscription.plan || 'free')}`}
            left={(props) => <List.Icon {...props} icon="card-membership" />}
            right={(props) => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => navigation.navigate('SubscriptionSettings')}
          />
        </Card.Content>
      </Card>

      {/* Configurações do App */}
      <Card style={styles.card}>
        <Card.Content>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Configurações do Aplicativo
          </Text>
          
          <List.Item
            title="Tema"
            description={`Aparência ${getThemeLabel(appSettings?.theme || 'auto')}`}
            left={(props) => <List.Icon {...props} icon="palette" />}
            right={() => (
              <Switch
                value={appSettings?.theme === 'dark'}
                onValueChange={handleThemeToggle}
              />
            )}
          />
          
          <List.Item
            title="Notificações"
            description="Alertas e lembretes"
            left={(props) => <List.Icon {...props} icon="notifications" />}
            right={(props) => <List.Icon {...props} icon="chevron-right" />}
            onPress={handleNotifications}
          />
          
          <List.Item
            title="Idioma e Região"
            description="Português (Brasil)"
            left={(props) => <List.Icon {...props} icon="language" />}
            right={(props) => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => navigation.navigate('LanguageSettings')}
          />
        </Card.Content>
      </Card>

      {/* Dados e Privacidade */}
      <Card style={styles.card}>
        <Card.Content>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Dados e Privacidade
          </Text>
          
          <List.Item
            title="Backup e Sincronização"
            description="Backup automático ativo"
            left={(props) => <List.Icon {...props} icon="backup" />}
            right={(props) => <List.Icon {...props} icon="chevron-right" />}
            onPress={handleBackup}
          />
          
          <List.Item
            title="Privacidade"
            description="Controle de dados pessoais"
            left={(props) => <List.Icon {...props} icon="privacy-tip" />}
            right={(props) => <List.Icon {...props} icon="chevron-right" />}
            onPress={handlePrivacy}
          />
          
          <List.Item
            title="Segurança"
            description="Autenticação e proteção"
            left={(props) => <List.Icon {...props} icon="security" />}
            right={(props) => <List.Icon {...props} icon="chevron-right" />}
            onPress={handleSecurity}
          />
        </Card.Content>
      </Card>

      {/* Suporte e Informações */}
      <Card style={styles.card}>
        <Card.Content>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Suporte e Informações
          </Text>
          
          <List.Item
            title="Central de Ajuda"
            description="Tutoriais e FAQ"
            left={(props) => <List.Icon {...props} icon="help" />}
            right={(props) => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => navigation.navigate('HelpCenter')}
          />
          
          <List.Item
            title="Contatar Suporte"
            description="Fale conosco"
            left={(props) => <List.Icon {...props} icon="support-agent" />}
            right={(props) => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => navigation.navigate('ContactSupport')}
          />
          
          <List.Item
            title="Sobre o App"
            description={`Versão ${systemInfo?.app_version || '1.0.0'}`}
            left={(props) => <List.Icon {...props} icon="info" />}
            right={(props) => <List.Icon {...props} icon="chevron-right" />}
            onPress={handleAbout}
          />
        </Card.Content>
      </Card>

      <View style={styles.bottomSpacing} />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: spacing.md,
    opacity: 0.7,
  },
  card: {
    margin: spacing.md,
    marginBottom: spacing.sm,
  },
  cardContent: {
    padding: spacing.lg,
  },
  clubHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.md,
  },
  clubInfo: {
    flex: 1,
  },
  clubName: {
    fontWeight: 'bold',
    marginBottom: spacing.xs,
  },
  clubFullName: {
    opacity: 0.8,
    marginBottom: spacing.xs,
  },
  foundedYear: {
    opacity: 0.6,
  },
  subscriptionInfo: {
    alignItems: 'flex-end',
  },
  subscriptionChip: {
    height: 28,
  },
  subscriptionText: {
    fontSize: 12,
    fontWeight: '600',
  },
  divider: {
    marginVertical: spacing.md,
  },
  usageStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statLabel: {
    opacity: 0.7,
    marginBottom: spacing.xs,
  },
  statValue: {
    fontWeight: '600',
  },
  syncCard: {
    margin: spacing.md,
    marginBottom: spacing.sm,
  },
  syncContent: {
    padding: spacing.md,
  },
  syncHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  syncInfo: {
    flex: 1,
    marginLeft: spacing.md,
  },
  syncTitle: {
    fontWeight: '600',
  },
  syncSubtitle: {
    opacity: 0.7,
  },
  lastSync: {
    opacity: 0.6,
    fontSize: 11,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: spacing.md,
  },
  bottomSpacing: {
    height: spacing.xl,
  },
});
