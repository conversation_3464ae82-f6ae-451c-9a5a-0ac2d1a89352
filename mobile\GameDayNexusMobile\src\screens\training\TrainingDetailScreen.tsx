import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
} from 'react-native';
import {
  Text,
  Card,
  Chip,
  Button,
  useTheme,
  ActivityIndicator,
  Divider,
  SegmentedButtons,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { spacing } from '@/theme';
import { 
  Training, 
  getTrainingTypeLabel, 
  getTrainingTypeColor,
  getTrainingStatusLabel,
  getTrainingStatusColor 
} from '@/types/training';

interface TrainingDetailScreenProps {
  route: {
    params: {
      trainingId: string;
    };
  };
  navigation: any;
}

export default function TrainingDetailScreen({ route, navigation }: TrainingDetailScreenProps) {
  const theme = useTheme();
  const { trainingId } = route.params;
  const [training, setTraining] = useState<Training | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedTab, setSelectedTab] = useState('info');

  // Mock data - em produção viria da API
  const mockTraining: Training = {
    id: trainingId,
    club_id: 'club1',
    title: 'Treino Técnico - Sub-20',
    description: 'Foco em passes curtos, controle de bola e finalização. Trabalho específico para melhorar a precisão dos passes e a primeira recepção.',
    date: '2025-08-25',
    start_time: '15:00',
    end_time: '17:00',
    location: 'Campo Principal',
    category_id: 'cat1',
    category_name: 'Sub-20',
    coach_name: 'Carlos Silva',
    type: 'technical',
    status: 'scheduled',
    objectives: ['Melhorar passe', 'Controle de bola', 'Finalização', 'Primeira recepção'],
    notes: 'Atenção especial aos jogadores que retornaram de lesão. Intensidade moderada.',
    created_at: '2025-01-01',
    updated_at: '2025-01-01',
  };

  useEffect(() => {
    loadTraining();
  }, [trainingId]);

  const loadTraining = async () => {
    setLoading(true);
    try {
      // Simular carregamento da API
      await new Promise(resolve => setTimeout(resolve, 1000));
      setTraining(mockTraining);
    } catch (error) {
      console.error('Erro ao carregar treinamento:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatTrainingDate = () => {
    if (!training) return '';
    try {
      const trainingDate = new Date(training.date);
      return format(trainingDate, "EEEE, dd 'de' MMMM 'de' yyyy", { locale: ptBR });
    } catch {
      return training.date;
    }
  };

  const getTypeColor = () => {
    if (!training) return theme.colors.outline;
    return getTrainingTypeColor(training.type);
  };

  const getStatusColor = () => {
    if (!training) return theme.colors.outline;
    return getTrainingStatusColor(training.status);
  };

  const isLiveTraining = () => {
    return training?.status === 'in_progress';
  };

  const isCompletedTraining = () => {
    return training?.status === 'completed';
  };

  const getDuration = () => {
    if (!training) return '';
    try {
      const start = new Date(`2000-01-01 ${training.start_time}`);
      const end = new Date(`2000-01-01 ${training.end_time}`);
      const diffMs = end.getTime() - start.getTime();
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
      const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
      
      if (diffHours > 0) {
        return `${diffHours}h${diffMinutes > 0 ? ` ${diffMinutes}min` : ''}`;
      }
      return `${diffMinutes}min`;
    } catch {
      return '';
    }
  };

  const handleEdit = () => {
    navigation.navigate('TrainingForm', { trainingId: training?.id });
  };

  const handleAttendance = () => {
    navigation.navigate('TrainingAttendance', { trainingId: training?.id });
  };

  const handleExercises = () => {
    navigation.navigate('TrainingExercises', { trainingId: training?.id });
  };

  const renderTrainingHeader = () => {
    if (!training) return null;

    return (
      <Card style={styles.headerCard}>
        <Card.Content style={styles.headerContent}>
          <View style={styles.statusContainer}>
            <Chip
              style={[styles.statusChip, { backgroundColor: `${getStatusColor()}20` }]}
              textStyle={[styles.statusText, { color: getStatusColor() }]}
              icon={isLiveTraining() ? 'circle' : undefined}
            >
              {getTrainingStatusLabel(training.status)}
            </Chip>
            <Text variant="bodyMedium" style={styles.category}>
              {training.category_name}
            </Text>
          </View>

          <View style={styles.titleContainer}>
            <View style={[styles.typeIcon, { backgroundColor: `${getTypeColor()}20` }]}>
              <Icon 
                name="sports-soccer" 
                size={32} 
                color={getTypeColor()} 
              />
            </View>
            
            <View style={styles.titleText}>
              <Text variant="headlineSmall" style={styles.title}>
                {training.title}
              </Text>
              
              <Chip
                style={[styles.typeChip, { backgroundColor: `${getTypeColor()}20` }]}
                textStyle={[styles.typeText, { color: getTypeColor() }]}
                compact
              >
                {getTrainingTypeLabel(training.type)}
              </Chip>
            </View>
          </View>

          {training.description && (
            <Text variant="bodyMedium" style={styles.description}>
              {training.description}
            </Text>
          )}

          <View style={styles.trainingDetails}>
            <View style={styles.detailItem}>
              <Icon name="event" size={20} color={theme.colors.outline} />
              <Text variant="bodyMedium" style={styles.detailText}>
                {formatTrainingDate()}
              </Text>
            </View>
            
            <View style={styles.detailItem}>
              <Icon name="access-time" size={20} color={theme.colors.outline} />
              <Text variant="bodyMedium" style={styles.detailText}>
                {training.start_time} - {training.end_time} ({getDuration()})
              </Text>
            </View>
            
            <View style={styles.detailItem}>
              <Icon name="location-on" size={20} color={theme.colors.outline} />
              <Text variant="bodyMedium" style={styles.detailText}>
                {training.location}
              </Text>
            </View>
            
            {training.coach_name && (
              <View style={styles.detailItem}>
                <Icon name="person" size={20} color={theme.colors.outline} />
                <Text variant="bodyMedium" style={styles.detailText}>
                  {training.coach_name}
                </Text>
              </View>
            )}
          </View>
        </Card.Content>
      </Card>
    );
  };

  const renderTabContent = () => {
    switch (selectedTab) {
      case 'info':
        return (
          <Card style={styles.card}>
            <Card.Content>
              <Text variant="titleMedium" style={styles.sectionTitle}>
                Informações do Treinamento
              </Text>
              <Divider style={styles.divider} />
              
              {training?.objectives && training.objectives.length > 0 && (
                <View style={styles.infoSection}>
                  <Text variant="bodySmall" style={styles.infoLabel}>Objetivos</Text>
                  <View style={styles.objectivesList}>
                    {training.objectives.map((objective, index) => (
                      <Chip
                        key={index}
                        style={styles.objectiveChip}
                        textStyle={styles.objectiveText}
                        compact
                      >
                        {objective}
                      </Chip>
                    ))}
                  </View>
                </View>
              )}
              
              {training?.notes && (
                <View style={styles.infoSection}>
                  <Text variant="bodySmall" style={styles.infoLabel}>Observações</Text>
                  <Text variant="bodyMedium">{training.notes}</Text>
                </View>
              )}
            </Card.Content>
          </Card>
        );
      
      case 'attendance':
        return (
          <Card style={styles.card}>
            <Card.Content>
              <Text variant="titleMedium" style={styles.sectionTitle}>
                Lista de Presença
              </Text>
              <Divider style={styles.divider} />
              
              <Text variant="bodyMedium" style={styles.emptyMessage}>
                Lista de presença ainda não registrada
              </Text>
              
              <Button
                mode="contained"
                onPress={handleAttendance}
                style={styles.actionButton}
                icon="check-circle"
              >
                Registrar Presença
              </Button>
            </Card.Content>
          </Card>
        );
      
      case 'exercises':
        return (
          <Card style={styles.card}>
            <Card.Content>
              <Text variant="titleMedium" style={styles.sectionTitle}>
                Exercícios
              </Text>
              <Divider style={styles.divider} />
              
              <Text variant="bodyMedium" style={styles.emptyMessage}>
                Nenhum exercício cadastrado
              </Text>
              
              <Button
                mode="contained"
                onPress={handleExercises}
                style={styles.actionButton}
                icon="fitness-center"
              >
                Adicionar Exercícios
              </Button>
            </Card.Content>
          </Card>
        );
      
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text variant="bodyMedium" style={styles.loadingText}>
          Carregando treinamento...
        </Text>
      </View>
    );
  }

  if (!training) {
    return (
      <View style={styles.errorContainer}>
        <Text variant="titleMedium">Treinamento não encontrado</Text>
        <Button mode="outlined" onPress={() => navigation.goBack()}>
          Voltar
        </Button>
      </View>
    );
  }

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {renderTrainingHeader()}

      <View style={styles.tabsContainer}>
        <SegmentedButtons
          value={selectedTab}
          onValueChange={setSelectedTab}
          buttons={[
            {
              value: 'info',
              label: 'Info',
              icon: 'info',
            },
            {
              value: 'attendance',
              label: 'Presença',
              icon: 'people',
            },
            {
              value: 'exercises',
              label: 'Exercícios',
              icon: 'fitness-center',
            },
          ]}
          style={styles.segmentedButtons}
        />
      </View>

      {renderTabContent()}

      {/* Ações */}
      <View style={styles.actions}>
        <Button
          mode="outlined"
          onPress={handleAttendance}
          style={styles.actionButton}
          icon="check-circle"
        >
          Lista de Presença
        </Button>
        
        <Button
          mode="contained"
          onPress={handleEdit}
          style={styles.actionButton}
          icon="edit"
        >
          Editar Treinamento
        </Button>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: spacing.md,
    opacity: 0.7,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.lg,
  },
  headerCard: {
    margin: spacing.md,
    marginBottom: spacing.sm,
  },
  headerContent: {
    padding: spacing.lg,
  },
  statusContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  statusChip: {
    height: 28,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  category: {
    opacity: 0.7,
    fontStyle: 'italic',
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  typeIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  titleText: {
    flex: 1,
  },
  title: {
    fontWeight: 'bold',
    marginBottom: spacing.sm,
  },
  typeChip: {
    height: 28,
    alignSelf: 'flex-start',
  },
  typeText: {
    fontSize: 12,
    fontWeight: '600',
  },
  description: {
    opacity: 0.8,
    lineHeight: 20,
    marginBottom: spacing.lg,
  },
  trainingDetails: {
    gap: spacing.sm,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  detailText: {
    marginLeft: spacing.sm,
  },
  tabsContainer: {
    padding: spacing.md,
    paddingBottom: spacing.sm,
  },
  segmentedButtons: {
    marginBottom: spacing.sm,
  },
  card: {
    margin: spacing.md,
    marginTop: spacing.sm,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: spacing.sm,
  },
  divider: {
    marginBottom: spacing.md,
  },
  infoSection: {
    marginBottom: spacing.lg,
  },
  infoLabel: {
    opacity: 0.7,
    marginBottom: spacing.sm,
    fontWeight: '500',
  },
  objectivesList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  objectiveChip: {
    height: 24,
    backgroundColor: '#f5f5f5',
  },
  objectiveText: {
    fontSize: 11,
    opacity: 0.8,
  },
  emptyMessage: {
    textAlign: 'center',
    opacity: 0.6,
    fontStyle: 'italic',
    paddingVertical: spacing.lg,
  },
  actions: {
    padding: spacing.md,
    paddingBottom: spacing.xl,
    gap: spacing.md,
  },
  actionButton: {
    marginTop: spacing.sm,
  },
});
