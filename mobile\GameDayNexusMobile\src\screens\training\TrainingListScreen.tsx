import React, { useState, useCallback } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  RefreshControl,
  TouchableOpacity,
} from 'react-native';
import {
  Text,
  Searchbar,
  FAB,
  Chip,
  useTheme,
  ActivityIndicator,
  SegmentedButtons,
} from 'react-native-paper';
import { useFocusEffect } from '@react-navigation/native';
import { spacing } from '@/theme';
import TrainingCard from '@/components/training/TrainingCard';
import TrainingFilterBottomSheet from '@/components/training/TrainingFilterBottomSheet';
import { Training, TrainingFilters, TrainingStatus } from '@/types/training';

interface TrainingListScreenProps {
  navigation: any;
}

export default function TrainingListScreen({ navigation }: TrainingListScreenProps) {
  const theme = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [trainings, setTrainings] = useState<Training[]>([]);
  const [filteredTrainings, setFilteredTrainings] = useState<Training[]>([]);
  const [filters, setFilters] = useState<TrainingFilters>({});
  const [showFilters, setShowFilters] = useState(false);
  const [selectedTab, setSelectedTab] = useState('upcoming');

  // Dados mockados para demonstração
  const mockTrainings: Training[] = [
    {
      id: '1',
      club_id: 'club1',
      title: 'Treino Técnico - Sub-20',
      description: 'Foco em passes e controle de bola',
      date: '2025-08-25',
      start_time: '15:00',
      end_time: '17:00',
      location: 'Campo Principal',
      category_id: 'cat1',
      category_name: 'Sub-20',
      coach_name: 'Carlos Silva',
      type: 'technical',
      status: 'scheduled',
      objectives: ['Melhorar passe', 'Controle de bola'],
      created_at: '2025-01-01',
      updated_at: '2025-01-01',
    },
    {
      id: '2',
      club_id: 'club1',
      title: 'Preparação Física',
      description: 'Treino de resistência e força',
      date: '2025-08-26',
      start_time: '09:00',
      end_time: '10:30',
      location: 'Academia',
      category_id: 'cat1',
      category_name: 'Sub-20',
      coach_name: 'João Santos',
      type: 'physical',
      status: 'scheduled',
      objectives: ['Resistência', 'Força'],
      created_at: '2025-01-01',
      updated_at: '2025-01-01',
    },
    {
      id: '3',
      club_id: 'club1',
      title: 'Treino Tático - Sub-17',
      description: 'Trabalho de posicionamento defensivo',
      date: '2025-08-20',
      start_time: '16:00',
      end_time: '18:00',
      location: 'Campo Secundário',
      category_id: 'cat2',
      category_name: 'Sub-17',
      coach_name: 'Pedro Lima',
      type: 'tactical',
      status: 'completed',
      objectives: ['Posicionamento', 'Marcação'],
      created_at: '2025-01-01',
      updated_at: '2025-01-01',
    },
    {
      id: '4',
      club_id: 'club1',
      title: 'Jogo-treino',
      description: 'Simulação de jogo real',
      date: '2025-08-18',
      start_time: '14:00',
      end_time: '16:00',
      location: 'Campo Principal',
      category_id: 'cat1',
      category_name: 'Sub-20',
      coach_name: 'Carlos Silva',
      type: 'friendly',
      status: 'completed',
      objectives: ['Aplicar conceitos', 'Avaliação'],
      created_at: '2025-01-01',
      updated_at: '2025-01-01',
    },
  ];

  useFocusEffect(
    useCallback(() => {
      loadTrainings();
    }, [])
  );

  const loadTrainings = async () => {
    setLoading(true);
    try {
      // Simular carregamento da API
      await new Promise(resolve => setTimeout(resolve, 1000));
      setTrainings(mockTrainings);
      filterTrainings(searchQuery, filters, selectedTab, mockTrainings);
    } catch (error) {
      console.error('Erro ao carregar treinamentos:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadTrainings();
    setRefreshing(false);
  }, []);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    filterTrainings(query, filters, selectedTab, trainings);
  };

  const handleFilter = (newFilters: TrainingFilters) => {
    setFilters(newFilters);
    filterTrainings(searchQuery, newFilters, selectedTab, trainings);
    setShowFilters(false);
  };

  const handleTabChange = (tab: string) => {
    setSelectedTab(tab);
    filterTrainings(searchQuery, filters, tab, trainings);
  };

  const filterTrainings = (
    query: string, 
    currentFilters: TrainingFilters, 
    tab: string,
    allTrainings: Training[]
  ) => {
    let filtered = [...allTrainings];

    // Filtro por aba (próximos/passados)
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (tab === 'upcoming') {
      filtered = filtered.filter(training => {
        const trainingDate = new Date(training.date);
        return trainingDate >= today || training.status === 'in_progress';
      });
    } else if (tab === 'past') {
      filtered = filtered.filter(training => {
        const trainingDate = new Date(training.date);
        return trainingDate < today && training.status === 'completed';
      });
    }

    // Filtro por busca
    if (query) {
      filtered = filtered.filter(training =>
        training.title.toLowerCase().includes(query.toLowerCase()) ||
        training.description?.toLowerCase().includes(query.toLowerCase()) ||
        training.location.toLowerCase().includes(query.toLowerCase()) ||
        training.coach_name?.toLowerCase().includes(query.toLowerCase())
      );
    }

    // Filtros específicos
    if (currentFilters.category_id) {
      filtered = filtered.filter(training => 
        training.category_id === currentFilters.category_id
      );
    }

    if (currentFilters.type) {
      filtered = filtered.filter(training => 
        training.type === currentFilters.type
      );
    }

    if (currentFilters.status) {
      filtered = filtered.filter(training => 
        training.status === currentFilters.status
      );
    }

    if (currentFilters.location) {
      filtered = filtered.filter(training => 
        training.location.toLowerCase().includes(currentFilters.location!.toLowerCase())
      );
    }

    // Ordenar por data e horário
    filtered.sort((a, b) => {
      const dateTimeA = new Date(`${a.date} ${a.start_time}`);
      const dateTimeB = new Date(`${b.date} ${b.start_time}`);
      
      if (tab === 'upcoming') {
        return dateTimeA.getTime() - dateTimeB.getTime(); // Próximos: mais próximos primeiro
      } else {
        return dateTimeB.getTime() - dateTimeA.getTime(); // Passados: mais recentes primeiro
      }
    });

    setFilteredTrainings(filtered);
  };

  const handleTrainingPress = (training: Training) => {
    navigation.navigate('TrainingDetail', { trainingId: training.id });
  };

  const handleAddTraining = () => {
    navigation.navigate('TrainingForm');
  };

  const getActiveFiltersCount = () => {
    return Object.values(filters).filter(value => value !== undefined && value !== '').length;
  };

  const renderTraining = ({ item }: { item: Training }) => (
    <TrainingCard
      training={item}
      onPress={() => handleTrainingPress(item)}
    />
  );

  const renderHeader = () => (
    <View style={styles.header}>
      <SegmentedButtons
        value={selectedTab}
        onValueChange={handleTabChange}
        buttons={[
          {
            value: 'upcoming',
            label: 'Próximos',
            icon: 'calendar-clock',
          },
          {
            value: 'past',
            label: 'Passados',
            icon: 'history',
          },
        ]}
        style={styles.segmentedButtons}
      />

      <Searchbar
        placeholder="Buscar treinamentos..."
        onChangeText={handleSearch}
        value={searchQuery}
        style={styles.searchbar}
      />
      
      <View style={styles.filtersContainer}>
        <TouchableOpacity onPress={() => setShowFilters(true)}>
          <Chip
            icon="filter-variant"
            style={styles.filterChip}
            textStyle={styles.filterChipText}
          >
            Filtros {getActiveFiltersCount() > 0 && `(${getActiveFiltersCount()})`}
          </Chip>
        </TouchableOpacity>
      </View>

      <View style={styles.resultsContainer}>
        <Text variant="bodyMedium" style={styles.resultsText}>
          {filteredTrainings.length} treinamento{filteredTrainings.length !== 1 ? 's' : ''} encontrado{filteredTrainings.length !== 1 ? 's' : ''}
        </Text>
      </View>
    </View>
  );

  const renderEmpty = () => (
    <View style={styles.emptyContainer}>
      <Text variant="titleMedium" style={styles.emptyTitle}>
        {selectedTab === 'upcoming' ? 'Nenhum treino agendado' : 'Nenhum treino encontrado'}
      </Text>
      <Text variant="bodyMedium" style={styles.emptyMessage}>
        {searchQuery || Object.keys(filters).length > 0
          ? 'Tente ajustar os filtros de busca'
          : selectedTab === 'upcoming' 
            ? 'Agende o primeiro treino da categoria'
            : 'Não há treinos finalizados'
        }
      </Text>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text variant="bodyMedium" style={styles.loadingText}>
          Carregando treinamentos...
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <FlatList
        data={filteredTrainings}
        renderItem={renderTraining}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={renderEmpty}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
      />

      <FAB
        icon="plus"
        style={[styles.fab, { backgroundColor: theme.colors.primary }]}
        onPress={handleAddTraining}
        label="Novo Treino"
      />

      <TrainingFilterBottomSheet
        visible={showFilters}
        onDismiss={() => setShowFilters(false)}
        onApply={handleFilter}
        currentFilters={filters}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: spacing.md,
    opacity: 0.7,
  },
  listContent: {
    flexGrow: 1,
  },
  header: {
    padding: spacing.md,
    paddingBottom: spacing.sm,
  },
  segmentedButtons: {
    marginBottom: spacing.md,
  },
  searchbar: {
    marginBottom: spacing.md,
  },
  filtersContainer: {
    flexDirection: 'row',
    marginBottom: spacing.md,
  },
  filterChip: {
    marginRight: spacing.sm,
  },
  filterChipText: {
    fontSize: 12,
  },
  resultsContainer: {
    marginBottom: spacing.sm,
  },
  resultsText: {
    opacity: 0.7,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.xxxl,
  },
  emptyTitle: {
    fontWeight: '600',
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  emptyMessage: {
    textAlign: 'center',
    opacity: 0.7,
    lineHeight: 20,
  },
  fab: {
    position: 'absolute',
    margin: spacing.md,
    right: 0,
    bottom: 0,
  },
});
