import { supabase, handleSupabaseResponse } from '@/lib/supabase';
import { Athlete, AthleteStats, Category } from '@/types/athletes';
import { AthleteFormData } from '@/types/forms';

export class AthleteService {
  // Buscar todos os atletas
  static async getAthletes(clubId: string, filters?: {
    category_id?: string;
    status?: string;
    position?: string;
    search?: string;
  }) {
    try {
      let query = supabase
        .from('athletes')
        .select(`
          *,
          category:categories(id, name, description),
          stats:athlete_stats(*)
        `)
        .eq('club_id', clubId)
        .order('first_name', { ascending: true });

      // Aplicar filtros
      if (filters?.category_id) {
        query = query.eq('category_id', filters.category_id);
      }

      if (filters?.status) {
        query = query.eq('status', filters.status);
      }

      if (filters?.position) {
        query = query.ilike('position', `%${filters.position}%`);
      }

      if (filters?.search) {
        query = query.or(`
          first_name.ilike.%${filters.search}%,
          last_name.ilike.%${filters.search}%,
          email.ilike.%${filters.search}%
        `);
      }

      const { data, error } = await query;
      return handleSupabaseResponse(data, error);
    } catch (error) {
      return handleSupabaseResponse(null, error);
    }
  }

  // Buscar atleta por ID
  static async getAthleteById(id: string) {
    try {
      const { data, error } = await supabase
        .from('athletes')
        .select(`
          *,
          category:categories(id, name, description),
          stats:athlete_stats(*),
          medical_records:medical_records(*),
          training_attendance:training_attendance(*)
        `)
        .eq('id', id)
        .single();

      return handleSupabaseResponse(data, error);
    } catch (error) {
      return handleSupabaseResponse(null, error);
    }
  }

  // Criar novo atleta
  static async createAthlete(clubId: string, athleteData: AthleteFormData) {
    try {
      const { data, error } = await supabase
        .from('athletes')
        .insert([{
          club_id: clubId,
          ...athleteData,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }])
        .select()
        .single();

      return handleSupabaseResponse(data, error);
    } catch (error) {
      return handleSupabaseResponse(null, error);
    }
  }

  // Atualizar atleta
  static async updateAthlete(id: string, athleteData: Partial<AthleteFormData>) {
    try {
      const { data, error } = await supabase
        .from('athletes')
        .update({
          ...athleteData,
          updated_at: new Date().toISOString(),
        })
        .eq('id', id)
        .select()
        .single();

      return handleSupabaseResponse(data, error);
    } catch (error) {
      return handleSupabaseResponse(null, error);
    }
  }

  // Deletar atleta
  static async deleteAthlete(id: string) {
    try {
      const { data, error } = await supabase
        .from('athletes')
        .delete()
        .eq('id', id);

      return handleSupabaseResponse(data, error);
    } catch (error) {
      return handleSupabaseResponse(null, error);
    }
  }

  // Buscar categorias
  static async getCategories(clubId: string) {
    try {
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .eq('club_id', clubId)
        .eq('active', true)
        .order('name', { ascending: true });

      return handleSupabaseResponse(data, error);
    } catch (error) {
      return handleSupabaseResponse(null, error);
    }
  }

  // Buscar estatísticas do atleta
  static async getAthleteStats(athleteId: string) {
    try {
      const { data, error } = await supabase
        .from('athlete_stats')
        .select('*')
        .eq('athlete_id', athleteId)
        .order('created_at', { ascending: false });

      return handleSupabaseResponse(data, error);
    } catch (error) {
      return handleSupabaseResponse(null, error);
    }
  }

  // Atualizar estatísticas do atleta
  static async updateAthleteStats(athleteId: string, stats: Partial<AthleteStats>) {
    try {
      const { data, error } = await supabase
        .from('athlete_stats')
        .upsert({
          athlete_id: athleteId,
          ...stats,
          updated_at: new Date().toISOString(),
        })
        .select()
        .single();

      return handleSupabaseResponse(data, error);
    } catch (error) {
      return handleSupabaseResponse(null, error);
    }
  }

  // Upload de foto do atleta
  static async uploadAthletePhoto(athleteId: string, photoFile: File | Blob) {
    try {
      const fileName = `athlete_${athleteId}_${Date.now()}.jpg`;
      const filePath = `athletes/${fileName}`;

      // Upload da imagem
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('athlete-photos')
        .upload(filePath, photoFile, {
          cacheControl: '3600',
          upsert: true,
        });

      if (uploadError) {
        return handleSupabaseResponse(null, uploadError);
      }

      // Obter URL pública
      const { data: urlData } = supabase.storage
        .from('athlete-photos')
        .getPublicUrl(filePath);

      // Atualizar URL da foto no perfil do atleta
      const { data: updateData, error: updateError } = await supabase
        .from('athletes')
        .update({ photo_url: urlData.publicUrl })
        .eq('id', athleteId)
        .select()
        .single();

      return handleSupabaseResponse(updateData, updateError);
    } catch (error) {
      return handleSupabaseResponse(null, error);
    }
  }

  // Buscar atletas por categoria
  static async getAthletesByCategory(clubId: string, categoryId: string) {
    try {
      const { data, error } = await supabase
        .from('athletes')
        .select(`
          *,
          category:categories(id, name, description)
        `)
        .eq('club_id', clubId)
        .eq('category_id', categoryId)
        .eq('status', 'active')
        .order('first_name', { ascending: true });

      return handleSupabaseResponse(data, error);
    } catch (error) {
      return handleSupabaseResponse(null, error);
    }
  }

  // Buscar atletas por posição
  static async getAthletesByPosition(clubId: string, position: string) {
    try {
      const { data, error } = await supabase
        .from('athletes')
        .select(`
          *,
          category:categories(id, name, description)
        `)
        .eq('club_id', clubId)
        .ilike('position', `%${position}%`)
        .eq('status', 'active')
        .order('first_name', { ascending: true });

      return handleSupabaseResponse(data, error);
    } catch (error) {
      return handleSupabaseResponse(null, error);
    }
  }

  // Buscar resumo de atletas
  static async getAthleteSummary(clubId: string) {
    try {
      const { data, error } = await supabase
        .rpc('get_athlete_summary', { club_id: clubId });

      return handleSupabaseResponse(data, error);
    } catch (error) {
      return handleSupabaseResponse(null, error);
    }
  }

  // Verificar disponibilidade de número da camisa
  static async checkJerseyNumber(clubId: string, categoryId: string, jerseyNumber: number, excludeAthleteId?: string) {
    try {
      let query = supabase
        .from('athletes')
        .select('id')
        .eq('club_id', clubId)
        .eq('category_id', categoryId)
        .eq('jersey_number', jerseyNumber);

      if (excludeAthleteId) {
        query = query.neq('id', excludeAthleteId);
      }

      const { data, error } = await query;

      if (error) {
        return handleSupabaseResponse(null, error);
      }

      // Retorna true se o número está disponível (não encontrou nenhum atleta)
      return handleSupabaseResponse(data.length === 0, null);
    } catch (error) {
      return handleSupabaseResponse(null, error);
    }
  }

  // Buscar aniversariantes do mês
  static async getBirthdayAthletes(clubId: string, month?: number) {
    try {
      const currentMonth = month || new Date().getMonth() + 1;
      
      const { data, error } = await supabase
        .from('athletes')
        .select(`
          *,
          category:categories(id, name, description)
        `)
        .eq('club_id', clubId)
        .eq('status', 'active')
        .gte('birth_date', `2000-${currentMonth.toString().padStart(2, '0')}-01`)
        .lt('birth_date', `2000-${(currentMonth + 1).toString().padStart(2, '0')}-01`)
        .order('birth_date', { ascending: true });

      return handleSupabaseResponse(data, error);
    } catch (error) {
      return handleSupabaseResponse(null, error);
    }
  }

  // Buscar atletas recém-cadastrados
  static async getRecentAthletes(clubId: string, days: number = 7) {
    try {
      const dateLimit = new Date();
      dateLimit.setDate(dateLimit.getDate() - days);

      const { data, error } = await supabase
        .from('athletes')
        .select(`
          *,
          category:categories(id, name, description)
        `)
        .eq('club_id', clubId)
        .gte('created_at', dateLimit.toISOString())
        .order('created_at', { ascending: false });

      return handleSupabaseResponse(data, error);
    } catch (error) {
      return handleSupabaseResponse(null, error);
    }
  }
}
