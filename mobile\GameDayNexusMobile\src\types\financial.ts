export interface FinancialTransaction {
  id: string;
  club_id: string;
  type: TransactionType;
  category_id: string;
  category: FinancialCategory;
  title: string;
  description?: string;
  amount: number;
  date: string;
  due_date?: string;
  payment_date?: string;
  status: TransactionStatus;
  payment_method?: PaymentMethod;
  reference_number?: string;
  tags?: string[];
  attachments?: string[];
  recurring?: RecurringConfig;
  created_by?: string;
  created_at: string;
  updated_at: string;
}

export type TransactionType = 
  | 'income'          // Receita
  | 'expense';        // Despesa

export type TransactionStatus = 
  | 'pending'         // Pendente
  | 'paid'            // Pago
  | 'overdue'         // Vencido
  | 'cancelled'       // Cancelado
  | 'partial';        // Parcial

export type PaymentMethod = 
  | 'cash'            // Dinheiro
  | 'bank_transfer'   // Transferência
  | 'credit_card'     // Cartão de Crédito
  | 'debit_card'      // Cartão de Débito
  | 'pix'             // PIX
  | 'check'           // Cheque
  | 'boleto';         // Boleto

export interface FinancialCategory {
  id: string;
  name: string;
  description?: string;
  type: TransactionType;
  color: string;
  icon: string;
  parent_id?: string;
  active: boolean;
  created_at: string;
  updated_at: string;
}

export interface RecurringConfig {
  frequency: RecurringFrequency;
  interval: number;
  end_date?: string;
  occurrences?: number;
}

export type RecurringFrequency = 
  | 'daily'           // Diário
  | 'weekly'          // Semanal
  | 'monthly'         // Mensal
  | 'quarterly'       // Trimestral
  | 'yearly';         // Anual

export interface FinancialReport {
  id: string;
  type: ReportType;
  title: string;
  period_start: string;
  period_end: string;
  data: ReportData;
  generated_at: string;
}

export type ReportType = 
  | 'cash_flow'       // Fluxo de Caixa
  | 'income_statement' // DRE
  | 'balance_sheet'   // Balanço
  | 'category_analysis' // Análise por Categoria
  | 'monthly_summary'; // Resumo Mensal

export interface ReportData {
  total_income: number;
  total_expenses: number;
  net_result: number;
  categories: CategorySummary[];
  monthly_data?: MonthlyData[];
  payment_methods?: PaymentMethodSummary[];
}

export interface CategorySummary {
  category_id: string;
  category_name: string;
  category_color: string;
  total_amount: number;
  transaction_count: number;
  percentage: number;
}

export interface MonthlyData {
  month: string;
  income: number;
  expenses: number;
  net_result: number;
}

export interface PaymentMethodSummary {
  method: PaymentMethod;
  total_amount: number;
  transaction_count: number;
  percentage: number;
}

export interface Budget {
  id: string;
  club_id: string;
  name: string;
  description?: string;
  period_start: string;
  period_end: string;
  categories: BudgetCategory[];
  status: BudgetStatus;
  created_at: string;
  updated_at: string;
}

export type BudgetStatus = 
  | 'draft'           // Rascunho
  | 'active'          // Ativo
  | 'completed'       // Concluído
  | 'cancelled';      // Cancelado

export interface BudgetCategory {
  category_id: string;
  category_name: string;
  planned_amount: number;
  actual_amount: number;
  variance: number;
  variance_percentage: number;
}

export interface FinancialFilters {
  type?: TransactionType;
  category_id?: string;
  status?: TransactionStatus;
  payment_method?: PaymentMethod;
  date_from?: string;
  date_to?: string;
  amount_min?: number;
  amount_max?: number;
  tags?: string[];
}

export interface FinancialFormData {
  type: TransactionType;
  category_id: string;
  title: string;
  description?: string;
  amount: number;
  date: string;
  due_date?: string;
  payment_method?: PaymentMethod;
  tags?: string[];
  recurring?: RecurringConfig;
}

export interface FinancialStatistics {
  current_balance: number;
  monthly_income: number;
  monthly_expenses: number;
  monthly_net: number;
  pending_receivables: number;
  pending_payables: number;
  overdue_count: number;
  top_expense_categories: CategorySummary[];
  top_income_categories: CategorySummary[];
  cash_flow_trend: MonthlyData[];
}

// Utilitários
export const getTransactionTypeLabel = (type: TransactionType): string => {
  switch (type) {
    case 'income':
      return 'Receita';
    case 'expense':
      return 'Despesa';
    default:
      return type;
  }
};

export const getTransactionTypeColor = (type: TransactionType): string => {
  switch (type) {
    case 'income':
      return '#4caf50';
    case 'expense':
      return '#f44336';
    default:
      return '#9e9e9e';
  }
};

export const getTransactionStatusLabel = (status: TransactionStatus): string => {
  switch (status) {
    case 'pending':
      return 'Pendente';
    case 'paid':
      return 'Pago';
    case 'overdue':
      return 'Vencido';
    case 'cancelled':
      return 'Cancelado';
    case 'partial':
      return 'Parcial';
    default:
      return status;
  }
};

export const getTransactionStatusColor = (status: TransactionStatus): string => {
  switch (status) {
    case 'pending':
      return '#ff9800';
    case 'paid':
      return '#4caf50';
    case 'overdue':
      return '#f44336';
    case 'cancelled':
      return '#9e9e9e';
    case 'partial':
      return '#2196f3';
    default:
      return '#9e9e9e';
  }
};

export const getPaymentMethodLabel = (method: PaymentMethod): string => {
  switch (method) {
    case 'cash':
      return 'Dinheiro';
    case 'bank_transfer':
      return 'Transferência';
    case 'credit_card':
      return 'Cartão de Crédito';
    case 'debit_card':
      return 'Cartão de Débito';
    case 'pix':
      return 'PIX';
    case 'check':
      return 'Cheque';
    case 'boleto':
      return 'Boleto';
    default:
      return method;
  }
};

export const getPaymentMethodIcon = (method: PaymentMethod): string => {
  switch (method) {
    case 'cash':
      return 'payments';
    case 'bank_transfer':
      return 'account-balance';
    case 'credit_card':
      return 'credit-card';
    case 'debit_card':
      return 'credit-card';
    case 'pix':
      return 'qr-code';
    case 'check':
      return 'receipt';
    case 'boleto':
      return 'receipt-long';
    default:
      return 'payment';
  }
};

export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  }).format(amount);
};

export const formatPercentage = (value: number): string => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'percent',
    minimumFractionDigits: 1,
    maximumFractionDigits: 1,
  }).format(value / 100);
};

export const calculateVariance = (planned: number, actual: number): number => {
  if (planned === 0) return 0;
  return ((actual - planned) / planned) * 100;
};

export const isOverdue = (dueDate: string, status: TransactionStatus): boolean => {
  if (status === 'paid' || status === 'cancelled') return false;
  const due = new Date(dueDate);
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return due < today;
};
