export interface FormField {
  id: string;
  type: FormFieldType;
  label: string;
  placeholder?: string;
  value: any;
  required?: boolean;
  disabled?: boolean;
  error?: string;
  options?: FormOption[];
  validation?: FormValidation;
  dependencies?: FormDependency[];
  metadata?: any;
}

export type FormFieldType = 
  | 'text'
  | 'email'
  | 'phone'
  | 'number'
  | 'password'
  | 'textarea'
  | 'select'
  | 'multiselect'
  | 'date'
  | 'time'
  | 'datetime'
  | 'switch'
  | 'checkbox'
  | 'radio'
  | 'image'
  | 'file'
  | 'color'
  | 'currency'
  | 'percentage';

export interface FormOption {
  label: string;
  value: any;
  disabled?: boolean;
  icon?: string;
  color?: string;
}

export interface FormValidation {
  min?: number;
  max?: number;
  minLength?: number;
  maxLength?: number;
  pattern?: string;
  custom?: (value: any) => string | null;
}

export interface FormDependency {
  field: string;
  condition: 'equals' | 'not_equals' | 'contains' | 'not_contains';
  value: any;
  action: 'show' | 'hide' | 'enable' | 'disable' | 'require';
}

export interface FormSection {
  id: string;
  title: string;
  description?: string;
  fields: FormField[];
  collapsible?: boolean;
  collapsed?: boolean;
}

export interface FormConfig {
  id: string;
  title: string;
  description?: string;
  sections: FormSection[];
  submitLabel?: string;
  cancelLabel?: string;
  autoSave?: boolean;
  autoSaveInterval?: number; // seconds
}

export interface FormData {
  [key: string]: any;
}

export interface FormErrors {
  [key: string]: string;
}

export interface FormState {
  data: FormData;
  errors: FormErrors;
  isValid: boolean;
  isDirty: boolean;
  isSubmitting: boolean;
  lastSaved?: string;
}

// Formulários específicos
export interface AthleteFormData {
  // Informações Básicas
  first_name: string;
  last_name: string;
  birth_date: string;
  gender: 'male' | 'female' | 'other';
  nationality: string;
  document_type: 'cpf' | 'rg' | 'passport';
  document_number: string;
  photo_url?: string;
  
  // Informações de Contato
  phone?: string;
  email?: string;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  emergency_contact_relationship?: string;
  
  // Endereço
  address_street?: string;
  address_number?: string;
  address_complement?: string;
  address_neighborhood?: string;
  address_city?: string;
  address_state?: string;
  address_zip_code?: string;
  
  // Informações Esportivas
  category_id: string;
  position: string;
  jersey_number?: number;
  preferred_foot: 'left' | 'right' | 'both';
  height?: number;
  weight?: number;
  
  // Status
  status: 'active' | 'inactive' | 'injured' | 'suspended' | 'loaned';
  join_date: string;
  contract_end_date?: string;
  
  // Observações
  notes?: string;
}

export interface MatchFormData {
  // Informações Básicas
  opponent: string;
  competition: string;
  match_type: 'friendly' | 'championship' | 'cup' | 'playoff';
  home_away: 'home' | 'away';
  
  // Data e Local
  date: string;
  time: string;
  venue: string;
  venue_address?: string;
  
  // Categoria
  category_id: string;
  
  // Resultado (se já jogado)
  status: 'scheduled' | 'live' | 'finished' | 'cancelled' | 'postponed';
  home_score?: number;
  away_score?: number;
  
  // Observações
  notes?: string;
  referee?: string;
  weather_conditions?: string;
}

export interface TrainingFormData {
  // Informações Básicas
  title: string;
  description?: string;
  type: 'technical' | 'physical' | 'tactical' | 'recovery' | 'friendly';
  
  // Data e Local
  date: string;
  start_time: string;
  end_time: string;
  location: string;
  
  // Categoria
  category_id: string;
  
  // Configurações
  max_participants?: number;
  equipment_needed?: string[];
  intensity: 'low' | 'medium' | 'high';
  
  // Observações
  objectives?: string;
  notes?: string;
}

export interface UserFormData {
  // Informações Básicas
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  birth_date?: string;
  
  // Função
  role: string;
  department?: string;
  position?: string;
  employee_id?: string;
  hire_date?: string;
  
  // Endereço
  address_street?: string;
  address_number?: string;
  address_complement?: string;
  address_neighborhood?: string;
  address_city?: string;
  address_state?: string;
  address_zip_code?: string;
  
  // Contato de Emergência
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  emergency_contact_relationship?: string;
  
  // Permissões
  permissions: {
    [key: string]: 'none' | 'read' | 'write' | 'admin';
  };
  
  // Observações
  notes?: string;
}

export interface FinancialFormData {
  // Informações Básicas
  type: 'income' | 'expense';
  category_id: string;
  amount: number;
  description: string;
  
  // Data
  date: string;
  due_date?: string;
  
  // Detalhes
  payment_method?: 'cash' | 'credit_card' | 'debit_card' | 'bank_transfer' | 'pix' | 'check';
  status: 'pending' | 'paid' | 'overdue' | 'cancelled';
  
  // Recorrência
  is_recurring?: boolean;
  recurring_frequency?: 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  recurring_end_date?: string;
  
  // Anexos
  receipt_url?: string;
  
  // Observações
  notes?: string;
}

export interface InventoryFormData {
  // Informações Básicas
  name: string;
  description?: string;
  category_id: string;
  brand?: string;
  model?: string;
  
  // Identificação
  serial_number?: string;
  barcode?: string;
  
  // Estoque
  current_stock: number;
  min_stock: number;
  max_stock?: number;
  unit: 'unit' | 'kg' | 'liter' | 'meter' | 'box' | 'pair';
  
  // Valores
  unit_cost?: number;
  
  // Localização
  location?: string;
  supplier?: string;
  
  // Datas
  purchase_date?: string;
  warranty_expiry?: string;
  
  // Estado
  condition: 'new' | 'good' | 'fair' | 'poor' | 'damaged';
  status: 'active' | 'inactive' | 'maintenance' | 'retired';
  
  // Tags e Observações
  tags?: string[];
  notes?: string;
}

// Utilitários de validação
export const validateRequired = (value: any): string | null => {
  if (value === null || value === undefined || value === '') {
    return 'Este campo é obrigatório';
  }
  return null;
};

export const validateEmail = (value: string): string | null => {
  if (!value) return null;
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(value)) {
    return 'E-mail inválido';
  }
  return null;
};

export const validatePhone = (value: string): string | null => {
  if (!value) return null;
  const phoneRegex = /^\(\d{2}\)\s\d{4,5}-\d{4}$/;
  if (!phoneRegex.test(value)) {
    return 'Telefone inválido. Use o formato (11) 99999-9999';
  }
  return null;
};

export const validateCPF = (value: string): string | null => {
  if (!value) return null;
  const cpf = value.replace(/\D/g, '');
  if (cpf.length !== 11) {
    return 'CPF deve ter 11 dígitos';
  }
  // Validação básica de CPF
  if (/^(\d)\1{10}$/.test(cpf)) {
    return 'CPF inválido';
  }
  return null;
};

export const validateMinLength = (min: number) => (value: string): string | null => {
  if (!value) return null;
  if (value.length < min) {
    return `Deve ter pelo menos ${min} caracteres`;
  }
  return null;
};

export const validateMaxLength = (max: number) => (value: string): string | null => {
  if (!value) return null;
  if (value.length > max) {
    return `Deve ter no máximo ${max} caracteres`;
  }
  return null;
};

export const validateNumber = (value: any): string | null => {
  if (value === null || value === undefined || value === '') return null;
  if (isNaN(Number(value))) {
    return 'Deve ser um número válido';
  }
  return null;
};

export const validatePositiveNumber = (value: any): string | null => {
  const numberError = validateNumber(value);
  if (numberError) return numberError;
  if (Number(value) < 0) {
    return 'Deve ser um número positivo';
  }
  return null;
};

export const validateDate = (value: string): string | null => {
  if (!value) return null;
  const date = new Date(value);
  if (isNaN(date.getTime())) {
    return 'Data inválida';
  }
  return null;
};

export const validateFutureDate = (value: string): string | null => {
  const dateError = validateDate(value);
  if (dateError) return dateError;
  const date = new Date(value);
  const now = new Date();
  if (date <= now) {
    return 'Data deve ser no futuro';
  }
  return null;
};

export const validatePastDate = (value: string): string | null => {
  const dateError = validateDate(value);
  if (dateError) return dateError;
  const date = new Date(value);
  const now = new Date();
  if (date >= now) {
    return 'Data deve ser no passado';
  }
  return null;
};

export const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  }).format(value);
};

export const formatPhone = (value: string): string => {
  const numbers = value.replace(/\D/g, '');
  if (numbers.length === 11) {
    return `(${numbers.slice(0, 2)}) ${numbers.slice(2, 7)}-${numbers.slice(7)}`;
  } else if (numbers.length === 10) {
    return `(${numbers.slice(0, 2)}) ${numbers.slice(2, 6)}-${numbers.slice(6)}`;
  }
  return value;
};

export const formatCPF = (value: string): string => {
  const numbers = value.replace(/\D/g, '');
  if (numbers.length === 11) {
    return `${numbers.slice(0, 3)}.${numbers.slice(3, 6)}.${numbers.slice(6, 9)}-${numbers.slice(9)}`;
  }
  return value;
};
