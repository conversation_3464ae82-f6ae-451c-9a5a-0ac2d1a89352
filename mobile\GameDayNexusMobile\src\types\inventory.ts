export interface InventoryItem {
  id: string;
  club_id: string;
  name: string;
  description?: string;
  category_id: string;
  category: InventoryCategory;
  brand?: string;
  model?: string;
  serial_number?: string;
  barcode?: string;
  current_stock: number;
  min_stock: number;
  max_stock?: number;
  unit: StockUnit;
  unit_cost?: number;
  total_value: number;
  location?: string;
  supplier?: string;
  purchase_date?: string;
  warranty_expiry?: string;
  condition: ItemCondition;
  status: ItemStatus;
  image_urls?: string[];
  tags?: string[];
  notes?: string;
  last_movement?: StockMovement;
  created_at: string;
  updated_at: string;
}

export type StockUnit = 
  | 'unit'            // Unidade
  | 'pair'            // Par
  | 'set'             // Conjunto
  | 'kg'              // Quilograma
  | 'liter'           // Litro
  | 'meter'           // Metro
  | 'box'             // Caixa
  | 'pack';           // Pacote

export type ItemCondition = 
  | 'new'             // Novo
  | 'good'            // Bom
  | 'fair'            // Regular
  | 'poor'            // Ruim
  | 'damaged';        // Danificado

export type ItemStatus = 
  | 'active'          // Ativo
  | 'inactive'        // Inativo
  | 'maintenance'     // Manutenção
  | 'retired';        // Aposentado

export interface InventoryCategory {
  id: string;
  name: string;
  description?: string;
  color: string;
  icon: string;
  parent_id?: string;
  active: boolean;
  created_at: string;
  updated_at: string;
}

export interface StockMovement {
  id: string;
  item_id: string;
  item: InventoryItem;
  type: MovementType;
  quantity: number;
  unit_cost?: number;
  total_cost?: number;
  reason: MovementReason;
  description?: string;
  date: string;
  reference_number?: string;
  supplier?: string;
  recipient?: string;
  location_from?: string;
  location_to?: string;
  created_by?: string;
  created_at: string;
  updated_at: string;
}

export type MovementType = 
  | 'in'              // Entrada
  | 'out'             // Saída
  | 'transfer'        // Transferência
  | 'adjustment';     // Ajuste

export type MovementReason = 
  | 'purchase'        // Compra
  | 'donation'        // Doação
  | 'return'          // Devolução
  | 'usage'           // Uso
  | 'loss'            // Perda
  | 'damage'          // Dano
  | 'theft'           // Roubo
  | 'maintenance'     // Manutenção
  | 'transfer'        // Transferência
  | 'adjustment'      // Ajuste de estoque
  | 'expired';        // Vencido

export interface StockAlert {
  id: string;
  item_id: string;
  item: InventoryItem;
  type: AlertType;
  severity: AlertSeverity;
  message: string;
  threshold?: number;
  current_value?: number;
  created_at: string;
  resolved_at?: string;
}

export type AlertType = 
  | 'low_stock'       // Estoque baixo
  | 'out_of_stock'    // Sem estoque
  | 'overstock'       // Excesso de estoque
  | 'expiry_warning'  // Aviso de vencimento
  | 'expired'         // Vencido
  | 'maintenance_due'; // Manutenção necessária

export type AlertSeverity = 
  | 'info'            // Informação
  | 'warning'         // Aviso
  | 'critical';       // Crítico

export interface InventoryReport {
  id: string;
  type: ReportType;
  title: string;
  period_start?: string;
  period_end?: string;
  data: ReportData;
  generated_at: string;
}

export type ReportType = 
  | 'stock_summary'   // Resumo de Estoque
  | 'movements'       // Movimentações
  | 'valuation'       // Valorização
  | 'alerts'          // Alertas
  | 'low_stock';      // Estoque Baixo

export interface ReportData {
  total_items: number;
  total_value: number;
  categories: CategorySummary[];
  movements?: MovementSummary[];
  alerts?: AlertSummary[];
  low_stock_items?: InventoryItem[];
}

export interface CategorySummary {
  category_id: string;
  category_name: string;
  category_color: string;
  item_count: number;
  total_value: number;
  percentage: number;
}

export interface MovementSummary {
  date: string;
  in_quantity: number;
  out_quantity: number;
  in_value: number;
  out_value: number;
}

export interface AlertSummary {
  type: AlertType;
  count: number;
  severity: AlertSeverity;
}

export interface InventoryFilters {
  category_id?: string;
  condition?: ItemCondition;
  status?: ItemStatus;
  location?: string;
  supplier?: string;
  low_stock_only?: boolean;
  out_of_stock_only?: boolean;
  tags?: string[];
}

export interface InventoryFormData {
  name: string;
  description?: string;
  category_id: string;
  brand?: string;
  model?: string;
  serial_number?: string;
  current_stock: number;
  min_stock: number;
  max_stock?: number;
  unit: StockUnit;
  unit_cost?: number;
  location?: string;
  supplier?: string;
  purchase_date?: string;
  warranty_expiry?: string;
  condition: ItemCondition;
  tags?: string[];
  notes?: string;
}

export interface MovementFormData {
  item_id: string;
  type: MovementType;
  quantity: number;
  unit_cost?: number;
  reason: MovementReason;
  description?: string;
  date: string;
  supplier?: string;
  recipient?: string;
  location_from?: string;
  location_to?: string;
}

export interface InventoryStatistics {
  total_items: number;
  total_value: number;
  low_stock_count: number;
  out_of_stock_count: number;
  categories_count: number;
  recent_movements: number;
  top_categories: CategorySummary[];
  recent_alerts: StockAlert[];
}

// Utilitários
export const getStockUnitLabel = (unit: StockUnit): string => {
  switch (unit) {
    case 'unit':
      return 'Unidade';
    case 'pair':
      return 'Par';
    case 'set':
      return 'Conjunto';
    case 'kg':
      return 'Kg';
    case 'liter':
      return 'Litro';
    case 'meter':
      return 'Metro';
    case 'box':
      return 'Caixa';
    case 'pack':
      return 'Pacote';
    default:
      return unit;
  }
};

export const getItemConditionLabel = (condition: ItemCondition): string => {
  switch (condition) {
    case 'new':
      return 'Novo';
    case 'good':
      return 'Bom';
    case 'fair':
      return 'Regular';
    case 'poor':
      return 'Ruim';
    case 'damaged':
      return 'Danificado';
    default:
      return condition;
  }
};

export const getItemConditionColor = (condition: ItemCondition): string => {
  switch (condition) {
    case 'new':
      return '#4caf50';
    case 'good':
      return '#8bc34a';
    case 'fair':
      return '#ff9800';
    case 'poor':
      return '#f44336';
    case 'damaged':
      return '#9c27b0';
    default:
      return '#9e9e9e';
  }
};

export const getItemStatusLabel = (status: ItemStatus): string => {
  switch (status) {
    case 'active':
      return 'Ativo';
    case 'inactive':
      return 'Inativo';
    case 'maintenance':
      return 'Manutenção';
    case 'retired':
      return 'Aposentado';
    default:
      return status;
  }
};

export const getItemStatusColor = (status: ItemStatus): string => {
  switch (status) {
    case 'active':
      return '#4caf50';
    case 'inactive':
      return '#9e9e9e';
    case 'maintenance':
      return '#ff9800';
    case 'retired':
      return '#795548';
    default:
      return '#9e9e9e';
  }
};

export const getMovementTypeLabel = (type: MovementType): string => {
  switch (type) {
    case 'in':
      return 'Entrada';
    case 'out':
      return 'Saída';
    case 'transfer':
      return 'Transferência';
    case 'adjustment':
      return 'Ajuste';
    default:
      return type;
  }
};

export const getMovementTypeColor = (type: MovementType): string => {
  switch (type) {
    case 'in':
      return '#4caf50';
    case 'out':
      return '#f44336';
    case 'transfer':
      return '#2196f3';
    case 'adjustment':
      return '#ff9800';
    default:
      return '#9e9e9e';
  }
};

export const getAlertSeverityColor = (severity: AlertSeverity): string => {
  switch (severity) {
    case 'info':
      return '#2196f3';
    case 'warning':
      return '#ff9800';
    case 'critical':
      return '#f44336';
    default:
      return '#9e9e9e';
  }
};

export const isLowStock = (item: InventoryItem): boolean => {
  return item.current_stock <= item.min_stock;
};

export const isOutOfStock = (item: InventoryItem): boolean => {
  return item.current_stock === 0;
};

export const calculateStockPercentage = (current: number, max: number): number => {
  if (max === 0) return 0;
  return Math.min((current / max) * 100, 100);
};

export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  }).format(amount);
};
