import { Athlete } from './athletes';

export interface Match {
  id: string;
  club_id: string;
  opponent_id?: string;
  opponent_name: string;
  opponent_logo?: string;
  competition_id?: string;
  competition_name: string;
  date: string;
  time: string;
  location: string;
  is_home: boolean;
  status: MatchStatus;
  result?: MatchResult;
  formation?: Formation;
  lineup?: MatchLineup;
  events?: MatchEvent[];
  statistics?: MatchStatistics;
  created_at: string;
  updated_at: string;
}

export type MatchStatus = 
  | 'scheduled'    // Agendada
  | 'live'         // Ao vivo
  | 'halftime'     // Intervalo
  | 'finished'     // Finalizada
  | 'postponed'    // Adiada
  | 'cancelled';   // Cancelada

export interface MatchResult {
  home_score: number;
  away_score: number;
  home_penalties?: number;
  away_penalties?: number;
  winner?: 'home' | 'away' | 'draw';
}

export interface Formation {
  id: string;
  name: string;
  formation: string; // ex: "4-4-2"
  positions: FormationPosition[];
}

export interface FormationPosition {
  id: string;
  position_name: string;
  x: number; // Posição X no campo (0-100)
  y: number; // Posição Y no campo (0-100)
  zone: 'goalkeeper' | 'defense' | 'midfield' | 'attack';
}

export interface MatchLineup {
  id: string;
  match_id: string;
  formation_id: string;
  starters: LineupPlayer[];
  substitutes: LineupPlayer[];
  technical_staff: TechnicalStaff[];
  captain_id?: string;
}

export interface LineupPlayer {
  athlete_id: string;
  athlete: Athlete;
  position_id: string;
  jersey_number: number;
  is_starter: boolean;
  is_captain: boolean;
  substituted_at?: number; // Minuto da substituição
  substituted_by?: string; // ID do jogador que entrou
  yellow_cards: number;
  red_cards: number;
  goals: number;
  assists: number;
}

export interface TechnicalStaff {
  id: string;
  name: string;
  role: 'coach' | 'assistant_coach' | 'physical_trainer' | 'doctor' | 'physiotherapist';
  photo_url?: string;
}

export interface MatchEvent {
  id: string;
  match_id: string;
  type: MatchEventType;
  minute: number;
  extra_time?: number;
  athlete_id?: string;
  athlete_name?: string;
  description: string;
  team: 'home' | 'away';
  created_at: string;
}

export type MatchEventType =
  | 'goal'
  | 'own_goal'
  | 'penalty_goal'
  | 'yellow_card'
  | 'red_card'
  | 'substitution'
  | 'injury'
  | 'offside'
  | 'corner'
  | 'free_kick'
  | 'penalty'
  | 'var_check'
  | 'kickoff'
  | 'halftime'
  | 'fulltime';

export interface MatchStatistics {
  id: string;
  match_id: string;
  team: 'home' | 'away';
  possession: number;
  shots: number;
  shots_on_target: number;
  corners: number;
  fouls: number;
  yellow_cards: number;
  red_cards: number;
  offsides: number;
  passes: number;
  pass_accuracy: number;
}

export interface MatchTimer {
  current_minute: number;
  extra_time: number;
  is_running: boolean;
  period: 'first_half' | 'halftime' | 'second_half' | 'extra_time' | 'penalties' | 'finished';
  started_at?: string;
  paused_at?: string;
}

export interface Competition {
  id: string;
  name: string;
  short_name: string;
  logo_url?: string;
  season: string;
  type: 'league' | 'cup' | 'friendly';
}

export interface MatchFilters {
  competition_id?: string;
  status?: MatchStatus;
  is_home?: boolean;
  date_from?: string;
  date_to?: string;
  opponent?: string;
}

export interface MatchFormData {
  opponent_name: string;
  opponent_logo?: string;
  competition_id?: string;
  competition_name: string;
  date: string;
  time: string;
  location: string;
  is_home: boolean;
}

export interface CallupData {
  match_id: string;
  players: {
    athlete_id: string;
    role: 'starter' | 'substitute';
    position_id?: string;
  }[];
  technical_staff: {
    staff_id: string;
    role: string;
  }[];
}

export interface TacticalFormationData {
  match_id: string;
  formation_id: string;
  positions: {
    athlete_id: string;
    position_id: string;
    x: number;
    y: number;
  }[];
}

// Utilitários
export const getMatchStatusLabel = (status: MatchStatus): string => {
  switch (status) {
    case 'scheduled':
      return 'Agendada';
    case 'live':
      return 'Ao Vivo';
    case 'halftime':
      return 'Intervalo';
    case 'finished':
      return 'Finalizada';
    case 'postponed':
      return 'Adiada';
    case 'cancelled':
      return 'Cancelada';
    default:
      return status;
  }
};

export const getMatchStatusColor = (status: MatchStatus): string => {
  switch (status) {
    case 'scheduled':
      return '#2196f3';
    case 'live':
      return '#4caf50';
    case 'halftime':
      return '#ff9800';
    case 'finished':
      return '#9e9e9e';
    case 'postponed':
      return '#ff5722';
    case 'cancelled':
      return '#f44336';
    default:
      return '#9e9e9e';
  }
};

export const getEventIcon = (type: MatchEventType): string => {
  switch (type) {
    case 'goal':
    case 'penalty_goal':
      return 'sports-soccer';
    case 'own_goal':
      return 'sports-soccer';
    case 'yellow_card':
      return 'crop-portrait';
    case 'red_card':
      return 'crop-portrait';
    case 'substitution':
      return 'swap-horiz';
    case 'injury':
      return 'local-hospital';
    case 'corner':
      return 'flag';
    case 'free_kick':
      return 'sports-soccer';
    case 'penalty':
      return 'gps-fixed';
    case 'var_check':
      return 'video-camera-back';
    case 'kickoff':
      return 'play-arrow';
    case 'halftime':
      return 'pause';
    case 'fulltime':
      return 'stop';
    default:
      return 'event';
  }
};
