import { Athlete } from './athletes';

export interface MedicalRecord {
  id: string;
  athlete_id: string;
  athlete: Athlete;
  type: MedicalRecordType;
  title: string;
  description: string;
  date: string;
  professional_id?: string;
  professional_name?: string;
  professional_specialty?: string;
  status: MedicalStatus;
  priority: MedicalPriority;
  diagnosis?: string;
  treatment?: string;
  medications?: Medication[];
  follow_up_date?: string;
  file_urls?: string[];
  notes?: string;
  created_at: string;
  updated_at: string;
}

export type MedicalRecordType = 
  | 'consultation'     // Consulta
  | 'exam'            // Exame
  | 'injury'          // Lesão
  | 'treatment'       // Tratamento
  | 'surgery'         // Cirurgia
  | 'physiotherapy'   // Fisioterapia
  | 'vaccination'     // Vacinação
  | 'checkup';        // Check-up

export type MedicalStatus = 
  | 'scheduled'       // Agendado
  | 'in_progress'     // Em andamento
  | 'completed'       // Concluído
  | 'cancelled'       // Cancelado
  | 'postponed'       // Adiado
  | 'pending_results'; // Aguardando resultados

export type MedicalPriority = 
  | 'low'             // Baixa
  | 'medium'          // Média
  | 'high'            // Alta
  | 'urgent';         // Urgente

export interface Medication {
  id: string;
  name: string;
  dosage: string;
  frequency: string;
  duration: string;
  instructions?: string;
  start_date: string;
  end_date?: string;
  active: boolean;
}

export interface MedicalExam {
  id: string;
  athlete_id: string;
  athlete: Athlete;
  type: ExamType;
  name: string;
  description?: string;
  date: string;
  location: string;
  professional_name?: string;
  status: MedicalStatus;
  results?: ExamResult[];
  file_urls?: string[];
  notes?: string;
  created_at: string;
  updated_at: string;
}

export type ExamType = 
  | 'blood'           // Sangue
  | 'urine'           // Urina
  | 'xray'            // Raio-X
  | 'mri'             // Ressonância
  | 'ultrasound'      // Ultrassom
  | 'ct_scan'         // Tomografia
  | 'ecg'             // Eletrocardiograma
  | 'stress_test'     // Teste de esforço
  | 'physical_eval';  // Avaliação física

export interface ExamResult {
  id: string;
  parameter: string;
  value: string;
  reference_range: string;
  unit?: string;
  status: 'normal' | 'abnormal' | 'borderline';
  notes?: string;
}

export interface Injury {
  id: string;
  athlete_id: string;
  athlete: Athlete;
  type: InjuryType;
  location: InjuryLocation;
  severity: InjurySeverity;
  description: string;
  date_occurred: string;
  date_diagnosed?: string;
  expected_recovery?: string;
  actual_recovery?: string;
  status: InjuryStatus;
  treatment_plan?: string;
  physiotherapy_sessions?: PhysiotherapySession[];
  medical_clearance?: boolean;
  return_to_play_date?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export type InjuryType = 
  | 'muscle_strain'   // Distensão muscular
  | 'ligament'        // Ligamento
  | 'tendon'          // Tendão
  | 'bone_fracture'   // Fratura óssea
  | 'joint'           // Articulação
  | 'concussion'      // Concussão
  | 'cut_bruise'      // Corte/Contusão
  | 'overuse';        // Sobrecarga

export type InjuryLocation = 
  | 'head'            // Cabeça
  | 'neck'            // Pescoço
  | 'shoulder'        // Ombro
  | 'arm'             // Braço
  | 'elbow'           // Cotovelo
  | 'wrist'           // Punho
  | 'hand'            // Mão
  | 'chest'           // Peito
  | 'back'            // Costas
  | 'abdomen'         // Abdômen
  | 'hip'             // Quadril
  | 'thigh'           // Coxa
  | 'knee'            // Joelho
  | 'calf'            // Panturrilha
  | 'ankle'           // Tornozelo
  | 'foot';           // Pé

export type InjurySeverity = 
  | 'minor'           // Leve
  | 'moderate'        // Moderada
  | 'severe'          // Grave
  | 'critical';       // Crítica

export type InjuryStatus = 
  | 'active'          // Ativa
  | 'recovering'      // Recuperando
  | 'recovered'       // Recuperada
  | 'chronic';        // Crônica

export interface PhysiotherapySession {
  id: string;
  injury_id: string;
  date: string;
  duration: number; // em minutos
  exercises: string[];
  progress_notes: string;
  pain_level: number; // 1-10
  mobility_score: number; // 1-10
  therapist_name?: string;
}

export interface MedicalAppointment {
  id: string;
  athlete_id: string;
  athlete: Athlete;
  type: MedicalRecordType;
  title: string;
  description?: string;
  date: string;
  time: string;
  duration: number; // em minutos
  location: string;
  professional_name: string;
  professional_specialty?: string;
  status: MedicalStatus;
  priority: MedicalPriority;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface MedicalFilters {
  athlete_id?: string;
  type?: MedicalRecordType;
  status?: MedicalStatus;
  priority?: MedicalPriority;
  professional_id?: string;
  date_from?: string;
  date_to?: string;
}

export interface MedicalFormData {
  athlete_id: string;
  type: MedicalRecordType;
  title: string;
  description: string;
  date: string;
  professional_name?: string;
  professional_specialty?: string;
  priority: MedicalPriority;
  diagnosis?: string;
  treatment?: string;
  notes?: string;
}

export interface MedicalStatistics {
  total_records: number;
  active_injuries: number;
  pending_appointments: number;
  completed_exams: number;
  athletes_with_restrictions: number;
  most_common_injury_type: InjuryType;
  average_recovery_time: number;
  injury_by_location: {
    location: InjuryLocation;
    count: number;
  }[];
}

// Utilitários
export const getMedicalRecordTypeLabel = (type: MedicalRecordType): string => {
  switch (type) {
    case 'consultation':
      return 'Consulta';
    case 'exam':
      return 'Exame';
    case 'injury':
      return 'Lesão';
    case 'treatment':
      return 'Tratamento';
    case 'surgery':
      return 'Cirurgia';
    case 'physiotherapy':
      return 'Fisioterapia';
    case 'vaccination':
      return 'Vacinação';
    case 'checkup':
      return 'Check-up';
    default:
      return type;
  }
};

export const getMedicalRecordTypeColor = (type: MedicalRecordType): string => {
  switch (type) {
    case 'consultation':
      return '#2196f3';
    case 'exam':
      return '#9c27b0';
    case 'injury':
      return '#f44336';
    case 'treatment':
      return '#4caf50';
    case 'surgery':
      return '#ff5722';
    case 'physiotherapy':
      return '#00bcd4';
    case 'vaccination':
      return '#8bc34a';
    case 'checkup':
      return '#607d8b';
    default:
      return '#9e9e9e';
  }
};

export const getMedicalStatusLabel = (status: MedicalStatus): string => {
  switch (status) {
    case 'scheduled':
      return 'Agendado';
    case 'in_progress':
      return 'Em Andamento';
    case 'completed':
      return 'Concluído';
    case 'cancelled':
      return 'Cancelado';
    case 'postponed':
      return 'Adiado';
    case 'pending_results':
      return 'Aguardando Resultados';
    default:
      return status;
  }
};

export const getMedicalStatusColor = (status: MedicalStatus): string => {
  switch (status) {
    case 'scheduled':
      return '#2196f3';
    case 'in_progress':
      return '#4caf50';
    case 'completed':
      return '#9e9e9e';
    case 'cancelled':
      return '#f44336';
    case 'postponed':
      return '#ff9800';
    case 'pending_results':
      return '#9c27b0';
    default:
      return '#9e9e9e';
  }
};

export const getMedicalPriorityLabel = (priority: MedicalPriority): string => {
  switch (priority) {
    case 'low':
      return 'Baixa';
    case 'medium':
      return 'Média';
    case 'high':
      return 'Alta';
    case 'urgent':
      return 'Urgente';
    default:
      return priority;
  }
};

export const getMedicalPriorityColor = (priority: MedicalPriority): string => {
  switch (priority) {
    case 'low':
      return '#4caf50';
    case 'medium':
      return '#ff9800';
    case 'high':
      return '#f44336';
    case 'urgent':
      return '#9c27b0';
    default:
      return '#9e9e9e';
  }
};

export const getInjurySeverityLabel = (severity: InjurySeverity): string => {
  switch (severity) {
    case 'minor':
      return 'Leve';
    case 'moderate':
      return 'Moderada';
    case 'severe':
      return 'Grave';
    case 'critical':
      return 'Crítica';
    default:
      return severity;
  }
};

export const getInjurySeverityColor = (severity: InjurySeverity): string => {
  switch (severity) {
    case 'minor':
      return '#4caf50';
    case 'moderate':
      return '#ff9800';
    case 'severe':
      return '#f44336';
    case 'critical':
      return '#9c27b0';
    default:
      return '#9e9e9e';
  }
};
