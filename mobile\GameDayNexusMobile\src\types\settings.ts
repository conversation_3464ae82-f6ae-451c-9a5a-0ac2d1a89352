export interface ClubSettings {
  id: string;
  club_id: string;
  name: string;
  full_name: string;
  founded_year?: number;
  logo_url?: string;
  colors: ClubColors;
  address?: ClubAddress;
  contact: ClubContact;
  social_media?: SocialMedia;
  preferences: ClubPreferences;
  subscription: SubscriptionInfo;
  created_at: string;
  updated_at: string;
}

export interface ClubColors {
  primary: string;
  secondary: string;
  accent?: string;
  home_kit: KitColors;
  away_kit: KitColors;
  third_kit?: KitColors;
}

export interface KitColors {
  shirt: string;
  shorts: string;
  socks: string;
}

export interface ClubAddress {
  street: string;
  number: string;
  complement?: string;
  neighborhood: string;
  city: string;
  state: string;
  zip_code: string;
  country: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}

export interface ClubContact {
  phone?: string;
  email?: string;
  website?: string;
  whatsapp?: string;
}

export interface SocialMedia {
  facebook?: string;
  instagram?: string;
  twitter?: string;
  youtube?: string;
  tiktok?: string;
  linkedin?: string;
}

export interface ClubPreferences {
  timezone: string;
  language: string;
  currency: string;
  date_format: DateFormat;
  time_format: TimeFormat;
  week_start: WeekStart;
  fiscal_year_start: number; // Mês (1-12)
  season_start: number; // Mês (1-12)
  default_category?: string;
  auto_backup: boolean;
  backup_frequency: BackupFrequency;
  data_retention_days: number;
}

export type DateFormat = 'DD/MM/YYYY' | 'MM/DD/YYYY' | 'YYYY-MM-DD';
export type TimeFormat = '12h' | '24h';
export type WeekStart = 'sunday' | 'monday';
export type BackupFrequency = 'daily' | 'weekly' | 'monthly';

export interface SubscriptionInfo {
  plan: SubscriptionPlan;
  status: SubscriptionStatus;
  started_at: string;
  expires_at?: string;
  features: SubscriptionFeatures;
  limits: SubscriptionLimits;
}

export type SubscriptionPlan = 'free' | 'basic' | 'premium' | 'enterprise';
export type SubscriptionStatus = 'active' | 'expired' | 'cancelled' | 'trial';

export interface SubscriptionFeatures {
  max_athletes: number;
  max_users: number;
  max_storage_gb: number;
  advanced_reports: boolean;
  api_access: boolean;
  custom_branding: boolean;
  priority_support: boolean;
  backup_retention_days: number;
}

export interface SubscriptionLimits {
  current_athletes: number;
  current_users: number;
  current_storage_gb: number;
}

export interface AppSettings {
  user_id: string;
  theme: AppTheme;
  language: string;
  notifications: NotificationSettings;
  privacy: PrivacySettings;
  accessibility: AccessibilitySettings;
  performance: PerformanceSettings;
  sync: SyncSettings;
  created_at: string;
  updated_at: string;
}

export type AppTheme = 'light' | 'dark' | 'auto';

export interface NotificationSettings {
  enabled: boolean;
  push_notifications: boolean;
  email_notifications: boolean;
  sms_notifications: boolean;
  sound_enabled: boolean;
  vibration_enabled: boolean;
  quiet_hours: QuietHours;
  categories: NotificationCategories;
}

export interface QuietHours {
  enabled: boolean;
  start_time: string; // HH:mm
  end_time: string; // HH:mm
}

export interface NotificationCategories {
  matches: boolean;
  training: boolean;
  medical: boolean;
  financial: boolean;
  inventory: boolean;
  reports: boolean;
  system: boolean;
}

export interface PrivacySettings {
  analytics_enabled: boolean;
  crash_reporting_enabled: boolean;
  location_sharing_enabled: boolean;
  contact_sync_enabled: boolean;
  data_sharing_enabled: boolean;
}

export interface AccessibilitySettings {
  font_size: FontSize;
  high_contrast: boolean;
  reduce_motion: boolean;
  screen_reader_enabled: boolean;
  voice_commands_enabled: boolean;
}

export type FontSize = 'small' | 'medium' | 'large' | 'extra_large';

export interface PerformanceSettings {
  image_quality: ImageQuality;
  animation_enabled: boolean;
  auto_sync_enabled: boolean;
  cache_size_mb: number;
  offline_mode_enabled: boolean;
}

export type ImageQuality = 'low' | 'medium' | 'high' | 'original';

export interface SyncSettings {
  auto_sync: boolean;
  sync_frequency: SyncFrequency;
  wifi_only: boolean;
  background_sync: boolean;
  last_sync?: string;
  sync_status: SyncStatus;
}

export type SyncFrequency = 'real_time' | 'every_5min' | 'every_15min' | 'every_hour' | 'manual';
export type SyncStatus = 'synced' | 'syncing' | 'error' | 'offline';

export interface BackupSettings {
  auto_backup: boolean;
  backup_frequency: BackupFrequency;
  include_images: boolean;
  include_documents: boolean;
  cloud_provider: CloudProvider;
  encryption_enabled: boolean;
  last_backup?: string;
  backup_size_mb?: number;
}

export type CloudProvider = 'google_drive' | 'icloud' | 'dropbox' | 'onedrive' | 'local';

export interface SecuritySettings {
  two_factor_enabled: boolean;
  biometric_enabled: boolean;
  auto_lock_enabled: boolean;
  auto_lock_timeout: number; // minutes
  session_timeout: number; // hours
  password_policy: PasswordPolicy;
  login_attempts_limit: number;
  device_trust_enabled: boolean;
}

export interface PasswordPolicy {
  min_length: number;
  require_uppercase: boolean;
  require_lowercase: boolean;
  require_numbers: boolean;
  require_symbols: boolean;
  expiry_days?: number;
}

export interface SystemInfo {
  app_version: string;
  build_number: string;
  platform: string;
  os_version: string;
  device_model: string;
  device_id: string;
  install_date: string;
  last_update: string;
  storage_used_mb: number;
  storage_available_mb: number;
}

export interface SettingsSection {
  id: string;
  title: string;
  description?: string;
  icon: string;
  items: SettingsItem[];
}

export interface SettingsItem {
  id: string;
  type: SettingsItemType;
  title: string;
  description?: string;
  icon?: string;
  value?: any;
  options?: SettingsOption[];
  action?: () => void;
  disabled?: boolean;
  badge?: string;
}

export type SettingsItemType = 
  | 'toggle'
  | 'select'
  | 'text'
  | 'number'
  | 'color'
  | 'action'
  | 'navigation'
  | 'info';

export interface SettingsOption {
  label: string;
  value: any;
  description?: string;
}

// Utilitários
export const getSubscriptionPlanLabel = (plan: SubscriptionPlan): string => {
  switch (plan) {
    case 'free':
      return 'Gratuito';
    case 'basic':
      return 'Básico';
    case 'premium':
      return 'Premium';
    case 'enterprise':
      return 'Empresarial';
    default:
      return plan;
  }
};

export const getSubscriptionStatusLabel = (status: SubscriptionStatus): string => {
  switch (status) {
    case 'active':
      return 'Ativo';
    case 'expired':
      return 'Expirado';
    case 'cancelled':
      return 'Cancelado';
    case 'trial':
      return 'Teste';
    default:
      return status;
  }
};

export const getSubscriptionStatusColor = (status: SubscriptionStatus): string => {
  switch (status) {
    case 'active':
      return '#4caf50';
    case 'expired':
      return '#f44336';
    case 'cancelled':
      return '#9e9e9e';
    case 'trial':
      return '#ff9800';
    default:
      return '#9e9e9e';
  }
};

export const getThemeLabel = (theme: AppTheme): string => {
  switch (theme) {
    case 'light':
      return 'Claro';
    case 'dark':
      return 'Escuro';
    case 'auto':
      return 'Automático';
    default:
      return theme;
  }
};

export const getFontSizeLabel = (size: FontSize): string => {
  switch (size) {
    case 'small':
      return 'Pequeno';
    case 'medium':
      return 'Médio';
    case 'large':
      return 'Grande';
    case 'extra_large':
      return 'Extra Grande';
    default:
      return size;
  }
};

export const getImageQualityLabel = (quality: ImageQuality): string => {
  switch (quality) {
    case 'low':
      return 'Baixa';
    case 'medium':
      return 'Média';
    case 'high':
      return 'Alta';
    case 'original':
      return 'Original';
    default:
      return quality;
  }
};

export const getSyncFrequencyLabel = (frequency: SyncFrequency): string => {
  switch (frequency) {
    case 'real_time':
      return 'Tempo Real';
    case 'every_5min':
      return 'A cada 5 min';
    case 'every_15min':
      return 'A cada 15 min';
    case 'every_hour':
      return 'A cada hora';
    case 'manual':
      return 'Manual';
    default:
      return frequency;
  }
};

export const getSyncStatusLabel = (status: SyncStatus): string => {
  switch (status) {
    case 'synced':
      return 'Sincronizado';
    case 'syncing':
      return 'Sincronizando';
    case 'error':
      return 'Erro';
    case 'offline':
      return 'Offline';
    default:
      return status;
  }
};

export const getSyncStatusColor = (status: SyncStatus): string => {
  switch (status) {
    case 'synced':
      return '#4caf50';
    case 'syncing':
      return '#2196f3';
    case 'error':
      return '#f44336';
    case 'offline':
      return '#9e9e9e';
    default:
      return '#9e9e9e';
  }
};

export const formatStorageSize = (sizeInMB: number): string => {
  if (sizeInMB < 1024) {
    return `${sizeInMB.toFixed(1)} MB`;
  }
  return `${(sizeInMB / 1024).toFixed(1)} GB`;
};

export const calculateStoragePercentage = (used: number, total: number): number => {
  if (total === 0) return 0;
  return Math.min((used / total) * 100, 100);
};
