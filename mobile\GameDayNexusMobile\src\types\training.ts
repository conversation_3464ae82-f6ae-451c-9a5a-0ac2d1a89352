import { Athlete } from './athletes';

export interface Training {
  id: string;
  club_id: string;
  title: string;
  description?: string;
  date: string;
  start_time: string;
  end_time: string;
  location: string;
  category_id?: string;
  category_name?: string;
  coach_id?: string;
  coach_name?: string;
  type: TrainingType;
  status: TrainingStatus;
  objectives?: string[];
  exercises?: Exercise[];
  attendance?: TrainingAttendance[];
  notes?: string;
  weather?: WeatherCondition;
  created_at: string;
  updated_at: string;
}

export type TrainingType = 
  | 'technical'      // Técnico
  | 'tactical'       // Tático
  | 'physical'       // Físico
  | 'recovery'       // Recuperação
  | 'friendly'       // Jogo-treino
  | 'evaluation'     // Avaliação
  | 'integration';   // Integração

export type TrainingStatus = 
  | 'scheduled'      // Agendado
  | 'in_progress'    // Em andamento
  | 'completed'      // Concluído
  | 'cancelled'      // Cancelado
  | 'postponed';     // Adiado

export interface Exercise {
  id: string;
  name: string;
  description: string;
  duration: number; // em minutos
  intensity: ExerciseIntensity;
  category: ExerciseCategory;
  equipment?: string[];
  instructions?: string;
  video_url?: string;
  image_url?: string;
  order: number;
}

export type ExerciseIntensity = 'low' | 'medium' | 'high' | 'maximum';

export type ExerciseCategory = 
  | 'warm_up'        // Aquecimento
  | 'technical'      // Técnico
  | 'tactical'       // Tático
  | 'physical'       // Físico
  | 'coordination'   // Coordenação
  | 'finishing'      // Finalização
  | 'passing'        // Passe
  | 'dribbling'      // Drible
  | 'defending'      // Defesa
  | 'set_pieces'     // Bolas paradas
  | 'cool_down';     // Volta à calma

export interface TrainingAttendance {
  id: string;
  training_id: string;
  athlete_id: string;
  athlete: Athlete;
  status: AttendanceStatus;
  arrival_time?: string;
  departure_time?: string;
  notes?: string;
  performance_rating?: number; // 1-5
  created_at: string;
  updated_at: string;
}

export type AttendanceStatus = 
  | 'present'        // Presente
  | 'absent'         // Ausente
  | 'late'           // Atrasado
  | 'excused'        // Justificado
  | 'injured'        // Lesionado
  | 'suspended';     // Suspenso

export interface WeatherCondition {
  temperature: number;
  humidity: number;
  condition: 'sunny' | 'cloudy' | 'rainy' | 'windy';
  description?: string;
}

export interface TrainingPlan {
  id: string;
  name: string;
  description: string;
  duration_weeks: number;
  exercises: Exercise[];
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface TrainingFilters {
  category_id?: string;
  type?: TrainingType;
  status?: TrainingStatus;
  coach_id?: string;
  date_from?: string;
  date_to?: string;
  location?: string;
}

export interface TrainingFormData {
  title: string;
  description?: string;
  date: string;
  start_time: string;
  end_time: string;
  location: string;
  category_id?: string;
  coach_id?: string;
  type: TrainingType;
  objectives?: string[];
  notes?: string;
}

export interface AttendanceFormData {
  training_id: string;
  attendances: {
    athlete_id: string;
    status: AttendanceStatus;
    arrival_time?: string;
    notes?: string;
    performance_rating?: number;
  }[];
}

export interface TrainingStatistics {
  total_trainings: number;
  completed_trainings: number;
  cancelled_trainings: number;
  average_attendance: number;
  most_common_type: TrainingType;
  total_hours: number;
  attendance_by_athlete: {
    athlete_id: string;
    athlete_name: string;
    total_trainings: number;
    present_count: number;
    attendance_rate: number;
  }[];
}

// Utilitários
export const getTrainingTypeLabel = (type: TrainingType): string => {
  switch (type) {
    case 'technical':
      return 'Técnico';
    case 'tactical':
      return 'Tático';
    case 'physical':
      return 'Físico';
    case 'recovery':
      return 'Recuperação';
    case 'friendly':
      return 'Jogo-treino';
    case 'evaluation':
      return 'Avaliação';
    case 'integration':
      return 'Integração';
    default:
      return type;
  }
};

export const getTrainingTypeColor = (type: TrainingType): string => {
  switch (type) {
    case 'technical':
      return '#2196f3';
    case 'tactical':
      return '#9c27b0';
    case 'physical':
      return '#f44336';
    case 'recovery':
      return '#4caf50';
    case 'friendly':
      return '#ff9800';
    case 'evaluation':
      return '#795548';
    case 'integration':
      return '#607d8b';
    default:
      return '#9e9e9e';
  }
};

export const getTrainingStatusLabel = (status: TrainingStatus): string => {
  switch (status) {
    case 'scheduled':
      return 'Agendado';
    case 'in_progress':
      return 'Em Andamento';
    case 'completed':
      return 'Concluído';
    case 'cancelled':
      return 'Cancelado';
    case 'postponed':
      return 'Adiado';
    default:
      return status;
  }
};

export const getTrainingStatusColor = (status: TrainingStatus): string => {
  switch (status) {
    case 'scheduled':
      return '#2196f3';
    case 'in_progress':
      return '#4caf50';
    case 'completed':
      return '#9e9e9e';
    case 'cancelled':
      return '#f44336';
    case 'postponed':
      return '#ff9800';
    default:
      return '#9e9e9e';
  }
};

export const getAttendanceStatusLabel = (status: AttendanceStatus): string => {
  switch (status) {
    case 'present':
      return 'Presente';
    case 'absent':
      return 'Ausente';
    case 'late':
      return 'Atrasado';
    case 'excused':
      return 'Justificado';
    case 'injured':
      return 'Lesionado';
    case 'suspended':
      return 'Suspenso';
    default:
      return status;
  }
};

export const getAttendanceStatusColor = (status: AttendanceStatus): string => {
  switch (status) {
    case 'present':
      return '#4caf50';
    case 'absent':
      return '#f44336';
    case 'late':
      return '#ff9800';
    case 'excused':
      return '#2196f3';
    case 'injured':
      return '#9c27b0';
    case 'suspended':
      return '#795548';
    default:
      return '#9e9e9e';
  }
};

export const getExerciseIntensityLabel = (intensity: ExerciseIntensity): string => {
  switch (intensity) {
    case 'low':
      return 'Baixa';
    case 'medium':
      return 'Média';
    case 'high':
      return 'Alta';
    case 'maximum':
      return 'Máxima';
    default:
      return intensity;
  }
};

export const getExerciseIntensityColor = (intensity: ExerciseIntensity): string => {
  switch (intensity) {
    case 'low':
      return '#4caf50';
    case 'medium':
      return '#ff9800';
    case 'high':
      return '#f44336';
    case 'maximum':
      return '#9c27b0';
    default:
      return '#9e9e9e';
  }
};
