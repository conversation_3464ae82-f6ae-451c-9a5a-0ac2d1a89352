-- Script de instalação do Sistema de Chat
-- Execute este script no seu banco Supabase

-- 1. <PERSON><PERSON><PERSON> tabelas
\i sql/chat_system.sql

-- 2. Verificar se as tabelas foram criadas
SELECT 
  schemaname,
  tablename 
FROM pg_tables 
WHERE tablename IN ('chat_rooms', 'chat_messages', 'chat_room_participants', 'user_presence')
ORDER BY tablename;

-- 3. Verificar políticas RLS
SELECT 
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual
FROM pg_policies 
WHERE tablename IN ('chat_rooms', 'chat_messages', 'chat_room_participants', 'user_presence')
ORDER BY tablename, policyname;

-- 4. Veri<PERSON>r triggers
SELECT 
  trigger_name,
  event_manipulation,
  event_object_table,
  action_statement
FROM information_schema.triggers 
WHERE event_object_table IN ('clubs', 'user_clubs')
  AND trigger_name LIKE '%chat%'
ORDER BY event_object_table, trigger_name;

-- 5. <PERSON><PERSON>r sala geral para clubes existentes (se necessário)
INSERT INTO chat_rooms (club_id, name, description, is_general)
SELECT 
  id,
  'Geral',
  'Sala geral do clube',
  true
FROM clubs
WHERE id NOT IN (
  SELECT club_id 
  FROM chat_rooms 
  WHERE is_general = true
);

-- 6. Adicionar usuários existentes nas salas gerais
INSERT INTO chat_room_participants (room_id, user_id)
SELECT DISTINCT
  cr.id as room_id,
  uc.user_id
FROM chat_rooms cr
JOIN user_clubs uc ON uc.club_id = cr.club_id
WHERE cr.is_general = true
  AND NOT EXISTS (
    SELECT 1 
    FROM chat_room_participants crp 
    WHERE crp.room_id = cr.id 
      AND crp.user_id = uc.user_id
  );

-- 7. Criar registros de presença para usuários existentes
INSERT INTO user_presence (user_id, club_id, status)
SELECT DISTINCT
  uc.user_id,
  uc.club_id,
  'offline'
FROM user_clubs uc
WHERE NOT EXISTS (
  SELECT 1 
  FROM user_presence up 
  WHERE up.user_id = uc.user_id
);

-- 8. Verificar instalação
SELECT 
  'chat_rooms' as tabela,
  COUNT(*) as registros
FROM chat_rooms
UNION ALL
SELECT 
  'chat_messages' as tabela,
  COUNT(*) as registros
FROM chat_messages
UNION ALL
SELECT 
  'chat_room_participants' as tabela,
  COUNT(*) as registros
FROM chat_room_participants
UNION ALL
SELECT 
  'user_presence' as tabela,
  COUNT(*) as registros
FROM user_presence
ORDER BY tabela;

-- Mensagem de sucesso
SELECT 'Sistema de Chat instalado com sucesso!' as status;