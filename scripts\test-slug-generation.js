// Script para testar a geração de slugs
// Execute com: node scripts/test-slug-generation.js

function generateSlugFromName(name) {
  // Mapa de caracteres acentuados para não acentuados
  const accentMap = {
    'á': 'a', 'à': 'a', 'ã': 'a', 'â': 'a', 'ä': 'a',
    'é': 'e', 'è': 'e', 'ê': 'e', 'ë': 'e',
    'í': 'i', 'ì': 'i', 'î': 'i', 'ï': 'i',
    'ó': 'o', 'ò': 'o', 'õ': 'o', 'ô': 'o', 'ö': 'o',
    'ú': 'u', 'ù': 'u', 'û': 'u', 'ü': 'u',
    'ç': 'c', 'ñ': 'n',
    'Á': 'A', 'À': 'A', 'Ã': 'A', 'Â': 'A', 'Ä': 'A',
    'É': 'E', 'È': 'E', 'Ê': 'E', 'Ë': 'E',
    'Í': 'I', 'Ì': 'I', 'Î': 'I', 'Ï': 'I',
    'Ó': 'O', 'Ò': 'O', 'Õ': 'O', 'Ô': 'O', 'Ö': 'O',
    'Ú': 'U', 'Ù': 'U', 'Û': 'U', 'Ü': 'U',
    'Ç': 'C', 'Ñ': 'N'
  };

  return name
    .toLowerCase()
    // Remover acentos usando o mapa
    .replace(/[áàãâäéèêëíìîïóòõôöúùûüçñÁÀÃÂÄÉÈÊËÍÌÎÏÓÒÕÔÖÚÙÛÜÇÑ]/g, (match) => accentMap[match] || match)
    // Remove caracteres especiais, mantém apenas letras, números, espaços e hífens
    .replace(/[^a-z0-9\s-]/g, '')
    // Substitui múltiplos espaços por um hífen
    .replace(/\s+/g, '-')
    // Remove hífens consecutivos
    .replace(/-+/g, '-')
    // Remove hífens do início e fim
    .replace(/^-+|-+$/g, '')
    .trim();
}

// Testes
const testCases = [
  'Sport Club Corinthians Paulista',
  'Sociedade Esportiva Palmeiras',
  'Clube de Regatas do Flamengo',
  'Grêmio Foot-Ball Porto Alegrense',
  'São Paulo Futebol Clube',
  'Clube Atlético Mineiro',
  'Botafogo de Futebol e Regatas',
  'Sport Club Internacional',
  'Cruzeiro Esporte Clube',
  'Santos Futebol Clube'
];

console.log('🧪 Testando geração de slugs:\n');

testCases.forEach(name => {
  const slug = generateSlugFromName(name);
  console.log(`${name.padEnd(35)} → ${slug}`);
});

console.log('\n✅ Todos os testes concluídos!');