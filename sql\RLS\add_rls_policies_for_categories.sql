-- Enable RLS on categories and player_categories tables

-- Enable RLS on categories table
ALTER TABLE public.categories ENABLE ROW LEVEL SECURITY;

-- Allow club members to view categories
CREATE POLICY "Club members can view categories"
ON public.categories
FOR SELECT
USING (
  club_id = get_current_club_id()
);

-- Allow club members to insert categories
CREATE POLICY "Club members can insert categories"
ON public.categories
FOR INSERT
WITH CHECK (
  club_id = get_current_club_id()
);

-- Allow club members to update categories
CREATE POLICY "Club members can update categories"
ON public.categories
FOR UPDATE
USING (
  club_id = get_current_club_id()
)
WITH CHECK (
  club_id = get_current_club_id()
);

-- Allow club members to delete categories
CREATE POLICY "Club members can delete categories"
ON public.categories
FOR DELETE
USING (
  club_id = get_current_club_id()
);

-- Enable RLS on player_categories table
ALTER TABLE public.player_categories ENABLE ROW LEVEL SECURITY;

-- Allow club members to view player_categories
CREATE POLICY "Club members can view player_categories"
ON public.player_categories
FOR SELECT
USING (
  club_id = get_current_club_id()
);

-- Allow club members to insert player_categories
CREATE POLICY "Club members can insert player_categories"
ON public.player_categories
FOR INSERT
WITH CHECK (
  club_id = get_current_club_id()
);

-- Allow club members to update player_categories
CREATE POLICY "Club members can update player_categories"
ON public.player_categories
FOR UPDATE
USING (
  club_id = get_current_club_id()
)
WITH CHECK (
  club_id = get_current_club_id()
);

-- Allow club members to delete player_categories
CREATE POLICY "Club members can delete player_categories"
ON public.player_categories
FOR DELETE
USING (
  club_id = get_current_club_id()
);

-- Grant access to authenticated users
GRANT ALL ON public.categories TO authenticated;
GRANT ALL ON public.player_categories TO authenticated;
