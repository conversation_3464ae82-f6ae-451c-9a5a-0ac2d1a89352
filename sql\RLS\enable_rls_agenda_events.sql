-- Enable RLS and add policies for agenda_events table
alter table public.agenda_events enable row level security;

create policy "Allow select for club members" on public.agenda_events
  for select
  using (club_id in (select club_id from public.club_members where user_id = auth.uid()));

create policy "Allow insert for club members" on public.agenda_events
  for insert
  with check (club_id in (select club_id from public.club_members where user_id = auth.uid()));

create policy "Allow update for club members" on public.agenda_events
  for update
  using (club_id in (select club_id from public.club_members where user_id = auth.uid()));

create policy "Allow delete for club members" on public.agenda_events
  for delete
  using (club_id in (select club_id from public.club_members where user_id = auth.uid()));