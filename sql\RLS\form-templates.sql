-- Enable Row Level Security and define policies for club isolation
ALTER TABLE club_form_templates ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Users can view form templates from their club" ON club_form_templates;
DROP POLICY IF EXISTS "Users can insert form templates for their club" ON club_form_templates;
DROP POLICY IF EXISTS "Users can update form templates from their club" ON club_form_templates;
DROP POLICY IF EXISTS "Users can delete form templates from their club" ON club_form_templates;

CREATE POLICY "Users can view form templates from their club" ON club_form_templates
  FOR SELECT
  USING (club_id = get_current_club_id());

CREATE POLICY "Users can insert form templates for their club" ON club_form_templates
  FOR INSERT
  WITH CHECK (club_id = get_current_club_id());

CREATE POLICY "Users can update form templates from their club" ON club_form_templates
  FOR UPDATE
  USING (club_id = get_current_club_id())
  WITH CHECK (club_id = get_current_club_id());

CREATE POLICY "Users can delete form templates from their club" ON club_form_templates
  FOR DELETE
  USING (club_id = get_current_club_id());