-- Add organization_id column and RLS policies for master_notification_logs
ALTER TABLE master_notification_logs
ADD COLUMN IF NOT EXISTS organization_id INTEGER REFERENCES master_organizations(id);

-- Ensure RLS is enabled
ALTER TABLE master_notification_logs ENABLE ROW LEVEL SECURITY;

-- Remove old policies if they exist
DROP POLICY IF EXISTS "master_users_can_view_notification_logs" ON master_notification_logs;
DROP POLICY IF EXISTS "system_can_modify_notification_logs" ON master_notification_logs;
DROP POLICY IF EXISTS "allow_authenticated_master_notification_logs" ON master_notification_logs;

-- Allow members of an organization or super admins to view logs for their organization
CREATE POLICY "organization_members_can_view_notification_logs" ON master_notification_logs
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM master_users mu
      WHERE mu.id = auth.uid()
      AND mu.is_active = true
      AND (mu.role = 'super_admin' OR mu.organization_id = master_notification_logs.organization_id)
    )
  );

-- Allow system-level inserts and updates without restriction
CREATE POLICY "system_can_modify_notification_logs" ON master_notification_logs
  FOR ALL
  USING (true)
  WITH CHECK (true);