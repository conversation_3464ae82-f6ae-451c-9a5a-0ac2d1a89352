-- =====================================================
-- SCRIPT: Add RLS policies for master support tables
-- DESCRIPTION: Enables row level security and adds policies per organization
-- =====================================================

-- Ensure helper function for master user check exists
CREATE OR REPLACE FUNCTION is_master_user()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM master_users
    WHERE id = auth.uid()
      AND is_active = true
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

GRANT EXECUTE ON FUNCTION is_master_user() TO authenticated;

-- Enable RLS on support tables
ALTER TABLE master_support_tickets ENABLE ROW LEVEL SECURITY;
ALTER TABLE master_support_messages ENABLE ROW LEVEL SECURITY;

-- Clubs can manage their own support tickets
CREATE POLICY "clubs_manage_own_support_tickets" ON master_support_tickets
  FOR ALL
  USING (club_id = (auth.jwt() ->> 'club_id')::INTEGER)
  WITH CHECK (club_id = (auth.jwt() ->> 'club_id')::INTEGER);

-- Master users can view all support tickets
CREATE POLICY "master_view_all_support_tickets" ON master_support_tickets
  FOR SELECT
  TO authenticated
  USING (is_master_user());

-- Master users can modify all support tickets
CREATE POLICY "master_modify_all_support_tickets" ON master_support_tickets
  FOR ALL
  TO authenticated
  USING (is_master_user())
  WITH CHECK (is_master_user());

-- Clubs can manage messages for their own tickets
CREATE POLICY "clubs_manage_own_support_messages" ON master_support_messages
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM master_support_tickets t
      WHERE t.id = master_support_messages.ticket_id
        AND t.club_id = (auth.jwt() ->> 'club_id')::INTEGER
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM master_support_tickets t
      WHERE t.id = master_support_messages.ticket_id
        AND t.club_id = (auth.jwt() ->> 'club_id')::INTEGER
    )
  );

-- Master users can manage all support messages
CREATE POLICY "master_manage_all_support_messages" ON master_support_messages
  FOR ALL
  TO authenticated
  USING (is_master_user())
  WITH CHECK (is_master_user());