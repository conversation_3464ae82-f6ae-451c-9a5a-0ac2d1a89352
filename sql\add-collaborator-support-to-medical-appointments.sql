-- Adici<PERSON><PERSON> suporte a colaboradores na tabela medical_appointments
-- Isso permite agendar consultas médicas tanto para jogadores quanto para colaboradores

-- 1. Adicionar colunas para suportar colaboradores
ALTER TABLE medical_appointments 
ADD COLUMN IF NOT EXISTS collaborator_id INTEGER REFERENCES collaborators(id),
ADD COLUMN IF NOT EXISTS patient_type TEXT DEFAULT 'player' CHECK (patient_type IN ('player', 'collaborator'));

-- 1.1. Remover a constraint NOT NULL da coluna player_id para permitir valores nulos
ALTER TABLE medical_appointments 
ALTER COLUMN player_id DROP NOT NULL;

-- 2. Modificar a constraint para permitir player_id OU collaborator_id
ALTER TABLE medical_appointments 
DROP CONSTRAINT IF EXISTS check_patient_type;

ALTER TABLE medical_appointments 
ADD CONSTRAINT check_patient_type CHECK (
  (patient_type = 'player' AND player_id IS NOT NULL AND collaborator_id IS NULL) OR 
  (patient_type = 'collaborator' AND player_id IS NULL AND collaborator_id IS NOT NULL)
);

-- 3. Atualizar registros existentes para definir patient_type como 'player'
UPDATE medical_appointments 
SET patient_type = 'player' 
WHERE patient_type IS NULL AND player_id IS NOT NULL;

-- 4. Criar índices para melhor performance
CREATE INDEX IF NOT EXISTS idx_medical_appointments_collaborator 
ON medical_appointments(club_id, collaborator_id);

CREATE INDEX IF NOT EXISTS idx_medical_appointments_patient_type 
ON medical_appointments(club_id, patient_type);

-- 5. Comentários para documentação
COMMENT ON COLUMN medical_appointments.collaborator_id IS 'ID do colaborador quando o paciente é um colaborador';
COMMENT ON COLUMN medical_appointments.patient_type IS 'Tipo de paciente: player (jogador) ou collaborator (colaborador)';
COMMENT ON CONSTRAINT check_patient_type ON medical_appointments IS 'Garante que apenas um tipo de paciente seja especificado: jogador ou colaborador';

-- 6. Atualizar a função de busca de agendamentos (se existir)
-- Esta parte será implementada no código da aplicação