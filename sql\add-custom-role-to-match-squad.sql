-- Adici<PERSON>r coluna custom_role à tabela match_squad
-- Execute este script no Supabase SQL Editor

-- 1. Adicionar a coluna custom_role
ALTER TABLE match_squad 
ADD COLUMN IF NOT EXISTS custom_role TEXT;

-- 2. Adici<PERSON>r coment<PERSON><PERSON> para documentação
COMMENT ON COLUMN match_squad.custom_role IS 'Função personalizada do membro na partida (ex: "Técnico Principal", "Preparador Físico", etc.)';

-- 3. <PERSON><PERSON><PERSON> índice para melhor performance em consultas por custom_role
CREATE INDEX IF NOT EXISTS idx_match_squad_custom_role ON match_squad(club_id, match_id, custom_role);

-- 4. Verificar se a coluna foi adicionada corretamente
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'match_squad' 
AND column_name = 'custom_role';