-- Adicionar colunas para assinatura digital nas ocorrências
-- Executar este script para adicionar suporte à assinatura digital

-- Adicionar colunas para assinatura digital
ALTER TABLE player_occurrences 
ADD COLUMN IF NOT EXISTS signed_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS signed_by_user_id UUID REFERENCES auth.users(id),
ADD COLUMN IF NOT EXISTS signed_by_name VARCHAR(255),
ADD COLUMN IF NOT EXISTS signed_by_role VARCHAR(100),
ADD COLUMN IF NOT EXISTS digital_signature_hash VARCHAR(512);

-- Criar índice para melhor performance nas consultas
CREATE INDEX IF NOT EXISTS idx_player_occurrences_signed_by 
ON player_occurrences(signed_by_user_id);

CREATE INDEX IF NOT EXISTS idx_player_occurrences_signed_at 
ON player_occurrences(signed_at);

-- Comentários para documentação
COMMENT ON COLUMN player_occurrences.signed_at IS 'Data e hora da assinatura digital';
COMMENT ON COLUMN player_occurrences.signed_by_user_id IS 'ID do usuário que assinou digitalmente';
COMMENT ON COLUMN player_occurrences.signed_by_name IS 'Nome do usuário que assinou';
COMMENT ON COLUMN player_occurrences.signed_by_role IS 'Função/cargo do usuário que assinou';
COMMENT ON COLUMN player_occurrences.digital_signature_hash IS 'Hash da assinatura digital para verificação de integridade';

-- Atualizar a política RLS se necessário (opcional)
-- DROP POLICY IF EXISTS "Users can view occurrences based on permissions" ON player_occurrences;
-- CREATE POLICY "Users can view occurrences based on permissions" ON player_occurrences
--   FOR SELECT USING (
--     EXISTS (
--       SELECT 1 FROM user_permissions up
--       WHERE up.user_id = auth.uid()
--       AND up.permission_name IN ('players.occurrences.view', 'players.occurrences.edit')
--     )
--   );