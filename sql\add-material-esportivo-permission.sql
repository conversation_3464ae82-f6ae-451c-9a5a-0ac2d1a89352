-- Script para adicionar a nova permissão do departamento Material Esportivo
-- e atualizar as permissões dos usuários admin e president

-- 1. Inserir a nova permissão na tabela permissions se não existir
INSERT INTO permissions (name, description, category)
VALUES ('inventory.departments.material_esportivo', 'Visualizar departamento Material Esportivo', 'inventory')
ON CONFLICT (name) DO NOTHING;

-- 2. Atualizar as permissões dos usuários com role 'admin' para incluir a nova permissão
UPDATE club_members 
SET permissions = permissions || '{"inventory.departments.material_esportivo": true}'::jsonb
WHERE role = 'admin';

-- 3. Atualizar as permissões dos usuários com role 'president' para incluir a nova permissão
UPDATE club_members 
SET permissions = permissions || '{"inventory.departments.material_esportivo": true}'::jsonb
WHERE role = 'president';

-- 4. Veri<PERSON><PERSON> se as permissões foram atualizadas corretamente
SELECT 
  cm.user_id,
  cm.role,
  cm.permissions->'inventory.departments.material_esportivo' as material_esportivo_permission
FROM club_members cm
WHERE cm.role IN ('admin', 'president')
  AND cm.permissions ? 'inventory.departments.material_esportivo';