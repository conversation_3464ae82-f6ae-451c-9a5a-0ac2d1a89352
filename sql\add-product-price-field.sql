-- Adicionar campo de preço para produtos de venda
ALTER TABLE inventory_products 
ADD COLUMN IF NOT EXISTS sale_price DECIMAL(10,2) DEFAULT 0;

-- Adicionar comentário no campo
COMMENT ON COLUMN inventory_products.sale_price IS 'Preço de venda do produto (usado apenas para departamento "A Venda")';

-- <PERSON><PERSON>r índice para melhorar performance em consultas de produtos de venda
CREATE INDEX IF NOT EXISTS idx_inventory_products_department_sale ON inventory_products(department) WHERE department = 'A Venda';