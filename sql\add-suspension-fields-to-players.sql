-- <PERSON><PERSON><PERSON> tabela para controle de suspensões por competição/categoria
CREATE TABLE IF NOT EXISTS player_suspensions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  club_id INTEGER NOT NULL REFERENCES club_info(id) ON DELETE CASCADE,
  player_id UUID NOT NULL REFERENCES players(id) ON DELETE CASCADE,
  competition_id UUID REFERENCES competitions(id) ON DELETE CASCADE,
  category_id INTEGER REFERENCES categories(id) ON DELETE CASCADE,
  reason TEXT NOT NULL,
  matches_remaining INTEGER NOT NULL DEFAULT 1,
  original_matches INTEGER NOT NULL DEFAULT 1,
  suspended_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- <PERSON><PERSON><PERSON><PERSON> que não há suspensões duplicadas para o mesmo contexto
  UNIQUE(club_id, player_id, competition_id, category_id)
);

-- <PERSON><PERSON><PERSON> índices para performance
CREATE INDEX IF NOT EXISTS idx_player_suspensions_club_player 
ON player_suspensions(club_id, player_id);

CREATE INDEX IF NOT EXISTS idx_player_suspensions_competition 
ON player_suspensions(club_id, competition_id) 
WHERE competition_id IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_player_suspensions_category 
ON player_suspensions(club_id, category_id) 
WHERE category_id IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_player_suspensions_active 
ON player_suspensions(club_id, player_id) 
WHERE matches_remaining > 0;

-- Função para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_player_suspensions_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger para atualizar updated_at
DROP TRIGGER IF EXISTS trigger_update_player_suspensions_updated_at ON player_suspensions;
CREATE TRIGGER trigger_update_player_suspensions_updated_at
  BEFORE UPDATE ON player_suspensions
  FOR EACH ROW
  EXECUTE FUNCTION update_player_suspensions_updated_at();

-- Comentários para documentação
COMMENT ON TABLE player_suspensions IS 'Controle de suspensões de jogadores por competição/categoria';
COMMENT ON COLUMN player_suspensions.competition_id IS 'ID da competição (NULL = suspensão geral)';
COMMENT ON COLUMN player_suspensions.category_id IS 'ID da categoria (NULL = suspensão geral)';
COMMENT ON COLUMN player_suspensions.reason IS 'Motivo da suspensão (cartão vermelho, segundo amarelo, etc.)';
COMMENT ON COLUMN player_suspensions.matches_remaining IS 'Número de partidas restantes de suspensão';
COMMENT ON COLUMN player_suspensions.original_matches IS 'Número original de partidas da suspensão';

-- Remover campos antigos da tabela players se existirem
ALTER TABLE players 
DROP COLUMN IF EXISTS suspension_reason,
DROP COLUMN IF EXISTS suspension_matches_remaining,
DROP COLUMN IF EXISTS suspended_at;