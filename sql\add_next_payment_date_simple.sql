-- =====================================================
-- SCRIPT SIMPLES: Adicionar campo next_payment_date
-- DESCRIÇÃO: Versão simplificada para evitar conflitos
-- VERSÃO: 1.0
-- DATA: 2025-08-07
-- =====================================================

-- Adicionar colunas necessárias
DO $$ 
BEGIN
    -- Adicionar next_payment_date
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'club_info' 
        AND column_name = 'next_payment_date'
    ) THEN
        ALTER TABLE club_info ADD COLUMN next_payment_date DATE;
        RAISE NOTICE 'Coluna next_payment_date adicionada';
    END IF;
    
    -- Adicionar last_payment_date se não existir
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'club_info' 
        AND column_name = 'last_payment_date'
    ) THEN
        ALTER TABLE club_info ADD COLUMN last_payment_date DATE;
        RAISE NOTICE 'Coluna last_payment_date adicionada';
    END IF;
END $$;

-- Definir datas padrão para clubes ativos sem trial
UPDATE club_info 
SET 
    last_payment_date = CURRENT_DATE,
    next_payment_date = CURRENT_DATE + INTERVAL '1 month'
WHERE subscription_status = 'active'
  AND is_trial = FALSE
  AND next_payment_date IS NULL;

-- Para testes, definir próximo pagamento em 5 dias para ver o aviso
-- UPDATE club_info 
-- SET next_payment_date = CURRENT_DATE + INTERVAL '5 days'
-- WHERE subscription_status = 'active' AND is_trial = FALSE;

RAISE NOTICE 'Migração concluída com sucesso!';