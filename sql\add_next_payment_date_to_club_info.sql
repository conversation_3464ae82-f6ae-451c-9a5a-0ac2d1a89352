-- =====================================================
-- SCRIPT: Adicionar campo next_payment_date à tabela club_info
-- DESCRIÇÃO: Adiciona campo para controlar data do próximo pagamento
-- VERSÃO: 1.0
-- DATA: 2025-08-07
-- =====================================================

-- Adicionar colunas necessárias se não existirem
DO $$ 
BEGIN
    -- Adicionar next_payment_date
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'club_info' 
        AND column_name = 'next_payment_date'
    ) THEN
        ALTER TABLE club_info 
        ADD COLUMN next_payment_date DATE;
        
        COMMENT ON COLUMN club_info.next_payment_date IS 'Data do próximo pagamento da mensalidade';
        RAISE NOTICE 'Coluna next_payment_date adicionada com sucesso à tabela club_info';
    ELSE
        RAISE NOTICE 'Coluna next_payment_date já existe na tabela club_info';
    END IF;
    
    -- Adicionar last_payment_date se não existir
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'club_info' 
        AND column_name = 'last_payment_date'
    ) THEN
        ALTER TABLE club_info 
        ADD COLUMN last_payment_date DATE;
        
        COMMENT ON COLUMN club_info.last_payment_date IS 'Data do último pagamento realizado';
        RAISE NOTICE 'Coluna last_payment_date adicionada com sucesso à tabela club_info';
    ELSE
        RAISE NOTICE 'Coluna last_payment_date já existe na tabela club_info';
    END IF;
END $$;

-- Remover função existente se houver conflito
DROP FUNCTION IF EXISTS calculate_next_payment_date(INTEGER, VARCHAR);

-- Função para calcular próxima data de pagamento
CREATE OR REPLACE FUNCTION calculate_next_payment_date(
    p_club_id INTEGER,
    p_billing_cycle VARCHAR(20) DEFAULT 'monthly'
)
RETURNS DATE AS $$
DECLARE
    v_last_payment_date DATE;
    v_next_payment_date DATE;
BEGIN
    -- Buscar última data de pagamento
    SELECT last_payment_date INTO v_last_payment_date
    FROM club_info
    WHERE id = p_club_id;
    
    -- Se não há data de último pagamento, usar data atual
    IF v_last_payment_date IS NULL THEN
        v_last_payment_date := CURRENT_DATE;
    END IF;
    
    -- Calcular próxima data baseada no ciclo
    IF p_billing_cycle = 'monthly' THEN
        v_next_payment_date := v_last_payment_date + INTERVAL '1 month';
    ELSIF p_billing_cycle = 'yearly' THEN
        v_next_payment_date := v_last_payment_date + INTERVAL '1 year';
    ELSE
        -- Default para mensal
        v_next_payment_date := v_last_payment_date + INTERVAL '1 month';
    END IF;
    
    RETURN v_next_payment_date;
END;
$$ LANGUAGE plpgsql;

-- Atualizar clubes ativos com próxima data de pagamento
UPDATE club_info 
SET next_payment_date = calculate_next_payment_date(
    club_info.id, 
    COALESCE(master_plans.billing_cycle, 'monthly')
)
FROM master_plans
WHERE club_info.master_plan_id = master_plans.id
  AND club_info.subscription_status = 'active'
  AND club_info.is_trial = FALSE
  AND club_info.next_payment_date IS NULL;

-- Função para atualizar automaticamente next_payment_date quando um pagamento é feito
CREATE OR REPLACE FUNCTION update_next_payment_date_on_payment()
RETURNS TRIGGER AS $$
DECLARE
    v_billing_cycle VARCHAR(20);
BEGIN
    -- Buscar ciclo de cobrança do plano
    SELECT billing_cycle INTO v_billing_cycle
    FROM master_plans mp
    JOIN club_info ci ON ci.master_plan_id = mp.id
    WHERE ci.id = NEW.club_id;
    
    -- Atualizar próxima data de pagamento
    UPDATE club_info
    SET 
        next_payment_date = calculate_next_payment_date(NEW.club_id, v_billing_cycle),
        last_payment_date = CURRENT_DATE,
        payment_status = 'current',
        updated_at = NOW()
    WHERE id = NEW.club_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Criar trigger para atualizar next_payment_date quando pagamento é marcado como pago
DROP TRIGGER IF EXISTS trigger_update_next_payment_date ON master_payments;
CREATE TRIGGER trigger_update_next_payment_date
    AFTER UPDATE OF status ON master_payments
    FOR EACH ROW
    WHEN (OLD.status != 'paid' AND NEW.status = 'paid')
    EXECUTE FUNCTION update_next_payment_date_on_payment();

RAISE NOTICE 'Script executado com sucesso! Campo next_payment_date adicionado e configurado.';