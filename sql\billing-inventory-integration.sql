-- Integração entre sistema de cobrança e estoque
-- Tabela para vincular transações de cobrança com itens do estoque

CREATE TABLE IF NOT EXISTS billing_transaction_items (
  id SERIAL PRIMARY KEY,
  club_id INTEGER NOT NULL REFERENCES club_info(id) ON DELETE CASCADE,
  transaction_id INTEGER NOT NULL REFERENCES billing_transactions(id) ON DELETE CASCADE,
  product_id INTEGER NOT NULL REFERENCES inventory_products(id) ON DELETE CASCADE,
  quantity INTEGER NOT NULL CHECK (quantity > 0),
  unit_price DECIMAL(10,2), -- Preço unitário do produto na transação
  total_price DECIMAL(10,2), -- Preço total (quantity * unit_price)
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_billing_transaction_items_club_id ON billing_transaction_items(club_id);
CREATE INDEX IF NOT EXISTS idx_billing_transaction_items_transaction_id ON billing_transaction_items(transaction_id);
CREATE INDEX IF NOT EXISTS idx_billing_transaction_items_product_id ON billing_transaction_items(product_id);

-- RLS (Row Level Security)
ALTER TABLE billing_transaction_items ENABLE ROW LEVEL SECURITY;

-- Políticas RLS para billing_transaction_items
DROP POLICY IF EXISTS "Users can view billing transaction items from their club" ON billing_transaction_items;
DROP POLICY IF EXISTS "Users can insert billing transaction items in their club" ON billing_transaction_items;
DROP POLICY IF EXISTS "Users can update billing transaction items from their club" ON billing_transaction_items;
DROP POLICY IF EXISTS "Users can delete billing transaction items from their club" ON billing_transaction_items;

CREATE POLICY "Users can view billing transaction items from their club" ON billing_transaction_items
  FOR SELECT USING (
    club_id IN (
      SELECT club_id FROM club_members 
      WHERE user_id = auth.uid() AND status = 'ativo'
    )
  );

CREATE POLICY "Users can insert billing transaction items in their club" ON billing_transaction_items
  FOR INSERT WITH CHECK (
    club_id IN (
      SELECT club_id FROM club_members 
      WHERE user_id = auth.uid() AND status = 'ativo'
    )
  );

CREATE POLICY "Users can update billing transaction items from their club" ON billing_transaction_items
  FOR UPDATE USING (
    club_id IN (
      SELECT club_id FROM club_members 
      WHERE user_id = auth.uid() AND status = 'ativo'
    )
  );

CREATE POLICY "Users can delete billing transaction items from their club" ON billing_transaction_items
  FOR DELETE USING (
    club_id IN (
      SELECT club_id FROM club_members 
      WHERE user_id = auth.uid() AND status = 'ativo'
    )
  );

-- Trigger para atualizar updated_at
CREATE TRIGGER update_billing_transaction_items_updated_at BEFORE UPDATE ON billing_transaction_items
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Função para processar itens do estoque quando transação é marcada como paga
CREATE OR REPLACE FUNCTION process_billing_transaction_inventory(
  p_club_id INTEGER,
  p_transaction_id INTEGER,
  p_user_id UUID
) RETURNS BOOLEAN AS $$$
DECLARE
  v_item RECORD;
  v_product_quantity INTEGER;
BEGIN
  -- Verificar se a transação existe e está sendo marcada como paga
  IF NOT EXISTS (
    SELECT 1 FROM billing_transactions 
    WHERE id = p_transaction_id AND club_id = p_club_id AND status = 'pago'
  ) THEN
    RAISE EXCEPTION 'Transação não encontrada ou não está marcada como paga';
  END IF;

  -- Processar cada item da transação
  FOR v_item IN 
    SELECT bti.product_id, bti.quantity, ip.name as product_name, ip.quantity as available_quantity
    FROM billing_transaction_items bti
    JOIN inventory_products ip ON ip.id = bti.product_id
    WHERE bti.transaction_id = p_transaction_id AND bti.club_id = p_club_id
  LOOP
    -- Verificar se há estoque suficiente
    IF v_item.available_quantity < v_item.quantity THEN
      RAISE EXCEPTION 'Estoque insuficiente para o produto "%". Disponível: %, Solicitado: %', 
        v_item.product_name, v_item.available_quantity, v_item.quantity;
    END IF;

    -- Debitar do estoque usando a função existente
    PERFORM update_inventory_product_quantity(
      p_club_id,
      v_item.product_id,
      v_item.quantity,
      'saida',
      p_user_id,
      'Saída automática - Transação de cobrança #' || p_transaction_id
    );
  END LOOP;

  RETURN TRUE;
END;
$$$ LANGUAGE plpgsql;

-- Comentários nas tabelas
COMMENT ON TABLE billing_transaction_items IS 'Itens do estoque vinculados a transações de cobrança';
COMMENT ON COLUMN billing_transaction_items.unit_price IS 'Preço unitário do produto na transação';
COMMENT ON COLUMN billing_transaction_items.total_price IS 'Preço total do item (quantity * unit_price)';