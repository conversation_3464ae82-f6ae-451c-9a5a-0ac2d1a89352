-- Function to change a club's plan
CREATE OR REPLACE FUNCTION change_club_plan(
  p_change_reason TEXT,
  p_club_id INTEGER,
  p_new_plan_id INTEGER
)
RETURNS VOID AS $$
DECLARE
  v_old_plan_id INTEGER;
  v_billing_cycle VARCHAR(20);
BEGIN
  -- Verify club exists
  SELECT master_plan_id INTO v_old_plan_id
  FROM club_info
  WHERE id = p_club_id;
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Clube % não encontrado', p_club_id;
  END IF;

  -- Verify new plan exists and is active
  SELECT billing_cycle INTO v_billing_cycle
  FROM master_plans
  WHERE id = p_new_plan_id AND is_active = TRUE;
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Plano % não encontrado ou inativo', p_new_plan_id;
  END IF;

  -- Update club with new plan and next payment date
  UPDATE club_info
  SET
    master_plan_id = p_new_plan_id,
    next_payment_date = calculate_next_payment_date(p_club_id, v_billing_cycle),
    updated_at = NOW()
  WHERE id = p_club_id;

  -- Audit log
  INSERT INTO master_audit_logs (
    action,
    entity_type,
    entity_id,
    old_values,
    new_values,
    details,
    created_at
  ) VALUES (
    'club_plan_changed',
    'club',
    p_club_id,
    jsonb_build_object('plan_id', v_old_plan_id),
    jsonb_build_object('plan_id', p_new_plan_id),
    jsonb_build_object('reason', p_change_reason),
    NOW()
  );
END;
$$ LANGUAGE plpgsql;