-- Chat System Tables
-- <PERSON>ria<PERSON> das tabelas para o sistema de chat

-- Tabela de salas de chat (por clube)
CREATE TABLE IF NOT EXISTS chat_rooms (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  club_id INTEGER NOT NULL,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  is_general BOOLEAN DEFAULT false, -- sala geral do clube
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(club_id, name) -- <PERSON><PERSON>tar salas duplicadas no mesmo clube
);

-- Tabel<PERSON> de mensagens
CREATE TABLE IF NOT EXISTS chat_messages (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  room_id UUID NOT NULL REFERENCES chat_rooms(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  message_type VARCHAR(50) DEFAULT 'text', -- text, image, file, system
  metadata JSONB, -- para anexos, menções, etc
  reply_to UUID REFERENCES chat_messages(id), -- para respostas
  edited_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de participantes das salas
CREATE TABLE IF NOT EXISTS chat_room_participants (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  room_id UUID NOT NULL REFERENCES chat_rooms(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_read_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_admin BOOLEAN DEFAULT false,
  UNIQUE(room_id, user_id)
);

-- Tabela de usuários online
CREATE TABLE IF NOT EXISTS user_presence (
  user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  club_id INTEGER NOT NULL,
  status VARCHAR(20) DEFAULT 'online', -- online, away, busy, offline
  last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_chat_messages_room_id ON chat_messages(room_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_created_at ON chat_messages(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_chat_room_participants_room_id ON chat_room_participants(room_id);
CREATE INDEX IF NOT EXISTS idx_chat_room_participants_user_id ON chat_room_participants(user_id);
CREATE INDEX IF NOT EXISTS idx_user_presence_club_id ON user_presence(club_id);
CREATE INDEX IF NOT EXISTS idx_chat_rooms_club_id ON chat_rooms(club_id);

-- RLS Policies
ALTER TABLE chat_rooms ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_room_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_presence ENABLE ROW LEVEL SECURITY;

-- Políticas para chat_rooms
CREATE POLICY "Users can view rooms from their club" ON chat_rooms
  FOR SELECT USING (
    club_id IN (
      SELECT club_id FROM club_members 
      WHERE user_id = auth.uid() AND status = 'ativo'
    )
  );

CREATE POLICY "Users can create rooms in their club" ON chat_rooms
  FOR INSERT WITH CHECK (
    club_id IN (
      SELECT club_id FROM club_members 
      WHERE user_id = auth.uid() AND status = 'ativo'
    )
  );

-- Políticas para chat_messages
CREATE POLICY "Users can view messages from rooms they participate" ON chat_messages
  FOR SELECT USING (
    room_id IN (
      SELECT room_id FROM chat_room_participants 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert messages in rooms they participate" ON chat_messages
  FOR INSERT WITH CHECK (
    room_id IN (
      SELECT room_id FROM chat_room_participants 
      WHERE user_id = auth.uid()
    ) AND user_id = auth.uid()
  );

CREATE POLICY "Users can update their own messages" ON chat_messages
  FOR UPDATE USING (user_id = auth.uid());

-- Políticas para chat_room_participants
CREATE POLICY "Users can view participants from their rooms" ON chat_room_participants
  FOR SELECT USING (
    room_id IN (
      SELECT room_id FROM chat_room_participants 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can join rooms from their club" ON chat_room_participants
  FOR INSERT WITH CHECK (
    room_id IN (
      SELECT id FROM chat_rooms 
      WHERE club_id IN (
        SELECT club_id FROM club_members 
        WHERE user_id = auth.uid()
      )
    ) AND user_id = auth.uid()
  );

-- Políticas para user_presence
CREATE POLICY "Users can view presence from their club" ON user_presence
  FOR SELECT USING (
    club_id IN (
      SELECT club_id FROM club_members 
      WHERE user_id = auth.uid() AND status = 'ativo'
    )
  );

CREATE POLICY "Users can update their own presence" ON user_presence
  FOR ALL USING (user_id = auth.uid());

-- Função para criar sala geral automaticamente quando um membro é adicionado ao clube
CREATE OR REPLACE FUNCTION ensure_general_chat_room()
RETURNS TRIGGER AS $$
BEGIN
  -- Criar sala geral se não existir
  INSERT INTO chat_rooms (club_id, name, description, is_general)
  VALUES (NEW.club_id, 'Geral', 'Sala geral do clube', true)
  ON CONFLICT (club_id, name) DO NOTHING;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger para criar sala geral quando necessário
CREATE TRIGGER trigger_ensure_general_chat_room
  AFTER INSERT ON club_members
  FOR EACH ROW
  EXECUTE FUNCTION ensure_general_chat_room();

-- Função para adicionar usuário automaticamente na sala geral
CREATE OR REPLACE FUNCTION add_user_to_general_room()
RETURNS TRIGGER AS $$
BEGIN
  -- Adiciona o usuário na sala geral do clube
  INSERT INTO chat_room_participants (room_id, user_id)
  SELECT cr.id, NEW.user_id
  FROM chat_rooms cr
  WHERE cr.club_id = NEW.club_id AND cr.is_general = true
  ON CONFLICT (room_id, user_id) DO NOTHING;
  
  -- Cria/atualiza presença do usuário
  INSERT INTO user_presence (user_id, club_id, status)
  VALUES (NEW.user_id, NEW.club_id, 'offline')
  ON CONFLICT (user_id) DO UPDATE SET
    club_id = NEW.club_id,
    status = 'offline',
    updated_at = NOW();
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger para adicionar usuário na sala geral
CREATE TRIGGER trigger_add_user_to_general_room
  AFTER INSERT ON club_members
  FOR EACH ROW
  EXECUTE FUNCTION add_user_to_general_room();

-- Função para atualizar timestamp de mensagens não lidas
CREATE OR REPLACE FUNCTION update_last_read()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE chat_room_participants 
  SET last_read_at = NOW()
  WHERE room_id = NEW.room_id AND user_id = auth.uid();
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Criar salas gerais para clubes existentes (executar apenas uma vez)
DO $$
DECLARE
    club_record RECORD;
BEGIN
    -- Para cada clube que existe, criar uma sala geral se não existir
    FOR club_record IN 
        SELECT DISTINCT club_id 
        FROM club_members 
        WHERE status = 'ativo'
    LOOP
        INSERT INTO chat_rooms (club_id, name, description, is_general)
        VALUES (club_record.club_id, 'Geral', 'Sala geral do clube', true)
        ON CONFLICT (club_id, name) DO NOTHING;
    END LOOP;
END $$;

-- Adicionar usuários existentes nas salas gerais
DO $$
DECLARE
    member_record RECORD;
BEGIN
    FOR member_record IN 
        SELECT cm.user_id, cm.club_id
        FROM club_members cm
        WHERE cm.status = 'ativo'
    LOOP
        -- Adicionar na sala geral
        INSERT INTO chat_room_participants (room_id, user_id)
        SELECT cr.id, member_record.user_id
        FROM chat_rooms cr
        WHERE cr.club_id = member_record.club_id AND cr.is_general = true
        ON CONFLICT (room_id, user_id) DO NOTHING;
        
        -- Criar registro de presença
        INSERT INTO user_presence (user_id, club_id, status)
        VALUES (member_record.user_id, member_record.club_id, 'offline')
        ON CONFLICT (user_id) DO UPDATE SET
            club_id = member_record.club_id,
            status = 'offline',
            updated_at = NOW();
    END LOOP;
END $$;