-- =====================================================
-- SCRIPT: Verificar status das tabelas master
-- DESCRIÇÃO: Diagnóstico completo das tabelas master
-- =====================================================

-- 1. Verificar se as tabelas existem
SELECT 
    table_name,
    table_type
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name LIKE 'master_%'
ORDER BY table_name;

-- 2. Verificar status do RLS
SELECT 
    schemaname,
    tablename,
    rowsecurity as rls_enabled,
    (SELECT count(*) FROM pg_policies WHERE tablename = t.tablename) as policies_count
FROM pg_tables t
WHERE schemaname = 'public' 
AND tablename LIKE 'master_%'
ORDER BY tablename;

-- 3. Verificar políticas existentes
SELECT 
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE tablename LIKE 'master_%'
ORDER BY tablename, policyname;

-- 4. Verificar dados nas tabelas
SELECT 'master_organizations' as tabela, count(*) as registros FROM master_organizations
UNION ALL
SELECT 'master_users' as tabela, count(*) as registros FROM master_users
UNION ALL
SELECT 'master_plans' as tabela, count(*) as registros FROM master_plans
UNION ALL
SELECT 'master_payments' as tabela, count(*) as registros FROM master_payments;

-- 5. Verificar estrutura da tabela master_users
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'master_users' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- 6. Verificar alguns registros de exemplo (sem dados sensíveis)
SELECT 
    id,
    email,
    role,
    is_active,
    organization_id,
    created_at
FROM master_users 
LIMIT 3;

-- 7. Verificar organizações
SELECT 
    id,
    name,
    email,
    is_active,
    created_at
FROM master_organizations 
LIMIT 3;

-- 8. Testar permissões básicas
DO $
DECLARE
    test_result TEXT;
BEGIN
    -- Testar se consegue fazer SELECT
    BEGIN
        PERFORM count(*) FROM master_users;
        test_result := 'SELECT em master_users: OK';
    EXCEPTION WHEN OTHERS THEN
        test_result := 'SELECT em master_users: ERRO - ' || SQLERRM;
    END;
    
    RAISE NOTICE '%', test_result;
    
    -- Testar se consegue fazer SELECT em master_organizations
    BEGIN
        PERFORM count(*) FROM master_organizations;
        test_result := 'SELECT em master_organizations: OK';
    EXCEPTION WHEN OTHERS THEN
        test_result := 'SELECT em master_organizations: ERRO - ' || SQLERRM;
    END;
    
    RAISE NOTICE '%', test_result;
END $;

RAISE NOTICE 'Verificação das tabelas master concluída!';