-- =====================================================
-- SCRIPT SQL PARA LIMPAR DESCRIÇÕES DE SALÁRIOS
-- Remove " - NOME" das descrições onde category = 'salários'
-- =====================================================

-- Limpar descrições de transações de salários para colaboradores
UPDATE financial_transactions 
SET description = TRIM(SUBSTRING(description FROM 1 FOR POSITION(' - ' IN description) - 1))
WHERE collaborator_id IS NOT NULL 
  AND category = 'salários'
  AND description LIKE '% - %'
  AND POSITION(' - ' IN description) > 0;

-- Limpar descrições de transações de salários para jogadores
UPDATE financial_transactions 
SET description = TRIM(SUBSTRING(description FROM 1 FOR POSITION(' - ' IN description) - 1))
WHERE player_id IS NOT NULL 
  AND category = 'salários'
  AND description LIKE '% - %'
  AND POSITION(' - ' IN description) > 0;

-- Limpar descrições de transações de salários para médicos
UPDATE financial_transactions 
SET description = TRIM(SUBSTRING(description FROM 1 FOR POSITION(' - ' IN description) - 1))
WHERE medical_professional_id IS NOT NULL 
  AND category = 'salários'
  AND description LIKE '% - %'
  AND POSITION(' - ' IN description) > 0;

-- Verificar o resultado (opcional - para conferir antes de aplicar)
SELECT 
  id,
  category,
  description,
  CASE 
    WHEN collaborator_id IS NOT NULL THEN 'colaborador'
    WHEN player_id IS NOT NULL THEN 'jogador'
    WHEN medical_professional_id IS NOT NULL THEN 'médico'
    ELSE 'outro'
  END as tipo_pessoa
FROM financial_transactions 
WHERE category = 'salários'
  AND (collaborator_id IS NOT NULL OR player_id IS NOT NULL OR medical_professional_id IS NOT NULL)
ORDER BY id DESC
LIMIT 20;

-- =====================================================
-- RESULTADO ESPERADO:
-- "mensalidade - RAISSA" → "mensalidade"
-- "salário - JOÃO SILVA" → "salário"
-- =====================================================