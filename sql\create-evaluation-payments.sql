-- Create evaluation payments table
CREATE TABLE IF NOT EXISTS evaluation_payments (
    id SERIAL PRIMARY KEY,
    club_id INTEGER NOT NULL REFERENCES club_info(id) ON DELETE CASCADE,
    player_id UUID NOT NULL REFERENCES players(id) ON DELETE CASCADE,
    player_evaluation_invitation_id INTEGER REFERENCES player_evaluation_invitations(id) ON DELETE SET NULL,
    
    -- Payment details
    amount DECIMAL(10,2) NOT NULL,
    period_description TEXT NOT NULL,
    pix_key TEXT NOT NULL,
    pix_code TEXT, -- Generated PIX code
    qr_code_data TEXT, -- QR code data URL
    
    -- Payment tracking
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'paid', 'cancelled')),
    payment_token UUID NOT NULL DEFAULT gen_random_uuid(), -- Unique token for receipt upload
    receipt_file_url TEXT, -- URL of uploaded receipt
    receipt_uploaded_at TIMESTAMPTZ,
    verified_by UUID REFERENCES auth.users(id),
    verified_at TIMESTAMPTZ,
    verification_notes TEXT,
    
    -- <PERSON><PERSON><PERSON>
    created_by UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(club_id, player_id, created_at), -- Prevent duplicate payments for same player on same day
    CONSTRAINT valid_amount CHECK (amount > 0)
);

-- Add RLS policies
ALTER TABLE evaluation_payments ENABLE ROW LEVEL SECURITY;

-- Policy for club members to manage their club's evaluation payments
CREATE POLICY "Club members can manage evaluation payments" ON evaluation_payments
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM club_members 
            WHERE club_members.club_id = evaluation_payments.club_id 
            AND club_members.user_id = auth.uid()
        )
    );

-- Policy for public access using payment token (for receipt upload)
CREATE POLICY "Public can upload receipt with valid token" ON evaluation_payments
    FOR UPDATE USING (
        -- Allow update if payment is pending and has no receipt yet
        status = 'pending' AND receipt_file_url IS NULL
    );

-- Policy for public read access with payment token
CREATE POLICY "Public can read payment with valid token" ON evaluation_payments
    FOR SELECT USING (true);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_evaluation_payments_club_id ON evaluation_payments(club_id);
CREATE INDEX IF NOT EXISTS idx_evaluation_payments_player_id ON evaluation_payments(player_id);
CREATE INDEX IF NOT EXISTS idx_evaluation_payments_token ON evaluation_payments(payment_token);
CREATE INDEX IF NOT EXISTS idx_evaluation_payments_status ON evaluation_payments(status);

-- Add comments
COMMENT ON TABLE evaluation_payments IS 'Payments for player evaluation periods';
COMMENT ON COLUMN evaluation_payments.payment_token IS 'Unique token for receipt upload without authentication';
COMMENT ON COLUMN evaluation_payments.period_description IS 'Description of the evaluation period (e.g., "15 a 30 de Janeiro de 2025")';
COMMENT ON COLUMN evaluation_payments.pix_code IS 'Generated PIX payment code';
COMMENT ON COLUMN evaluation_payments.qr_code_data IS 'QR code data URL for PIX payment';

-- Add trigger for updated_at
CREATE OR REPLACE FUNCTION update_evaluation_payments_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_evaluation_payments_updated_at
    BEFORE UPDATE ON evaluation_payments
    FOR EACH ROW
    EXECUTE FUNCTION update_evaluation_payments_updated_at();