-- =====================================================
-- SCRIPT: Criação da Tabela de Relatórios Master
-- DESCRIÇÃO: Cria tabela para armazenar relatórios gerados
-- =====================================================

-- Tabela de relatórios master
CREATE TABLE IF NOT EXISTS master_reports (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  type VARCHAR(50) NOT NULL CHECK (type IN ('financial', 'clubs', 'users', 'system')),
  status VARCHAR(20) NOT NULL DEFAULT 'generating' CHECK (status IN ('generating', 'ready', 'error')),
  file_path TEXT,
  file_size BIGINT,
  parameters JSONB DEFAULT '{}',
  error_message TEXT,
  generated_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE
);

-- Índices
CREATE INDEX IF NOT EXISTS idx_master_reports_type ON master_reports(type);
CREATE INDEX IF NOT EXISTS idx_master_reports_status ON master_reports(status);
CREATE INDEX IF NOT EXISTS idx_master_reports_created_at ON master_reports(created_at);
CREATE INDEX IF NOT EXISTS idx_master_reports_generated_by ON master_reports(generated_by);

-- Trigger para updated_at
CREATE TRIGGER update_master_reports_completed_at 
    BEFORE UPDATE ON master_reports
    FOR EACH ROW 
    WHEN (NEW.status = 'ready' OR NEW.status = 'error')
    EXECUTE FUNCTION update_updated_at_column();

-- Comentários
COMMENT ON TABLE master_reports IS 'Relatórios gerados no sistema master';
COMMENT ON COLUMN master_reports.parameters IS 'Parâmetros usados para gerar o relatório (filtros, período, etc.)';
COMMENT ON COLUMN master_reports.file_size IS 'Tamanho do arquivo em bytes';

-- Inserir alguns relatórios de exemplo
INSERT INTO master_reports (name, description, type, status, file_size, parameters, completed_at) VALUES
('Relatório Financeiro - Janeiro 2025', 'Receitas, pagamentos e inadimplência de Janeiro', 'financial', 'ready', 2457600, '{"month": "2025-01", "include_overdue": true}', NOW() - INTERVAL '2 hours'),
('Análise de Clubes Ativos', 'Status e performance dos clubes cadastrados', 'clubs', 'ready', 1887436, '{"status": "active", "include_metrics": true}', NOW() - INTERVAL '1 day'),
('Relatório de Usuários Master', 'Atividade e permissões dos usuários do sistema', 'users', 'generating', NULL, '{"include_permissions": true}', NULL),
('Log de Auditoria Semanal', 'Ações realizadas no sistema na última semana', 'system', 'ready', 5368709, '{"period": "7_days", "include_details": true}', NOW() - INTERVAL '3 days'),
('Análise de Churn - Q4 2024', 'Clubes cancelados e motivos de cancelamento', 'clubs', 'error', NULL, '{"quarter": "Q4_2024"}', NOW() - INTERVAL '5 days');

RAISE NOTICE 'Tabela master_reports criada com sucesso!';