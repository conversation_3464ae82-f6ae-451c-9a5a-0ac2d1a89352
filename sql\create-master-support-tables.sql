-- =====================================================
-- SCRIPT: Criação das Tabelas de Suporte Master
-- DESCRIÇÃO: Cria tabelas para sistema de suporte
-- =====================================================

-- Tabela de tickets de suporte
CREATE TABLE IF NOT EXISTS master_support_tickets (
  id SERIAL PRIMARY KEY,
  club_id INTEGER NOT NULL REFERENCES club_info(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  description TEXT NOT NULL,
  status VARCHAR(20) NOT NULL DEFAULT 'open' CHECK (status IN ('open', 'in_progress', 'resolved', 'closed')),
  priority VARCHAR(20) NOT NULL DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
  category VARCHAR(50) NOT NULL DEFAULT 'other' CHECK (category IN ('technical', 'billing', 'feature', 'bug', 'other')),
  assigned_to UUID REFERENCES master_users(id) ON DELETE SET NULL,
  customer_email VARCHAR(255) NOT NULL,
  customer_phone VARCHAR(20),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  resolved_at TIMESTAMP WITH TIME ZONE
);

-- Tabela de mensagens dos tickets
CREATE TABLE IF NOT EXISTS master_support_messages (
  id SERIAL PRIMARY KEY,
  ticket_id INTEGER NOT NULL REFERENCES master_support_tickets(id) ON DELETE CASCADE,
  sender_type VARCHAR(20) NOT NULL CHECK (sender_type IN ('customer', 'support')),
  sender_name VARCHAR(100) NOT NULL,
  sender_email VARCHAR(255),
  message TEXT NOT NULL,
  attachments JSONB DEFAULT '[]',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices
CREATE INDEX IF NOT EXISTS idx_master_support_tickets_club_id ON master_support_tickets(club_id);
CREATE INDEX IF NOT EXISTS idx_master_support_tickets_status ON master_support_tickets(status);
CREATE INDEX IF NOT EXISTS idx_master_support_tickets_priority ON master_support_tickets(priority);
CREATE INDEX IF NOT EXISTS idx_master_support_tickets_category ON master_support_tickets(category);
CREATE INDEX IF NOT EXISTS idx_master_support_tickets_assigned_to ON master_support_tickets(assigned_to);
CREATE INDEX IF NOT EXISTS idx_master_support_tickets_created_at ON master_support_tickets(created_at);

CREATE INDEX IF NOT EXISTS idx_master_support_messages_ticket_id ON master_support_messages(ticket_id);
CREATE INDEX IF NOT EXISTS idx_master_support_messages_created_at ON master_support_messages(created_at);

-- Triggers para updated_at
CREATE TRIGGER update_master_support_tickets_updated_at 
    BEFORE UPDATE ON master_support_tickets
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Trigger para resolved_at
CREATE OR REPLACE FUNCTION update_resolved_at()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.status = 'resolved' AND OLD.status != 'resolved' THEN
        NEW.resolved_at = NOW();
    ELSIF NEW.status != 'resolved' THEN
        NEW.resolved_at = NULL;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_master_support_tickets_resolved_at 
    BEFORE UPDATE ON master_support_tickets
    FOR EACH ROW EXECUTE FUNCTION update_resolved_at();

-- Comentários
COMMENT ON TABLE master_support_tickets IS 'Tickets de suporte dos clubes';
COMMENT ON TABLE master_support_messages IS 'Mensagens dos tickets de suporte';

-- Inserir dados de exemplo
INSERT INTO master_support_tickets (club_id, title, description, status, priority, category, customer_email, customer_phone) 
SELECT 
    c.id,
    CASE 
        WHEN c.id % 4 = 0 THEN 'Erro ao gerar relatório financeiro'
        WHEN c.id % 4 = 1 THEN 'Dúvida sobre cobrança'
        WHEN c.id % 4 = 2 THEN 'Solicitação de nova funcionalidade'
        ELSE 'Problema no sistema de escalação'
    END,
    CASE 
        WHEN c.id % 4 = 0 THEN 'Não consigo gerar o relatório financeiro do mês. Aparece erro 500.'
        WHEN c.id % 4 = 1 THEN 'Gostaria de entender por que foi cobrado um valor diferente este mês.'
        WHEN c.id % 4 = 2 THEN 'Gostaria de solicitar a implementação de um módulo de nutrição.'
        ELSE 'O sistema de escalação não está salvando as alterações.'
    END,
    CASE 
        WHEN c.id % 4 = 0 THEN 'open'
        WHEN c.id % 4 = 1 THEN 'in_progress'
        WHEN c.id % 4 = 2 THEN 'resolved'
        ELSE 'open'
    END,
    CASE 
        WHEN c.id % 4 = 0 THEN 'high'
        WHEN c.id % 4 = 1 THEN 'medium'
        WHEN c.id % 4 = 2 THEN 'low'
        ELSE 'medium'
    END,
    CASE 
        WHEN c.id % 4 = 0 THEN 'technical'
        WHEN c.id % 4 = 1 THEN 'billing'
        WHEN c.id % 4 = 2 THEN 'feature'
        ELSE 'bug'
    END,
    COALESCE(c.email, 'contato@' || LOWER(REPLACE(c.name, ' ', '')) || '.com'),
    '+55 11 9' || LPAD((c.id * 1234567)::text, 8, '0')
FROM club_info c
WHERE c.id <= 10
ON CONFLICT DO NOTHING;

-- Inserir mensagens para os tickets
INSERT INTO master_support_messages (ticket_id, sender_type, sender_name, sender_email, message)
SELECT 
    t.id,
    'customer',
    'Cliente ' || c.name,
    t.customer_email,
    t.description
FROM master_support_tickets t
JOIN club_info c ON c.id = t.club_id;

-- Inserir algumas respostas de suporte
INSERT INTO master_support_messages (ticket_id, sender_type, sender_name, sender_email, message)
SELECT 
    t.id,
    'support',
    'Suporte Master',
    '<EMAIL>',
    CASE 
        WHEN t.category = 'technical' THEN 'Olá! Vou verificar esse erro e te retorno em breve com uma solução.'
        WHEN t.category = 'billing' THEN 'Vou analisar sua conta e esclarecer os valores cobrados.'
        WHEN t.category = 'feature' THEN 'Obrigado pela sugestão! Vou encaminhar para nossa equipe de produto.'
        ELSE 'Estou analisando o problema e em breve te envio uma solução.'
    END
FROM master_support_tickets t
WHERE t.status IN ('in_progress', 'resolved');

RAISE NOTICE 'Tabelas de suporte criadas com sucesso!';