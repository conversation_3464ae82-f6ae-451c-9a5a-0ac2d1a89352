-- Sistema de Mensalidades para Escolinhas de Futebol
-- Criação das tabelas necessárias para controle de mensalidades

-- Tabela de configurações de mensalidades por categoria
CREATE TABLE IF NOT EXISTS monthly_fee_settings (
  id SERIAL PRIMARY KEY,
  club_id INTEGER NOT NULL REFERENCES club_info(id) ON DELETE CASCADE,
  category_id INTEGER REFERENCES categories(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL, -- Nome da configuração (ex: "Mensalidade Sub-15")
  amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
  due_day INTEGER NOT NULL CHECK (due_day >= 1 AND due_day <= 31), -- Dia do vencimento
  reminder_days_before INTEGER DEFAULT 3 CHECK (reminder_days_before >= 0), -- Dias antes para lembrete
  late_fee_percentage DECIMAL(5,2) DEFAULT 0 CHECK (late_fee_percentage >= 0), -- Multa por atraso
  discount_percentage DECIMAL(5,2) DEFAULT 0 CHECK (discount_percentage >= 0), -- Desconto para pagamento antecipado
  discount_days_before INTEGER DEFAULT 0 CHECK (discount_days_before >= 0), -- Dias antes para desconto
  is_active BOOLEAN DEFAULT true,
  pix_key VARCHAR(255), -- Chave PIX específica para esta mensalidade
  description TEXT, -- Descrição adicional
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id)
);

-- Tabela de mensalidades dos atletas
CREATE TABLE IF NOT EXISTS player_monthly_fees (
  id SERIAL PRIMARY KEY,
  club_id INTEGER NOT NULL REFERENCES club_info(id) ON DELETE CASCADE,
  player_id UUID NOT NULL REFERENCES players(id) ON DELETE CASCADE,
  fee_setting_id INTEGER NOT NULL REFERENCES monthly_fee_settings(id) ON DELETE CASCADE,
  reference_month INTEGER NOT NULL CHECK (reference_month >= 1 AND reference_month <= 12),
  reference_year INTEGER NOT NULL CHECK (reference_year >= 2020),
  amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
  due_date DATE NOT NULL,
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'paid', 'overdue', 'cancelled')),
  paid_at TIMESTAMP WITH TIME ZONE,
  payment_method VARCHAR(50), -- 'pix', 'cash', 'transfer', etc.
  billing_transaction_id INTEGER REFERENCES billing_transactions(id), -- Link com sistema de billing
  late_fee_applied DECIMAL(10,2) DEFAULT 0,
  discount_applied DECIMAL(10,2) DEFAULT 0,
  final_amount DECIMAL(10,2), -- Valor final após multas/descontos
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),
  
  -- Constraint para evitar duplicatas
  UNIQUE(club_id, player_id, reference_month, reference_year, fee_setting_id)
);

-- Tabela de comprovantes de pagamento
CREATE TABLE IF NOT EXISTS monthly_fee_receipts (
  id SERIAL PRIMARY KEY,
  club_id INTEGER NOT NULL REFERENCES club_info(id) ON DELETE CASCADE,
  monthly_fee_id INTEGER NOT NULL REFERENCES player_monthly_fees(id) ON DELETE CASCADE,
  player_id UUID NOT NULL REFERENCES players(id) ON DELETE CASCADE,
  file_path VARCHAR(500) NOT NULL, -- Caminho do arquivo no Supabase Storage
  file_name VARCHAR(255) NOT NULL,
  file_size INTEGER,
  mime_type VARCHAR(100),
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
  reviewed_by UUID REFERENCES auth.users(id),
  reviewed_at TIMESTAMP WITH TIME ZONE,
  review_notes TEXT,
  uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Metadados do upload
  upload_ip INET,
  user_agent TEXT
);

-- Tabela de histórico de emails enviados
CREATE TABLE IF NOT EXISTS monthly_fee_email_log (
  id SERIAL PRIMARY KEY,
  club_id INTEGER NOT NULL REFERENCES club_info(id) ON DELETE CASCADE,
  monthly_fee_id INTEGER REFERENCES player_monthly_fees(id) ON DELETE CASCADE,
  player_id UUID NOT NULL REFERENCES players(id) ON DELETE CASCADE,
  email_type VARCHAR(50) NOT NULL CHECK (email_type IN ('reminder', 'overdue', 'payment_link', 'receipt_confirmation')),
  recipient_email VARCHAR(255) NOT NULL,
  subject VARCHAR(500) NOT NULL,
  sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  success BOOLEAN DEFAULT false,
  error_message TEXT,
  
  -- Dados do email para auditoria
  email_data JSONB
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_monthly_fee_settings_club_id ON monthly_fee_settings(club_id);
CREATE INDEX IF NOT EXISTS idx_monthly_fee_settings_category_id ON monthly_fee_settings(category_id);
CREATE INDEX IF NOT EXISTS idx_monthly_fee_settings_active ON monthly_fee_settings(club_id, is_active);

CREATE INDEX IF NOT EXISTS idx_player_monthly_fees_club_id ON player_monthly_fees(club_id);
CREATE INDEX IF NOT EXISTS idx_player_monthly_fees_player_id ON player_monthly_fees(player_id);
CREATE INDEX IF NOT EXISTS idx_player_monthly_fees_status ON player_monthly_fees(club_id, status);
CREATE INDEX IF NOT EXISTS idx_player_monthly_fees_due_date ON player_monthly_fees(club_id, due_date);
CREATE INDEX IF NOT EXISTS idx_player_monthly_fees_reference ON player_monthly_fees(club_id, reference_year, reference_month);

CREATE INDEX IF NOT EXISTS idx_monthly_fee_receipts_club_id ON monthly_fee_receipts(club_id);
CREATE INDEX IF NOT EXISTS idx_monthly_fee_receipts_monthly_fee_id ON monthly_fee_receipts(monthly_fee_id);
CREATE INDEX IF NOT EXISTS idx_monthly_fee_receipts_status ON monthly_fee_receipts(club_id, status);

CREATE INDEX IF NOT EXISTS idx_monthly_fee_email_log_club_id ON monthly_fee_email_log(club_id);
CREATE INDEX IF NOT EXISTS idx_monthly_fee_email_log_player_id ON monthly_fee_email_log(player_id);
CREATE INDEX IF NOT EXISTS idx_monthly_fee_email_log_type ON monthly_fee_email_log(club_id, email_type);

-- RLS (Row Level Security)
ALTER TABLE monthly_fee_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE player_monthly_fees ENABLE ROW LEVEL SECURITY;
ALTER TABLE monthly_fee_receipts ENABLE ROW LEVEL SECURITY;
ALTER TABLE monthly_fee_email_log ENABLE ROW LEVEL SECURITY;

-- Políticas RLS para monthly_fee_settings
DROP POLICY IF EXISTS "Users can view monthly fee settings from their club" ON monthly_fee_settings;
DROP POLICY IF EXISTS "Users can insert monthly fee settings in their club" ON monthly_fee_settings;
DROP POLICY IF EXISTS "Users can update monthly fee settings from their club" ON monthly_fee_settings;
DROP POLICY IF EXISTS "Users can delete monthly fee settings from their club" ON monthly_fee_settings;

CREATE POLICY "Users can view monthly fee settings from their club" ON monthly_fee_settings
  FOR SELECT USING (
    club_id IN (
      SELECT club_id FROM club_members 
      WHERE user_id = auth.uid() AND status = 'ativo'
    )
  );

CREATE POLICY "Users can insert monthly fee settings in their club" ON monthly_fee_settings
  FOR INSERT WITH CHECK (
    club_id IN (
      SELECT club_id FROM club_members 
      WHERE user_id = auth.uid() AND status = 'ativo'
    )
  );

CREATE POLICY "Users can update monthly fee settings from their club" ON monthly_fee_settings
  FOR UPDATE USING (
    club_id IN (
      SELECT club_id FROM club_members 
      WHERE user_id = auth.uid() AND status = 'ativo'
    )
  );

CREATE POLICY "Users can delete monthly fee settings from their club" ON monthly_fee_settings
  FOR DELETE USING (
    club_id IN (
      SELECT club_id FROM club_members 
      WHERE user_id = auth.uid() AND status = 'ativo'
    )
  );

-- Políticas RLS para player_monthly_fees
DROP POLICY IF EXISTS "Users can view monthly fees from their club" ON player_monthly_fees;
DROP POLICY IF EXISTS "Users can insert monthly fees in their club" ON player_monthly_fees;
DROP POLICY IF EXISTS "Users can update monthly fees from their club" ON player_monthly_fees;
DROP POLICY IF EXISTS "Players can view their own monthly fees" ON player_monthly_fees;

CREATE POLICY "Users can view monthly fees from their club" ON player_monthly_fees
  FOR SELECT USING (
    club_id IN (
      SELECT club_id FROM club_members 
      WHERE user_id = auth.uid() AND status = 'ativo'
    )
  );

CREATE POLICY "Users can insert monthly fees in their club" ON player_monthly_fees
  FOR INSERT WITH CHECK (
    club_id IN (
      SELECT club_id FROM club_members 
      WHERE user_id = auth.uid() AND status = 'ativo'
    )
  );

CREATE POLICY "Users can update monthly fees from their club" ON player_monthly_fees
  FOR UPDATE USING (
    club_id IN (
      SELECT club_id FROM club_members 
      WHERE user_id = auth.uid() AND status = 'ativo'
    )
  );

-- Política para jogadores verem suas próprias mensalidades
CREATE POLICY "Players can view their own monthly fees" ON player_monthly_fees
  FOR SELECT USING (
    player_id IN (
      SELECT id FROM players 
      WHERE user_id = auth.uid()
    )
  );

-- Políticas RLS para monthly_fee_receipts
DROP POLICY IF EXISTS "Users can view receipts from their club" ON monthly_fee_receipts;
DROP POLICY IF EXISTS "Users can insert receipts in their club" ON monthly_fee_receipts;
DROP POLICY IF EXISTS "Users can update receipts from their club" ON monthly_fee_receipts;
DROP POLICY IF EXISTS "Players can upload their own receipts" ON monthly_fee_receipts;

CREATE POLICY "Users can view receipts from their club" ON monthly_fee_receipts
  FOR SELECT USING (
    club_id IN (
      SELECT club_id FROM club_members 
      WHERE user_id = auth.uid() AND status = 'ativo'
    )
  );

CREATE POLICY "Users can insert receipts in their club" ON monthly_fee_receipts
  FOR INSERT WITH CHECK (
    club_id IN (
      SELECT club_id FROM club_members 
      WHERE user_id = auth.uid() AND status = 'ativo'
    )
  );

CREATE POLICY "Users can update receipts from their club" ON monthly_fee_receipts
  FOR UPDATE USING (
    club_id IN (
      SELECT club_id FROM club_members 
      WHERE user_id = auth.uid() AND status = 'ativo'
    )
  );

-- Política para jogadores enviarem seus próprios comprovantes
CREATE POLICY "Players can upload their own receipts" ON monthly_fee_receipts
  FOR INSERT WITH CHECK (
    player_id IN (
      SELECT id FROM players 
      WHERE user_id = auth.uid()
    )
  );

-- Políticas RLS para monthly_fee_email_log
DROP POLICY IF EXISTS "Users can view email log from their club" ON monthly_fee_email_log;
DROP POLICY IF EXISTS "Users can insert email log in their club" ON monthly_fee_email_log;

CREATE POLICY "Users can view email log from their club" ON monthly_fee_email_log
  FOR SELECT USING (
    club_id IN (
      SELECT club_id FROM club_members 
      WHERE user_id = auth.uid() AND status = 'ativo'
    )
  );

CREATE POLICY "Users can insert email log in their club" ON monthly_fee_email_log
  FOR INSERT WITH CHECK (
    club_id IN (
      SELECT club_id FROM club_members 
      WHERE user_id = auth.uid() AND status = 'ativo'
    )
  );

-- Triggers para atualizar updated_at
CREATE TRIGGER update_monthly_fee_settings_updated_at BEFORE UPDATE ON monthly_fee_settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_player_monthly_fees_updated_at BEFORE UPDATE ON player_monthly_fees
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Função para gerar mensalidades automaticamente
CREATE OR REPLACE FUNCTION generate_monthly_fees_for_month(
  p_club_id INTEGER,
  p_year INTEGER,
  p_month INTEGER,
  p_user_id UUID DEFAULT NULL
) RETURNS INTEGER AS $$
DECLARE
  v_setting RECORD;
  v_player RECORD;
  v_due_date DATE;
  v_generated_count INTEGER := 0;
BEGIN
  -- Validar parâmetros
  IF p_month < 1 OR p_month > 12 THEN
    RAISE EXCEPTION 'Mês deve estar entre 1 e 12';
  END IF;
  
  IF p_year < 2020 OR p_year > 2050 THEN
    RAISE EXCEPTION 'Ano deve estar entre 2020 e 2050';
  END IF;

  -- Para cada configuração de mensalidade ativa
  FOR v_setting IN 
    SELECT * FROM monthly_fee_settings 
    WHERE club_id = p_club_id AND is_active = true
  LOOP
    -- Calcular data de vencimento
    v_due_date := make_date(p_year, p_month, LEAST(v_setting.due_day, 
      EXTRACT(days FROM date_trunc('month', make_date(p_year, p_month, 1)) + interval '1 month - 1 day')::INTEGER
    ));
    
    -- Para cada jogador da categoria (ou todos se não especificada)
    FOR v_player IN 
      SELECT DISTINCT p.id, p.club_id
      FROM players p
      LEFT JOIN player_categories pc ON pc.player_id = p.id AND pc.club_id = p.club_id
      WHERE p.club_id = p_club_id 
        AND p.status NOT IN ('inativo', 'aguardando documentacao', 'aguardando agendamento', 'jogador agendado')
        AND (v_setting.category_id IS NULL OR pc.category_id = v_setting.category_id)
    LOOP
      -- Verificar se já existe mensalidade para este jogador/mês/ano/configuração
      IF NOT EXISTS (
        SELECT 1 FROM player_monthly_fees 
        WHERE club_id = p_club_id 
          AND player_id = v_player.id 
          AND fee_setting_id = v_setting.id
          AND reference_month = p_month 
          AND reference_year = p_year
      ) THEN
        -- Criar mensalidade
        INSERT INTO player_monthly_fees (
          club_id,
          player_id,
          fee_setting_id,
          reference_month,
          reference_year,
          amount,
          due_date,
          final_amount,
          created_by
        ) VALUES (
          p_club_id,
          v_player.id,
          v_setting.id,
          p_month,
          p_year,
          v_setting.amount,
          v_due_date,
          v_setting.amount,
          p_user_id
        );
        
        v_generated_count := v_generated_count + 1;
      END IF;
    END LOOP;
  END LOOP;
  
  RETURN v_generated_count;
END;
$$ LANGUAGE plpgsql;

-- Função para atualizar status de mensalidades em atraso
CREATE OR REPLACE FUNCTION update_overdue_monthly_fees(p_club_id INTEGER DEFAULT NULL) 
RETURNS INTEGER AS $$
DECLARE
  v_updated_count INTEGER := 0;
BEGIN
  UPDATE player_monthly_fees 
  SET 
    status = 'overdue',
    updated_at = NOW()
  WHERE 
    status = 'pending'
    AND due_date < CURRENT_DATE
    AND (p_club_id IS NULL OR club_id = p_club_id);
    
  GET DIAGNOSTICS v_updated_count = ROW_COUNT;
  RETURN v_updated_count;
END;
$$ LANGUAGE plpgsql;

-- Função para marcar mensalidade como paga
CREATE OR REPLACE FUNCTION mark_monthly_fee_as_paid(
  p_club_id INTEGER,
  p_monthly_fee_id INTEGER,
  p_payment_method VARCHAR(50) DEFAULT 'pix',
  p_billing_transaction_id INTEGER DEFAULT NULL,
  p_user_id UUID DEFAULT NULL
) RETURNS BOOLEAN AS $$
DECLARE
  v_fee RECORD;
BEGIN
  -- Buscar a mensalidade
  SELECT * INTO v_fee 
  FROM player_monthly_fees 
  WHERE id = p_monthly_fee_id AND club_id = p_club_id;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Mensalidade não encontrada';
  END IF;
  
  IF v_fee.status = 'paid' THEN
    RAISE EXCEPTION 'Mensalidade já está paga';
  END IF;
  
  -- Atualizar mensalidade
  UPDATE player_monthly_fees 
  SET 
    status = 'paid',
    paid_at = NOW(),
    payment_method = p_payment_method,
    billing_transaction_id = p_billing_transaction_id,
    updated_at = NOW()
  WHERE id = p_monthly_fee_id;
  
  -- Se há uma transação de billing associada, marcar como paga também
  IF p_billing_transaction_id IS NOT NULL THEN
    UPDATE billing_transactions 
    SET 
      status = 'pago',
      paid_at = NOW(),
      updated_at = NOW()
    WHERE id = p_billing_transaction_id AND club_id = p_club_id;
  END IF;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Comentários nas tabelas
COMMENT ON TABLE monthly_fee_settings IS 'Configurações de mensalidades por categoria de jogadores';
COMMENT ON TABLE player_monthly_fees IS 'Mensalidades individuais dos jogadores';
COMMENT ON TABLE monthly_fee_receipts IS 'Comprovantes de pagamento enviados pelos jogadores';
COMMENT ON TABLE monthly_fee_email_log IS 'Log de emails enviados relacionados às mensalidades';

COMMENT ON COLUMN monthly_fee_settings.due_day IS 'Dia do mês para vencimento (1-31)';
COMMENT ON COLUMN monthly_fee_settings.reminder_days_before IS 'Quantos dias antes do vencimento enviar lembrete';
COMMENT ON COLUMN monthly_fee_settings.late_fee_percentage IS 'Percentual de multa por atraso';
COMMENT ON COLUMN monthly_fee_settings.discount_percentage IS 'Percentual de desconto para pagamento antecipado';

COMMENT ON COLUMN player_monthly_fees.final_amount IS 'Valor final após aplicar multas e descontos';
COMMENT ON COLUMN player_monthly_fees.billing_transaction_id IS 'Link com o sistema de billing para PIX';