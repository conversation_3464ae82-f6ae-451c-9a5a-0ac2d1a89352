-- Sistema de Transferência de Atletas
-- Este arquivo cria todas as tabelas e funções necessárias para o sistema de transferência

-- 1. Tabela de jogadores globais (dados que não mudam entre clubes)
CREATE TABLE IF NOT EXISTS global_players (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  cpf_number TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  birthdate DATE,
  birthplace TEXT,
  nationality TEXT DEFAULT 'Brasil',
  rg_number TEXT,
  father_name TEXT,
  mother_name TEXT,
  phone TEXT,
  email TEXT,
  height DECIMAL(3,2),
  weight DECIMAL(5,2),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Tabela de documentos globais (compartilhados entre clubes)
CREATE TABLE IF NOT EXISTS global_player_documents (
  id SERIAL PRIMARY KEY,
  global_player_id UUID REFERENCES global_players(id) ON DELETE CASCADE,
  document_type TEXT NOT NULL,
  file_url TEXT NOT NULL,
  original_club_id INTEGER REFERENCES club_info(id),
  uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_active BOOLEAN DEFAULT TRUE,
  file_size INTEGER,
  file_name TEXT
);

-- 3. Tabela de transferências
CREATE TABLE IF NOT EXISTS player_transfers (
  id SERIAL PRIMARY KEY,
  global_player_id UUID REFERENCES global_players(id) ON DELETE CASCADE,
  from_club_id INTEGER REFERENCES club_info(id),
  to_club_id INTEGER REFERENCES club_info(id) NOT NULL,
  transfer_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  transfer_type TEXT DEFAULT 'transfer' CHECK (transfer_type IN ('transfer', 'loan', 'return')),
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'completed')),
  requested_by UUID REFERENCES auth.users(id),
  approved_by UUID REFERENCES auth.users(id),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE
);

-- 4. Adicionar colunas na tabela players existente
ALTER TABLE players 
ADD COLUMN IF NOT EXISTS global_player_id UUID REFERENCES global_players(id),
ADD COLUMN IF NOT EXISTS is_transfer BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS transfer_id INTEGER REFERENCES player_transfers(id);

-- 5. Índices para performance
CREATE INDEX IF NOT EXISTS idx_global_players_cpf ON global_players(cpf_number);
CREATE INDEX IF NOT EXISTS idx_global_player_documents_global_player_id ON global_player_documents(global_player_id);
CREATE INDEX IF NOT EXISTS idx_player_transfers_global_player_id ON player_transfers(global_player_id);
CREATE INDEX IF NOT EXISTS idx_player_transfers_to_club_id ON player_transfers(to_club_id);
CREATE INDEX IF NOT EXISTS idx_players_global_player_id ON players(global_player_id);

-- 6. Função para buscar jogador por CPF
CREATE OR REPLACE FUNCTION search_player_by_cpf(p_cpf TEXT)
RETURNS TABLE (
  found BOOLEAN,
  global_player_id UUID,
  name TEXT,
  birthdate DATE,
  birthplace TEXT,
  nationality TEXT,
  rg_number TEXT,
  father_name TEXT,
  mother_name TEXT,
  phone TEXT,
  email TEXT,
  height DECIMAL,
  weight DECIMAL,
  last_club_id INTEGER,
  last_club_name TEXT,
  current_status TEXT,
  is_active_elsewhere BOOLEAN,
  active_club_name TEXT,
  documents_count INTEGER
) AS $$
BEGIN
  -- Verificar se o jogador existe
  IF NOT EXISTS (SELECT 1 FROM global_players WHERE cpf_number = p_cpf) THEN
    RETURN QUERY SELECT FALSE::BOOLEAN, NULL::UUID, NULL::TEXT, NULL::DATE, NULL::TEXT, 
                        NULL::TEXT, NULL::TEXT, NULL::TEXT, NULL::TEXT, NULL::TEXT, 
                        NULL::TEXT, NULL::DECIMAL, NULL::DECIMAL, NULL::INTEGER, 
                        NULL::TEXT, NULL::TEXT, FALSE::BOOLEAN, NULL::TEXT, 0::INTEGER;
    RETURN;
  END IF;

  -- Retornar dados do jogador
  RETURN QUERY
  SELECT 
    TRUE::BOOLEAN as found,
    gp.id as global_player_id,
    gp.name,
    gp.birthdate,
    gp.birthplace,
    gp.nationality,
    gp.rg_number,
    gp.father_name,
    gp.mother_name,
    gp.phone,
    gp.email,
    gp.height,
    gp.weight,
    last_player.club_id as last_club_id,
    ci_last.name as last_club_name,
    active_player.status as current_status,
    CASE WHEN active_player.id IS NOT NULL THEN TRUE ELSE FALSE END as is_active_elsewhere,
    ci_active.name as active_club_name,
    COALESCE(doc_count.count, 0)::INTEGER as documents_count
  FROM global_players gp
  LEFT JOIN LATERAL (
    SELECT p.club_id, p.status
    FROM players p
    WHERE p.global_player_id = gp.id
    ORDER BY p.created_at DESC
    LIMIT 1
  ) last_player ON TRUE
  LEFT JOIN LATERAL (
    SELECT p.id, p.club_id, p.status
    FROM players p
    WHERE p.global_player_id = gp.id
      AND p.status IN ('ativo', 'disponivel', 'emprestado')
    LIMIT 1
  ) active_player ON TRUE
  LEFT JOIN club_info ci_last ON ci_last.id = last_player.club_id
  LEFT JOIN club_info ci_active ON ci_active.id = active_player.club_id
  LEFT JOIN (
    SELECT global_player_id, COUNT(*) as count
    FROM global_player_documents
    WHERE is_active = TRUE
    GROUP BY global_player_id
  ) doc_count ON doc_count.global_player_id = gp.id
  WHERE gp.cpf_number = p_cpf;
END;
$$ LANGUAGE plpgsql;

-- 7. Função para iniciar transferência
CREATE OR REPLACE FUNCTION initiate_player_transfer(
  p_cpf TEXT,
  p_to_club_id INTEGER,
  p_player_data JSONB,
  p_requested_by UUID
)
RETURNS TABLE (
  success BOOLEAN,
  player_id UUID,
  transfer_id INTEGER,
  global_player_id UUID,
  message TEXT
) AS $$
DECLARE
  v_global_player_id UUID;
  v_player_id UUID;
  v_transfer_id INTEGER;
  v_active_player RECORD;
BEGIN
  -- Verificar se jogador global existe
  SELECT id INTO v_global_player_id 
  FROM global_players 
  WHERE cpf_number = p_cpf;
  
  -- Se não existe, criar jogador global
  IF v_global_player_id IS NULL THEN
    INSERT INTO global_players (
      cpf_number, name, birthdate, birthplace, nationality,
      rg_number, father_name, mother_name, phone, email, height, weight
    ) VALUES (
      p_cpf,
      (p_player_data->>'name')::TEXT,
      (p_player_data->>'birthdate')::DATE,
      (p_player_data->>'birthplace')::TEXT,
      COALESCE((p_player_data->>'nationality')::TEXT, 'Brasil'),
      (p_player_data->>'rg_number')::TEXT,
      (p_player_data->>'father_name')::TEXT,
      (p_player_data->>'mother_name')::TEXT,
      (p_player_data->>'phone')::TEXT,
      (p_player_data->>'email')::TEXT,
      (p_player_data->>'height')::DECIMAL,
      (p_player_data->>'weight')::DECIMAL
    ) RETURNING id INTO v_global_player_id;
  END IF;
  
  -- Verificar se jogador já está ativo em outro clube
  SELECT p.id, p.club_id, p.status, ci.name as club_name
  INTO v_active_player
  FROM players p
  JOIN club_info ci ON ci.id = p.club_id
  WHERE p.global_player_id = v_global_player_id 
    AND p.status IN ('ativo', 'disponivel', 'emprestado')
    AND p.club_id != p_to_club_id;
    
  IF v_active_player.id IS NOT NULL THEN
    RETURN QUERY SELECT FALSE, NULL::UUID, NULL::INTEGER, v_global_player_id,
                        ('Jogador já está ativo no clube: ' || v_active_player.club_name || '. O clube atual deve marcar o jogador como inativo antes da transferência.')::TEXT;
    RETURN;
  END IF;
  
  -- Criar registro de transferência
  INSERT INTO player_transfers (
    global_player_id, to_club_id, requested_by, status
  ) VALUES (
    v_global_player_id, p_to_club_id, p_requested_by, 'completed'
  ) RETURNING id INTO v_transfer_id;
  
  -- Criar jogador no novo clube
  INSERT INTO players (
    id, club_id, global_player_id, is_transfer, transfer_id,
    name, position, age, number, nationality, height, weight,
    birthdate, birthplace, status, entry_date, championship_registration,
    nickname, professional_status, rg_number, cpf_number,
    father_name, mother_name, referred_by, phone, address,
    zip_code, city, state, email, contract_end_date, observation
  ) VALUES (
    gen_random_uuid(),
    p_to_club_id,
    v_global_player_id,
    TRUE,
    v_transfer_id,
    (p_player_data->>'name')::TEXT,
    (p_player_data->>'position')::TEXT,
    (p_player_data->>'age')::INTEGER,
    (p_player_data->>'number')::INTEGER,
    COALESCE((p_player_data->>'nationality')::TEXT, 'Brasil'),
    (p_player_data->>'height')::DECIMAL,
    (p_player_data->>'weight')::DECIMAL,
    (p_player_data->>'birthdate')::DATE,
    (p_player_data->>'birthplace')::TEXT,
    COALESCE((p_player_data->>'status')::TEXT, 'ativo'),
    COALESCE((p_player_data->>'entry_date')::DATE, CURRENT_DATE),
    (p_player_data->>'championship_registration')::TEXT,
    (p_player_data->>'nickname')::TEXT,
    (p_player_data->>'professional_status')::TEXT,
    (p_player_data->>'rg_number')::TEXT,
    p_cpf,
    (p_player_data->>'father_name')::TEXT,
    (p_player_data->>'mother_name')::TEXT,
    (p_player_data->>'referred_by')::TEXT,
    (p_player_data->>'phone')::TEXT,
    (p_player_data->>'address')::TEXT,
    (p_player_data->>'zip_code')::TEXT,
    (p_player_data->>'city')::TEXT,
    (p_player_data->>'state')::TEXT,
    (p_player_data->>'email')::TEXT,
    (p_player_data->>'contract_end_date')::DATE,
    (p_player_data->>'observation')::TEXT
  ) RETURNING id INTO v_player_id;
  
  -- Atualizar data de conclusão da transferência
  UPDATE player_transfers 
  SET completed_at = NOW() 
  WHERE id = v_transfer_id;
  
  RETURN QUERY SELECT TRUE, v_player_id, v_transfer_id, v_global_player_id, 'Transferência realizada com sucesso'::TEXT;
END;
$$ LANGUAGE plpgsql;

-- 8. Função para obter documentos de um jogador global
CREATE OR REPLACE FUNCTION get_global_player_documents(p_global_player_id UUID)
RETURNS TABLE (
  id INTEGER,
  document_type TEXT,
  file_url TEXT,
  original_club_id INTEGER,
  original_club_name TEXT,
  uploaded_at TIMESTAMP WITH TIME ZONE,
  file_size INTEGER,
  file_name TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    gpd.id,
    gpd.document_type,
    gpd.file_url,
    gpd.original_club_id,
    ci.name as original_club_name,
    gpd.uploaded_at,
    gpd.file_size,
    gpd.file_name
  FROM global_player_documents gpd
  LEFT JOIN club_info ci ON ci.id = gpd.original_club_id
  WHERE gpd.global_player_id = p_global_player_id
    AND gpd.is_active = TRUE
  ORDER BY gpd.uploaded_at DESC;
END;
$$ LANGUAGE plpgsql;

-- 9. Função para migrar jogador existente para o sistema global
CREATE OR REPLACE FUNCTION migrate_player_to_global(p_player_id UUID)
RETURNS UUID AS $$
DECLARE
  v_player RECORD;
  v_global_player_id UUID;
BEGIN
  -- Buscar dados do jogador
  SELECT * INTO v_player FROM players WHERE id = p_player_id;
  
  IF v_player.id IS NULL THEN
    RAISE EXCEPTION 'Jogador não encontrado';
  END IF;
  
  -- Verificar se já tem global_player_id
  IF v_player.global_player_id IS NOT NULL THEN
    RETURN v_player.global_player_id;
  END IF;
  
  -- Verificar se já existe um jogador global com o mesmo CPF
  SELECT id INTO v_global_player_id 
  FROM global_players 
  WHERE cpf_number = v_player.cpf_number;
  
  -- Se não existe, criar
  IF v_global_player_id IS NULL THEN
    INSERT INTO global_players (
      cpf_number, name, birthdate, birthplace, nationality,
      rg_number, father_name, mother_name, phone, email, height, weight
    ) VALUES (
      v_player.cpf_number,
      v_player.name,
      v_player.birthdate::DATE,
      v_player.birthplace,
      COALESCE(v_player.nationality, 'Brasil'),
      v_player.rg_number,
      v_player.father_name,
      v_player.mother_name,
      v_player.phone,
      v_player.email,
      v_player.height,
      v_player.weight
    ) RETURNING id INTO v_global_player_id;
  END IF;
  
  -- Atualizar o jogador com o global_player_id
  UPDATE players 
  SET global_player_id = v_global_player_id 
  WHERE id = p_player_id;
  
  -- Migrar documentos para a tabela global
  INSERT INTO global_player_documents (
    global_player_id, document_type, file_url, original_club_id, uploaded_at
  )
  SELECT 
    v_global_player_id,
    pd.document_type,
    pd.file_url,
    pd.club_id,
    pd.uploaded_at
  FROM player_documents pd
  WHERE pd.player_id = p_player_id
    AND NOT EXISTS (
      SELECT 1 FROM global_player_documents gpd
      WHERE gpd.global_player_id = v_global_player_id
        AND gpd.document_type = pd.document_type
        AND gpd.file_url = pd.file_url
    );
  
  RETURN v_global_player_id;
END;
$$ LANGUAGE plpgsql;

-- 10. Trigger para atualizar updated_at em global_players
CREATE OR REPLACE FUNCTION update_global_players_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_update_global_players_updated_at ON global_players;
CREATE TRIGGER trigger_update_global_players_updated_at
  BEFORE UPDATE ON global_players
  FOR EACH ROW
  EXECUTE FUNCTION update_global_players_updated_at();

-- 11. RLS (Row Level Security) para as novas tabelas
ALTER TABLE global_players ENABLE ROW LEVEL SECURITY;
ALTER TABLE global_player_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE player_transfers ENABLE ROW LEVEL SECURITY;

-- Políticas RLS para global_players (apenas leitura para busca por CPF)
CREATE POLICY "global_players_select_policy" ON global_players
  FOR SELECT USING (true); -- Permitir leitura para busca por CPF

CREATE POLICY "global_players_insert_policy" ON global_players
  FOR INSERT WITH CHECK (true); -- Permitir inserção durante transferência

CREATE POLICY "global_players_update_policy" ON global_players
  FOR UPDATE USING (true); -- Permitir atualização

-- Políticas RLS para global_player_documents
CREATE POLICY "global_player_documents_select_policy" ON global_player_documents
  FOR SELECT USING (true); -- Permitir leitura para transferências

CREATE POLICY "global_player_documents_insert_policy" ON global_player_documents
  FOR INSERT WITH CHECK (true); -- Permitir inserção

-- Políticas RLS para player_transfers
CREATE POLICY "player_transfers_select_policy" ON player_transfers
  FOR SELECT USING (
    to_club_id IN (
      SELECT club_id FROM club_members WHERE user_id = auth.uid()
    )
    OR from_club_id IN (
      SELECT club_id FROM club_members WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "player_transfers_insert_policy" ON player_transfers
  FOR INSERT WITH CHECK (
    to_club_id IN (
      SELECT club_id FROM club_members WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "player_transfers_update_policy" ON player_transfers
  FOR UPDATE USING (
    to_club_id IN (
      SELECT club_id FROM club_members WHERE user_id = auth.uid()
    )
    OR from_club_id IN (
      SELECT club_id FROM club_members WHERE user_id = auth.uid()
    )
  );

-- Comentários para documentação
COMMENT ON TABLE global_players IS 'Tabela global de jogadores com dados que não mudam entre clubes';
COMMENT ON TABLE global_player_documents IS 'Documentos globais dos jogadores, compartilhados entre clubes';
COMMENT ON TABLE player_transfers IS 'Histórico de transferências de jogadores entre clubes';
COMMENT ON FUNCTION search_player_by_cpf(TEXT) IS 'Busca jogador por CPF no sistema global';
COMMENT ON FUNCTION initiate_player_transfer(TEXT, INTEGER, JSONB, UUID) IS 'Inicia processo de transferência de jogador';
COMMENT ON FUNCTION get_global_player_documents(UUID) IS 'Obtém documentos de um jogador global';
COMMENT ON FUNCTION migrate_player_to_global(UUID) IS 'Migra jogador existente para o sistema global';