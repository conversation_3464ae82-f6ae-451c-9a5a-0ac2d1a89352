-- Criar função para validar status de jogador (bypassa RLS)

CREATE OR REPLACE FUNCTION validate_player_status_change(
  p_player_id UUID,
  p_club_id INTEGER,
  p_new_status TEXT
)
RETURNS TABLE (
  can_change BOOLEAN,
  error_message TEXT
)
SECURITY DEFINER  -- Bypassa RLS
SET search_path = public
AS $$
DECLARE
  v_player_data RECORD;
  v_active_elsewhere RECORD;
BEGIN
  -- Se o novo status é inativo, sempre permitir
  IF p_new_status = 'inativo' THEN
    RETURN QUERY SELECT TRUE, NULL::TEXT;
    RETURN;
  END IF;

  -- Buscar dados do jogador
  SELECT cpf_number, global_player_id, name
  INTO v_player_data
  FROM players
  WHERE id = p_player_id AND club_id = p_club_id;

  -- Se não tem global_player_id, permitir (jogador antigo)
  IF v_player_data.global_player_id IS NULL THEN
    RETURN QUERY SELECT TRUE, NULL::TEXT;
    RETURN;
  END IF;

  -- Verificar se existe outro jogador ativo com o mesmo global_player_id
  SELECT p.id, p.club_id, p.status, ci.name as club_name
  INTO v_active_elsewhere
  FROM players p
  JOIN club_info ci ON ci.id = p.club_id
  WHERE p.global_player_id = v_player_data.global_player_id
    AND p.club_id != p_club_id
    AND p.status NOT IN ('inativo')
  LIMIT 1;

  -- Se encontrou jogador ativo em outro clube, bloquear
  IF v_active_elsewhere.id IS NOT NULL THEN
    RETURN QUERY SELECT 
      FALSE, 
      ('Não é possível ativar ' || v_player_data.name || '. Jogador já está ativo no clube: ' || v_active_elsewhere.club_name || ' (Status: ' || v_active_elsewhere.status || '). O outro clube deve marcar o jogador como inativo primeiro.')::TEXT;
    RETURN;
  END IF;

  -- Se chegou até aqui, pode alterar
  RETURN QUERY SELECT TRUE, NULL::TEXT;
END;
$$ LANGUAGE plpgsql;

SELECT 'Função de validação de status criada!' as status;