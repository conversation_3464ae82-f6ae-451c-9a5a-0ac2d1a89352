-- =====================================================
-- SCRIPT: Debug do Sistema de Autenticação
-- DESCRIÇÃO: Verifica se o usuário existe no Auth
-- =====================================================

-- Verificar se o usuário existe na tabela auth.users
SELECT 
  id,
  email,
  email_confirmed_at,
  created_at,
  last_sign_in_at
FROM auth.users 
WHERE email = '<EMAIL>';

-- Verificar se existe na tabela master_users
SELECT 
  id,
  name,
  email,
  role,
  is_active,
  created_at
FROM master_users 
WHERE email = '<EMAIL>';

-- Verificar se os IDs coincidem
SELECT 
  au.id as auth_id,
  au.email as auth_email,
  mu.id as master_id,
  mu.email as master_email,
  CASE 
    WHEN au.id = mu.id THEN 'IDs coincidem ✅'
    ELSE 'IDs NÃO coincidem ❌'
  END as status
FROM auth.users au
FULL OUTER JOIN master_users mu ON au.id = mu.id
WHERE au.email = '<EMAIL>' OR mu.email = '<EMAIL>';