-- Debug simples do problema de RLS

-- 1. Verificar se RLS está ativo
SELECT 'RLS na tabela players:' as info;
SELECT schemaname, tablename, rowsecurity FROM pg_tables WHERE tablename = 'players';

-- 2. Verificar políticas RLS
SELECT 'Políticas RLS:' as info;
SELECT policyname, permissive, cmd FROM pg_policies WHERE tablename = 'players';

-- 3. Criar função que BYPASSA RLS
CREATE OR REPLACE FUNCTION search_player_by_cpf(p_cpf TEXT)
RETURNS TABLE (
  found BOOLEAN,
  global_player_id_result UUID,
  name TEXT,
  birthdate DATE,
  birthplace TEXT,
  nationality TEXT,
  rg_number TEXT,
  father_name TEXT,
  mother_name TEXT,
  phone TEXT,
  email TEXT,
  height INTEGER,
  weight INTEGER,
  last_club_id INTEGER,
  last_club_name TEXT,
  current_status TEXT,
  is_active_elsewhere BOOLEAN,
  active_club_name TEXT,
  documents_count INTEGER
) 
SECURITY DEFINER  -- BYPASSA RLS
SET search_path = public
AS $$
DECLARE
  v_global_player_id UUID;
  v_active_player RECORD;
  v_last_player RECORD;
  v_doc_count INTEGER;
BEGIN
  -- Buscar jogador global
  SELECT id INTO v_global_player_id FROM global_players WHERE cpf_number = p_cpf;
  
  IF v_global_player_id IS NULL THEN
    RETURN QUERY SELECT FALSE::BOOLEAN, NULL::UUID, NULL::TEXT, NULL::DATE, NULL::TEXT, 
                        NULL::TEXT, NULL::TEXT, NULL::TEXT, NULL::TEXT, NULL::TEXT, 
                        NULL::TEXT, NULL::INTEGER, NULL::INTEGER, NULL::INTEGER, 
                        NULL::TEXT, NULL::TEXT, FALSE::BOOLEAN, NULL::TEXT, 0::INTEGER;
    RETURN;
  END IF;

  -- Buscar jogador ativo (BYPASSANDO RLS)
  SELECT p.id, p.club_id, p.status, ci.name as club_name
  INTO v_active_player
  FROM players p
  JOIN club_info ci ON ci.id = p.club_id
  WHERE p.global_player_id = v_global_player_id
    AND p.status NOT IN ('inativo')
  ORDER BY p.id DESC
  LIMIT 1;

  -- Buscar último clube
  SELECT p.club_id, p.status, ci.name as club_name
  INTO v_last_player
  FROM players p
  JOIN club_info ci ON ci.id = p.club_id
  WHERE p.global_player_id = v_global_player_id
  ORDER BY p.id DESC
  LIMIT 1;

  -- Contar documentos
  SELECT COUNT(*) INTO v_doc_count
  FROM global_player_documents
  WHERE global_player_id = v_global_player_id AND is_active = TRUE;

  -- Retornar resultado
  RETURN QUERY
  SELECT 
    TRUE::BOOLEAN as found,
    v_global_player_id as global_player_id_result,
    gp.name,
    gp.birthdate,
    gp.birthplace,
    gp.nationality,
    gp.rg_number,
    gp.father_name,
    gp.mother_name,
    gp.phone,
    gp.email,
    gp.height,
    gp.weight,
    v_last_player.club_id as last_club_id,
    v_last_player.club_name as last_club_name,
    COALESCE(v_active_player.status, v_last_player.status) as current_status,
    CASE WHEN v_active_player.id IS NOT NULL THEN TRUE ELSE FALSE END as is_active_elsewhere,
    v_active_player.club_name as active_club_name,
    COALESCE(v_doc_count, 0)::INTEGER as documents_count
  FROM global_players gp
  WHERE gp.id = v_global_player_id;
END;
$$ LANGUAGE plpgsql;

-- 4. Testar
SELECT 'Teste com bypass RLS:' as info;
SELECT * FROM search_player_by_cpf('75164303078');

SELECT 'Função criada com SECURITY DEFINER para bypassar RLS!' as status;