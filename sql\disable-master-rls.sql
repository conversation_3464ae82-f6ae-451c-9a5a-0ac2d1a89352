-- =====================================================
-- SCRIPT: Desabilitar RLS temporariamente para debug
-- DESCRIÇÃO: Remove RLS das tabelas master para testar login
-- =====================================================

-- Desabilitar RLS nas tabelas master
ALTER TABLE master_organizations DISABLE ROW LEVEL SECURITY;
ALTER TABLE master_plans DISABLE ROW LEVEL SECURITY;
ALTER TABLE master_users DISABLE ROW LEVEL SECURITY;
ALTER TABLE master_payments DISABLE ROW LEVEL SECURITY;
ALTER TABLE master_audit_logs DISABLE ROW LEVEL SECURITY;
ALTER TABLE master_notification_logs DISABLE ROW LEVEL SECURITY;
ALTER TABLE master_settings DISABLE ROW LEVEL SECURITY;

-- Verificar status do RLS
SELECT 
    schemaname,
    tablename,
    rowsecurity as rls_enabled,
    (SELECT count(*) FROM pg_policies WHERE tablename = t.tablename) as policies_count
FROM pg_tables t
WHERE schemaname = 'public' 
AND tablename LIKE 'master_%'
ORDER BY tablename;

RAISE NOTICE 'RLS desabilitado nas tabelas master para debug';