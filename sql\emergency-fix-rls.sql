-- =====================================================
-- SCRIPT DE EMERGÊNCIA: Corrigir RLS das tabelas master
-- Execute este script no SQL Editor do Supabase
-- =====================================================

-- 1. DESABILITAR RLS TEMPORARIAMENTE
ALTER TABLE master_organizations DISABLE ROW LEVEL SECURITY;
ALTER TABLE master_users DISABLE ROW LEVEL SECURITY;
ALTER TABLE master_plans DISABLE ROW LEVEL SECURITY;
ALTER TABLE master_payments DISABLE ROW LEVEL SECURITY;

-- 2. REMOVER TODAS AS POLÍTICAS PROBLEMÁTICAS
DO $$ 
DECLARE
    pol RECORD;
BEGIN
    FOR pol IN 
        SELECT policyname, tablename 
        FROM pg_policies 
        WHERE tablename LIKE 'master_%'
    LOOP
        EXECUTE format('DROP POLICY IF EXISTS %I ON %I', pol.policyname, pol.tablename);
    END LOOP;
END $$;

-- 3. CRIAR POLÍTICAS SIMPLES E PERMISSIVAS
-- Permitir tudo para usuários autenticados (temporário)

CREATE POLICY "temp_allow_all_master_organizations" ON master_organizations
  FOR ALL TO authenticated USING (true) WITH CHECK (true);

CREATE POLICY "temp_allow_all_master_users" ON master_users
  FOR ALL TO authenticated USING (true) WITH CHECK (true);

CREATE POLICY "temp_allow_all_master_plans" ON master_plans
  FOR ALL TO authenticated USING (true) WITH CHECK (true);

CREATE POLICY "temp_allow_all_master_payments" ON master_payments
  FOR ALL TO authenticated USING (true) WITH CHECK (true);

-- 4. REABILITAR RLS
ALTER TABLE master_organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE master_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE master_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE master_payments ENABLE ROW LEVEL SECURITY;

-- 5. TESTAR SE FUNCIONA
DO $$
DECLARE
    test_count INTEGER;
BEGIN
    SELECT count(*) INTO test_count FROM master_organizations;
    RAISE NOTICE 'master_organizations: % registros', test_count;
    
    SELECT count(*) INTO test_count FROM master_users;
    RAISE NOTICE 'master_users: % registros', test_count;
    
    SELECT count(*) INTO test_count FROM master_plans;
    RAISE NOTICE 'master_plans: % registros', test_count;
    
    RAISE NOTICE 'RLS corrigido com sucesso!';
EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Erro no teste: %', SQLERRM;
END $$;