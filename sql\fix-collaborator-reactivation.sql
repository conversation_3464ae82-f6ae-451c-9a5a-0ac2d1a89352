-- =====================================================
-- CORREÇÃO: REATIVAÇÃO DE COLABORADORES
-- =====================================================
-- Este script corrige o erro ao tentar reativar colaboradores
-- que estavam com status 'inactive'

-- Recriar a função restore_collaborator_permissions sem a coluna updated_at
CREATE OR REPLACE FUNCTION restore_collaborator_permissions(p_club_id INTEGER, p_collaborator_id INTEGER)
RETURNS VOID AS $$
DECLARE
  v_user_id UUID;
  v_collaborator_status TEXT;
  v_default_permissions JSONB;
BEGIN
  -- Log the operation
  RAISE NOTICE 'Restaurando permissões do colaborador % no clube %', p_collaborator_id, p_club_id;

  -- 1. Get collaborator's user_id and current status
  SELECT user_id, status INTO v_user_id, v_collaborator_status
  FROM collaborators
  WHERE id = p_collaborator_id AND club_id = p_club_id;

  IF v_user_id IS NULL THEN
    RAISE NOTICE 'Colaborador % não encontrado ou sem usuário associado', p_collaborator_id;
    RETURN;
  END IF;

  -- 2. Define default permissions for collaborators
  v_default_permissions := '{
    "collaborators.view": true,
    "collaborators.create": false,
    "collaborators.edit": false,
    "collaborators.delete": false,
    "collaborator_finances.view": true,
    "collaborator_finances.create": false,
    "collaborator_finances.edit": false,
    "collaborator_finances.delete": false
  }'::jsonb;

  -- 3. Check if user already has permissions in club_members
  IF EXISTS (
    SELECT 1 FROM club_members 
    WHERE club_id = p_club_id AND user_id = v_user_id
  ) THEN
    -- Update existing permissions, preserving any existing ones and adding collaborator permissions
    UPDATE club_members
    SET 
      permissions = COALESCE(permissions, '{}'::jsonb) || v_default_permissions
    WHERE club_id = p_club_id AND user_id = v_user_id;
  ELSE
    -- Insert new permissions record
    INSERT INTO club_members (
      club_id, 
      user_id, 
      permissions, 
      created_at
    ) VALUES (
      p_club_id,
      v_user_id,
      v_default_permissions,
      NOW()
    );
  END IF;

  -- 4. Reactivate financial data if it was deactivated
  UPDATE collaborators
  SET 
    financial_data = jsonb_set(
      COALESCE(financial_data, '{}'::jsonb),
      '{status}',
      '"active"',
      true
    ),
    updated_at = NOW()
  WHERE id = p_collaborator_id AND club_id = p_club_id
  AND (financial_data->>'status' = 'inactive' OR financial_data->>'status' IS NULL);

  -- 5. Log completion
  RAISE NOTICE 'Permissões do colaborador % restauradas com sucesso', p_collaborator_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Comentário explicativo
COMMENT ON FUNCTION restore_collaborator_permissions(INTEGER, INTEGER) IS 'Restaura permissões de um colaborador quando ele é reativado (status muda de inactive para ativo). Versão corrigida sem referência à coluna updated_at inexistente.';

-- Teste da função (opcional)
DO $$
DECLARE
  test_club_id INTEGER := 21; -- Substitua pelo ID do clube real se quiser testar
  test_collaborator_id INTEGER := 111; -- Substitua pelo ID do colaborador real se quiser testar
BEGIN
  -- Verificar se o colaborador existe antes de testar
  IF EXISTS (
    SELECT 1 FROM collaborators 
    WHERE club_id = test_club_id AND id = test_collaborator_id
  ) THEN
    RAISE NOTICE 'Testando função restore_collaborator_permissions...';
    -- Comentar a linha abaixo se não quiser executar o teste automaticamente
    -- PERFORM restore_collaborator_permissions(test_club_id, test_collaborator_id);
    RAISE NOTICE 'Função disponível para teste. Para testar manualmente, execute:';
    RAISE NOTICE 'SELECT restore_collaborator_permissions(%, %);', test_club_id, test_collaborator_id;
  ELSE
    RAISE NOTICE 'Colaborador de teste não encontrado. Função corrigida e pronta para uso.';
  END IF;
END $$;

-- Também corrigir a função restore_player_permissions
CREATE OR REPLACE FUNCTION restore_player_permissions(p_club_id INTEGER, p_player_id UUID)
RETURNS VOID AS $$
DECLARE
  v_user_id UUID;
  v_default_permissions JSONB;
BEGIN
  -- Log the operation
  RAISE NOTICE 'Restaurando permissões do jogador % no clube %', p_player_id, p_club_id;

  -- Get player's user_id
  SELECT user_id INTO v_user_id
  FROM players
  WHERE id = p_player_id AND club_id = p_club_id;

  IF v_user_id IS NULL THEN
    RAISE NOTICE 'Jogador % não encontrado ou sem usuário associado', p_player_id;
    RETURN;
  END IF;

  -- Define default permissions for players
  v_default_permissions := '{
    "players.view": true,
    "players.edit_own": true,
    "player_finances.view_own": true
  }'::jsonb;

  -- Update or insert permissions in club_members
  IF EXISTS (
    SELECT 1 FROM club_members
    WHERE club_id = p_club_id AND user_id = v_user_id
  ) THEN
    UPDATE club_members
    SET permissions = v_default_permissions,
        status = 'ativo'
    WHERE club_id = p_club_id AND user_id = v_user_id;
  ELSE
    INSERT INTO club_members (
      club_id,
      user_id,
      permissions,
      status,
      created_at
    ) VALUES (
      p_club_id,
      v_user_id,
      v_default_permissions,
      'ativo',
      NOW()
    );
  END IF;

  RAISE NOTICE 'Permissões do jogador % restauradas com sucesso', p_player_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Comentário explicativo
COMMENT ON FUNCTION restore_player_permissions(INTEGER, UUID) IS 'Restaura permissões de um jogador quando ele é reativado (status muda de inativo para ativo). Versão corrigida sem referência à coluna updated_at inexistente.';

-- Log da correção
DO $$
BEGIN
  RAISE NOTICE 'Funções restore_collaborator_permissions e restore_player_permissions corrigidas com sucesso!';
  RAISE NOTICE 'Agora colaboradores e jogadores podem ser reativados sem erro de coluna updated_at.';
END $$;