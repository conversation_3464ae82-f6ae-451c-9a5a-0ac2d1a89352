-- Drop existing policies
DROP POLICY IF EXISTS "Public can upload receipt with valid token" ON evaluation_payments;
DROP POLICY IF EXISTS "Public can read payment with valid token" ON evaluation_payments;

-- Create new policy for public read access with payment token
CREATE POLICY "Public can read payment with valid token" ON evaluation_payments
    FOR SELECT USING (true);

-- Create new policy for public update of receipt fields only
CREATE POLICY "Public can upload receipt with valid token" ON evaluation_payments
    FOR UPDATE USING (
        -- Allow update only for pending payments without existing receipt
        status = 'pending' AND receipt_file_url IS NULL
    )
    WITH CHECK (
        -- Allow updating only receipt-related fields
        status = 'pending' AND receipt_file_url IS NOT NULL AND receipt_uploaded_at IS NOT NULL
    );

-- Grant necessary permissions to anonymous users
GRANT SELECT ON evaluation_payments TO anon;
GRANT UPDATE ON evaluation_payments TO anon;