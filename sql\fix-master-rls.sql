-- =====================================================
-- SCRIPT: Corrigir problemas de RLS nas tabelas master
-- DESCRIÇÃO: Desabilita RLS temporariamente e recria políticas
-- =====================================================

-- 1. Desabilitar RLS temporariamente para debug
ALTER TABLE master_organizations DISABLE ROW LEVEL SECURITY;
ALTER TABLE master_plans DISABLE ROW LEVEL SECURITY;
ALTER TABLE master_users DISABLE ROW LEVEL SECURITY;
ALTER TABLE master_payments DISABLE ROW LEVEL SECURITY;
ALTER TABLE master_audit_logs DISABLE ROW LEVEL SECURITY;
ALTER TABLE master_notification_logs DISABLE ROW LEVEL SECURITY;
ALTER TABLE master_settings DISABLE ROW LEVEL SECURITY;

-- 2. Remover todas as políticas existentes
DROP POLICY IF EXISTS "master_users_can_view_organizations" ON master_organizations;
DROP POLICY IF EXISTS "super_admin_can_modify_organizations" ON master_organizations;
DROP POLICY IF EXISTS "master_users_can_view_plans" ON master_plans;
DROP POLICY IF EXISTS "authorized_users_can_modify_plans" ON master_plans;
DROP POLICY IF EXISTS "master_users_can_view_users" ON master_users;
DROP POLICY IF EXISTS "super_admin_can_modify_users" ON master_users;
DROP POLICY IF EXISTS "users_can_update_own_data" ON master_users;
DROP POLICY IF EXISTS "master_users_can_view_payments" ON master_payments;
DROP POLICY IF EXISTS "authorized_users_can_modify_payments" ON master_payments;
DROP POLICY IF EXISTS "master_users_can_view_audit_logs" ON master_audit_logs;
DROP POLICY IF EXISTS "system_can_insert_audit_logs" ON master_audit_logs;
DROP POLICY IF EXISTS "super_admin_can_delete_audit_logs" ON master_audit_logs;
DROP POLICY IF EXISTS "master_users_can_view_notification_logs" ON master_notification_logs;
DROP POLICY IF EXISTS "system_can_modify_notification_logs" ON master_notification_logs;
DROP POLICY IF EXISTS "master_users_can_view_settings" ON master_settings;
DROP POLICY IF EXISTS "super_admin_can_modify_settings" ON master_settings;
DROP POLICY IF EXISTS "master_users_can_view_all_clubs" ON club_info;
DROP POLICY IF EXISTS "master_users_can_modify_clubs" ON club_info;

-- 3. Testar consultas básicas
DO $
DECLARE
    test_count INTEGER;
BEGIN
    -- Testar master_organizations
    SELECT count(*) INTO test_count FROM master_organizations;
    RAISE NOTICE 'master_organizations: % registros', test_count;
    
    -- Testar master_users
    SELECT count(*) INTO test_count FROM master_users;
    RAISE NOTICE 'master_users: % registros', test_count;
    
    -- Testar master_plans
    SELECT count(*) INTO test_count FROM master_plans;
    RAISE NOTICE 'master_plans: % registros', test_count;
    
    RAISE NOTICE 'Todas as consultas funcionaram sem RLS!';
END $;

-- 4. Criar políticas mais simples e permissivas
-- Para master_organizations
CREATE POLICY "allow_authenticated_master_organizations" ON master_organizations
  FOR ALL
  TO authenticated
  USING (true)
  WITH CHECK (true);

-- Para master_users
CREATE POLICY "allow_authenticated_master_users" ON master_users
  FOR ALL
  TO authenticated
  USING (true)
  WITH CHECK (true);

-- Para master_plans
CREATE POLICY "allow_authenticated_master_plans" ON master_plans
  FOR ALL
  TO authenticated
  USING (true)
  WITH CHECK (true);

-- Para master_payments
CREATE POLICY "allow_authenticated_master_payments" ON master_payments
  FOR ALL
  TO authenticated
  USING (true)
  WITH CHECK (true);

-- Para master_audit_logs
CREATE POLICY "allow_authenticated_master_audit_logs" ON master_audit_logs
  FOR ALL
  TO authenticated
  USING (true)
  WITH CHECK (true);

-- Para master_notification_logs
CREATE POLICY "allow_authenticated_master_notification_logs" ON master_notification_logs
  FOR ALL
  TO authenticated
  USING (true)
  WITH CHECK (true);

-- Para master_settings
CREATE POLICY "allow_authenticated_master_settings" ON master_settings
  FOR ALL
  TO authenticated
  USING (true)
  WITH CHECK (true);

-- 5. Reabilitar RLS com políticas mais simples
ALTER TABLE master_organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE master_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE master_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE master_payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE master_audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE master_notification_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE master_settings ENABLE ROW LEVEL SECURITY;

-- 6. Testar novamente com RLS habilitado
DO $
DECLARE
    test_count INTEGER;
BEGIN
    -- Testar master_organizations
    SELECT count(*) INTO test_count FROM master_organizations;
    RAISE NOTICE 'Com RLS - master_organizations: % registros', test_count;
    
    -- Testar master_users
    SELECT count(*) INTO test_count FROM master_users;
    RAISE NOTICE 'Com RLS - master_users: % registros', test_count;
    
    -- Testar master_plans
    SELECT count(*) INTO test_count FROM master_plans;
    RAISE NOTICE 'Com RLS - master_plans: % registros', test_count;
    
    RAISE NOTICE 'Políticas RLS corrigidas com sucesso!';
END $;

RAISE NOTICE 'Script fix-master-rls.sql executado com sucesso!';