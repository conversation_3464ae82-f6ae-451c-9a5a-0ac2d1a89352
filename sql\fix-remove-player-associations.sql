-- =====================================================
-- CORREÇÃO: FUNÇÃO remove_player_associations MAIS SEGURA
-- =====================================================
-- Esta versão da função não falha se alguma tabela não existir

CREATE OR REPLACE FUNCTION remove_player_associations(p_club_id INTEGER, p_player_id UUID)
RETURNS VOID AS $$
DECLARE
  v_user_id UUID;
  v_error_message TEXT;
BEGIN
  -- Log the operation
  RAISE NOTICE 'Removendo vinculações do jogador % do clube %', p_player_id, p_club_id;

  -- Get the user_id associated with this player
  SELECT user_id INTO v_user_id
  FROM players
  WHERE id = p_player_id AND club_id = p_club_id;

  -- 1. Remove from categories (se a tabela existir)
  BEGIN
    DELETE FROM player_categories
    WHERE club_id = p_club_id AND player_id = p_player_id;
    RAISE NOTICE 'Removido de player_categories';
  EXCEPTION
    WHEN undefined_table THEN
      RAISE NOTICE 'Tabela player_categories não existe, pulando...';
    WHEN OTHERS THEN
      GET STACKED DIAGNOSTICS v_error_message = MESSAGE_TEXT;
      RAISE NOTICE 'Erro ao remover de player_categories: %', v_error_message;
  END;

  -- 2. Remove from accommodations (se a tabela existir)
  BEGIN
    UPDATE player_accommodations
    SET status = 'completed', check_out_date = CURRENT_DATE
    WHERE club_id = p_club_id AND player_id = p_player_id AND status = 'active';
    RAISE NOTICE 'Atualizado player_accommodations';
  EXCEPTION
    WHEN undefined_table THEN
      RAISE NOTICE 'Tabela player_accommodations não existe, pulando...';
    WHEN OTHERS THEN
      GET STACKED DIAGNOSTICS v_error_message = MESSAGE_TEXT;
      RAISE NOTICE 'Erro ao atualizar player_accommodations: %', v_error_message;
  END;

  -- 3. Deactivate salaries (se a tabela existir)
  BEGIN
    UPDATE player_salaries
    SET status = 'inactive', end_date = CURRENT_DATE
    WHERE club_id = p_club_id AND player_id = p_player_id AND status = 'active';
    RAISE NOTICE 'Atualizado player_salaries';
  EXCEPTION
    WHEN undefined_table THEN
      RAISE NOTICE 'Tabela player_salaries não existe, pulando...';
    WHEN OTHERS THEN
      GET STACKED DIAGNOSTICS v_error_message = MESSAGE_TEXT;
      RAISE NOTICE 'Erro ao atualizar player_salaries: %', v_error_message;
  END;

  -- 4. Remove from training participants (se a tabela existir)
  BEGIN
    DELETE FROM training_players
    WHERE club_id = p_club_id AND player_id = p_player_id;
    RAISE NOTICE 'Removido de training_players';
  EXCEPTION
    WHEN undefined_table THEN
      RAISE NOTICE 'Tabela training_players não existe, pulando...';
    WHEN OTHERS THEN
      GET STACKED DIAGNOSTICS v_error_message = MESSAGE_TEXT;
      RAISE NOTICE 'Erro ao remover de training_players: %', v_error_message;
  END;

  -- 5. Remove from callups (se a tabela existir)
  BEGIN
    DELETE FROM callup_players
    WHERE club_id = p_club_id AND player_id = p_player_id
    AND callup_id IN (
      SELECT id FROM callups
      WHERE club_id = p_club_id AND match_date > NOW()
    );
    RAISE NOTICE 'Removido de callup_players';
  EXCEPTION
    WHEN undefined_table THEN
      RAISE NOTICE 'Tabela callup_players não existe, pulando...';
    WHEN OTHERS THEN
      GET STACKED DIAGNOSTICS v_error_message = MESSAGE_TEXT;
      RAISE NOTICE 'Erro ao remover de callup_players: %', v_error_message;
  END;

  -- 6. Deactivate player accounts (se a tabela existir)
  BEGIN
    UPDATE player_accounts
    SET expires_at = NOW()
    WHERE club_id = p_club_id AND player_id = p_player_id;
    RAISE NOTICE 'Atualizado player_accounts';
  EXCEPTION
    WHEN undefined_table THEN
      RAISE NOTICE 'Tabela player_accounts não existe, pulando...';
    WHEN OTHERS THEN
      GET STACKED DIAGNOSTICS v_error_message = MESSAGE_TEXT;
      RAISE NOTICE 'Erro ao atualizar player_accounts: %', v_error_message;
  END;

  -- 7. Remove user from club_members (se a tabela existir)
  IF v_user_id IS NOT NULL THEN
    BEGIN
      UPDATE club_members
      SET status = 'inativo'
      WHERE club_id = p_club_id AND user_id = v_user_id;
      RAISE NOTICE 'Usuário % suspenso do clube %', v_user_id, p_club_id;
    EXCEPTION
      WHEN undefined_table THEN
        RAISE NOTICE 'Tabela club_members não existe, pulando...';
      WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS v_error_message = MESSAGE_TEXT;
        RAISE NOTICE 'Erro ao atualizar club_members: %', v_error_message;
    END;
  END IF;

  -- 8. Remove from agenda events participants (se a tabela existir)
  BEGIN
    UPDATE agenda_events
    SET participants = array_remove(participants, p_player_id::text)
    WHERE club_id = p_club_id AND participants @> ARRAY[p_player_id::text];
    RAISE NOTICE 'Removido de agenda_events';
  EXCEPTION
    WHEN undefined_table THEN
      RAISE NOTICE 'Tabela agenda_events não existe, pulando...';
    WHEN OTHERS THEN
      GET STACKED DIAGNOSTICS v_error_message = MESSAGE_TEXT;
      RAISE NOTICE 'Erro ao remover de agenda_events: %', v_error_message;
  END;

  -- 9. Mark medical records as inactive (se a tabela existir)
  BEGIN
    UPDATE medical_records
    SET status = 'inactive'
    WHERE club_id = p_club_id AND player_id = p_player_id::text AND status != 'completed';
    RAISE NOTICE 'Atualizado medical_records';
  EXCEPTION
    WHEN undefined_table THEN
      RAISE NOTICE 'Tabela medical_records não existe, pulando...';
    WHEN OTHERS THEN
      GET STACKED DIAGNOSTICS v_error_message = MESSAGE_TEXT;
      RAISE NOTICE 'Erro ao atualizar medical_records: %', v_error_message;
  END;

  -- 10. Cancel pending salary advances (se a tabela existir)
  BEGIN
    UPDATE salary_advances
    SET status = 'cancelled'
    WHERE club_id = p_club_id AND person_id::text = p_player_id::text
    AND person_type = 'player' AND status = 'active';
    RAISE NOTICE 'Atualizado salary_advances';
  EXCEPTION
    WHEN undefined_table THEN
      RAISE NOTICE 'Tabela salary_advances não existe, pulando...';
    WHEN OTHERS THEN
      GET STACKED DIAGNOSTICS v_error_message = MESSAGE_TEXT;
      RAISE NOTICE 'Erro ao atualizar salary_advances: %', v_error_message;
  END;

  -- 11. Mark evaluation invitations as inactive (se a tabela existir)
  BEGIN
    UPDATE player_evaluation_invitations
    SET status = 'inactive'
    WHERE club_id = p_club_id AND player_id = p_player_id AND status = 'pending';
    RAISE NOTICE 'Atualizado player_evaluation_invitations';
  EXCEPTION
    WHEN undefined_table THEN
      RAISE NOTICE 'Tabela player_evaluation_invitations não existe, pulando...';
    WHEN OTHERS THEN
      GET STACKED DIAGNOSTICS v_error_message = MESSAGE_TEXT;
      RAISE NOTICE 'Erro ao atualizar player_evaluation_invitations: %', v_error_message;
  END;

  -- 12. Remove from meal participants (se a tabela existir) - NOVA FUNCIONALIDADE
  BEGIN
    DELETE FROM meal_participants
    WHERE club_id = p_club_id 
    AND player_uuid = p_player_id
    AND participant_type = 'player';
    
    GET DIAGNOSTICS v_error_message = ROW_COUNT;
    RAISE NOTICE 'Removido % registros de meal_participants', v_error_message;
  EXCEPTION
    WHEN undefined_table THEN
      RAISE NOTICE 'Tabela meal_participants não existe, pulando...';
    WHEN OTHERS THEN
      GET STACKED DIAGNOSTICS v_error_message = MESSAGE_TEXT;
      RAISE NOTICE 'Erro ao remover de meal_participants: %', v_error_message;
  END;

  RAISE NOTICE 'Vinculações removidas com sucesso para o jogador %', p_player_id;
END;
$$ LANGUAGE plpgsql;

-- Comentário explicativo
COMMENT ON FUNCTION remove_player_associations(INTEGER, UUID) IS 'Remove todas as associações de um jogador quando ele é marcado como inativo. Versão segura que não falha se alguma tabela não existir.';

-- Log da atualização
DO $$
BEGIN
  RAISE NOTICE 'Função remove_player_associations atualizada com tratamento de erros mais robusto!';
END $$;