-- =====================================================
-- SISTEMA DE ANÁLISE DE MERCADO - FUTEZADA
-- =====================================================
-- Este arquivo cria todas as tabelas necessárias para o
-- sistema de análise de mercado de jogadores e profissionais
-- =====================================================

-- 1. TABELA DE PERFIS DE MERCADO (PRINCIPAL)
-- =====================================================
CREATE TABLE IF NOT EXISTS market_profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- Dados básicos
  full_name VARCHAR(255) NOT NULL,
  nickname VARCHAR(100),
  email VARCHAR(255) UNIQUE NOT NULL,
  phone VARCHAR(20),
  birth_date DATE NOT NULL,
  nationality VARCHAR(100) DEFAULT 'Brasil',
  has_eu_passport BOOLEAN DEFAULT FALSE,
  
  -- Dados físicos
  height INTEGER, -- em centímetros
  weight DECIMAL(5,2), -- em kg
  
  -- Tipo de perfil
  profile_type VARCHAR(20) NOT NULL CHECK (profile_type IN ('player', 'technical_staff', 'support_staff')),
  
  -- Para jogadores
  position VARCHAR(50), -- Goleiro, Zagueiro, Lateral, Volante, Meia, Atacante
  preferred_foot VARCHAR(10) CHECK (preferred_foot IN ('direito', 'esquerdo', 'ambos')),
  
  -- Para staff técnico e suporte
  role VARCHAR(100), -- Técnico, Auxiliar, Preparador, etc.
  
  -- Experiência profissional
  years_experience INTEGER DEFAULT 0,
  last_club VARCHAR(255),
  career_highlights TEXT,
  
  -- Características técnicas (1-10) - apenas para jogadores
  speed_rating INTEGER CHECK (speed_rating >= 1 AND speed_rating <= 10),
  finishing_rating INTEGER CHECK (finishing_rating >= 1 AND finishing_rating <= 10),
  passing_rating INTEGER CHECK (passing_rating >= 1 AND passing_rating <= 10),
  defending_rating INTEGER CHECK (defending_rating >= 1 AND defending_rating <= 10),
  physical_rating INTEGER CHECK (physical_rating >= 1 AND physical_rating <= 10),
  
  -- Informações profissionais
  market_value_estimate DECIMAL(12,2), -- valor estimado em reais
  salary_expectation_min DECIMAL(10,2), -- salário mínimo pretendido
  salary_expectation_max DECIMAL(10,2), -- salário máximo pretendido
  available_for_travel BOOLEAN DEFAULT TRUE,
  available_for_relocation BOOLEAN DEFAULT TRUE,
  languages_spoken TEXT[], -- array de idiomas
  
  -- Status e disponibilidade
  availability_status VARCHAR(20) DEFAULT 'available' CHECK (
    availability_status IN ('available', 'contracted', 'on_loan', 'retired', 'injured')
  ),
  contract_end_date DATE,
  
  -- Certificações e licenças (para staff)
  certifications TEXT[],
  specializations TEXT[],
  work_methodology TEXT,
  
  -- Mídia e documentos
  profile_photo_url TEXT,
  additional_photos TEXT[], -- array de URLs
  highlight_video_url TEXT,
  full_game_video_url TEXT,
  resume_document_url TEXT,
  
  -- Descrição pessoal
  personal_description TEXT,
  
  -- Sistema de assinatura
  subscription_status VARCHAR(20) DEFAULT 'trial' CHECK (
    subscription_status IN ('trial', 'active', 'expired', 'suspended')
  ),
  subscription_start_date DATE DEFAULT CURRENT_DATE,
  subscription_end_date DATE,
  last_payment_date DATE,
  
  -- Metadados
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),
  is_active BOOLEAN DEFAULT TRUE,
  
  -- Índices para busca
  search_vector tsvector
);

-- 2. TABELA DE HISTÓRICO PROFISSIONAL
-- =====================================================
CREATE TABLE IF NOT EXISTS market_career_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  profile_id UUID REFERENCES market_profiles(id) ON DELETE CASCADE,
  
  club_name VARCHAR(255) NOT NULL,
  position_role VARCHAR(100), -- posição jogada ou função exercida
  start_date DATE,
  end_date DATE,
  achievements TEXT,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. TABELA DE BUSCAS SALVAS
-- =====================================================
CREATE TABLE IF NOT EXISTS market_saved_searches (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  
  search_name VARCHAR(255) NOT NULL,
  search_criteria JSONB NOT NULL, -- filtros aplicados
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. TABELA DE FAVORITOS
-- =====================================================
CREATE TABLE IF NOT EXISTS market_favorites (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  profile_id UUID REFERENCES market_profiles(id) ON DELETE CASCADE,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(user_id, profile_id)
);

-- 5. TABELA DE VISUALIZAÇÕES DE PERFIL
-- =====================================================
CREATE TABLE IF NOT EXISTS market_profile_views (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  profile_id UUID REFERENCES market_profiles(id) ON DELETE CASCADE,
  viewer_user_id UUID REFERENCES auth.users(id),
  viewer_ip_address INET,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. TABELA DE PAGAMENTOS DE ASSINATURA
-- =====================================================
CREATE TABLE IF NOT EXISTS market_subscription_payments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  profile_id UUID REFERENCES market_profiles(id) ON DELETE CASCADE,
  
  amount DECIMAL(10,2) NOT NULL,
  payment_method VARCHAR(50),
  payment_status VARCHAR(20) DEFAULT 'pending' CHECK (
    payment_status IN ('pending', 'completed', 'failed', 'refunded')
  ),
  payment_date DATE,
  reference_code VARCHAR(255),
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- ÍNDICES PARA PERFORMANCE
-- =====================================================

-- Índices para busca de perfis
CREATE INDEX IF NOT EXISTS idx_market_profiles_type ON market_profiles(profile_type);
CREATE INDEX IF NOT EXISTS idx_market_profiles_position ON market_profiles(position);
CREATE INDEX IF NOT EXISTS idx_market_profiles_availability ON market_profiles(availability_status);
CREATE INDEX IF NOT EXISTS idx_market_profiles_subscription ON market_profiles(subscription_status);
CREATE INDEX IF NOT EXISTS idx_market_profiles_birth_date ON market_profiles(birth_date);
CREATE INDEX IF NOT EXISTS idx_market_profiles_experience ON market_profiles(years_experience);
CREATE INDEX IF NOT EXISTS idx_market_profiles_market_value ON market_profiles(market_value_estimate);

-- Índice para busca textual
CREATE INDEX IF NOT EXISTS idx_market_profiles_search ON market_profiles USING gin(search_vector);

-- Índices para relacionamentos
CREATE INDEX IF NOT EXISTS idx_market_career_profile ON market_career_history(profile_id);
CREATE INDEX IF NOT EXISTS idx_market_favorites_user ON market_favorites(user_id);
CREATE INDEX IF NOT EXISTS idx_market_favorites_profile ON market_favorites(profile_id);
CREATE INDEX IF NOT EXISTS idx_market_views_profile ON market_profile_views(profile_id);

-- =====================================================
-- TRIGGERS E FUNÇÕES
-- =====================================================

-- Função para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_market_profile_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger para updated_at
CREATE TRIGGER trigger_market_profiles_updated_at
  BEFORE UPDATE ON market_profiles
  FOR EACH ROW
  EXECUTE FUNCTION update_market_profile_updated_at();

-- Função para atualizar search_vector
CREATE OR REPLACE FUNCTION update_market_profile_search_vector()
RETURNS TRIGGER AS $$
BEGIN
  NEW.search_vector := to_tsvector('portuguese', 
    COALESCE(NEW.full_name, '') || ' ' ||
    COALESCE(NEW.nickname, '') || ' ' ||
    COALESCE(NEW.position, '') || ' ' ||
    COALESCE(NEW.role, '') || ' ' ||
    COALESCE(NEW.last_club, '') || ' ' ||
    COALESCE(NEW.personal_description, '')
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger para search_vector
CREATE TRIGGER trigger_market_profiles_search_vector
  BEFORE INSERT OR UPDATE ON market_profiles
  FOR EACH ROW
  EXECUTE FUNCTION update_market_profile_search_vector();

-- =====================================================
-- POLÍTICAS DE SEGURANÇA (RLS)
-- =====================================================

-- Habilitar RLS nas tabelas principais
ALTER TABLE market_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE market_career_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE market_saved_searches ENABLE ROW LEVEL SECURITY;
ALTER TABLE market_favorites ENABLE ROW LEVEL SECURITY;
ALTER TABLE market_profile_views ENABLE ROW LEVEL SECURITY;
ALTER TABLE market_subscription_payments ENABLE ROW LEVEL SECURITY;

-- Política para visualização de perfis (todos os usuários autenticados podem ver perfis ativos)
CREATE POLICY "Authenticated users can view active market profiles"
ON market_profiles FOR SELECT
TO authenticated
USING (is_active = true AND subscription_status IN ('trial', 'active'));

-- Política para criação de perfis (usuários podem criar seus próprios perfis)
CREATE POLICY "Users can create their own market profiles"
ON market_profiles FOR INSERT
TO authenticated
WITH CHECK (created_by = auth.uid());

-- Política para edição de perfis (usuários podem editar apenas seus próprios perfis)
CREATE POLICY "Users can update their own market profiles"
ON market_profiles FOR UPDATE
TO authenticated
USING (created_by = auth.uid());

-- Política para histórico profissional
CREATE POLICY "Users can manage their own career history"
ON market_career_history FOR ALL
TO authenticated
USING (profile_id IN (
  SELECT id FROM market_profiles WHERE created_by = auth.uid()
));

-- Política para buscas salvas
CREATE POLICY "Users can manage their own saved searches"
ON market_saved_searches FOR ALL
TO authenticated
USING (user_id = auth.uid());

-- Política para favoritos
CREATE POLICY "Users can manage their own favorites"
ON market_favorites FOR ALL
TO authenticated
USING (user_id = auth.uid());

-- Política para visualizações (todos podem inserir, apenas donos podem ver)
CREATE POLICY "Anyone can log profile views"
ON market_profile_views FOR INSERT
TO authenticated
WITH CHECK (true);

CREATE POLICY "Profile owners can see their views"
ON market_profile_views FOR SELECT
TO authenticated
USING (profile_id IN (
  SELECT id FROM market_profiles WHERE created_by = auth.uid()
));

-- Política para pagamentos
CREATE POLICY "Users can manage their own subscription payments"
ON market_subscription_payments FOR ALL
TO authenticated
USING (profile_id IN (
  SELECT id FROM market_profiles WHERE created_by = auth.uid()
));
