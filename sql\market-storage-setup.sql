-- =====================================================
-- CONFIGURAÇÃO DE STORAGE PARA ANÁLISE DE MERCADO
-- =====================================================
-- Este arquivo configura o bucket de storage e políticas
-- para o sistema de análise de mercado
-- =====================================================

-- =====================================================
-- 1. CRIAÇÃO DO BUCKET
-- =====================================================
-- IMPORTANTE: Execute este comando no console do Supabase ou via API
-- INSERT INTO storage.buckets (id, name, public) 
-- VALUES ('market-profiles', 'Market Profiles Storage', true);

-- =====================================================
-- 2. POLÍTICAS DE ACESSO PARA O BUCKET market-profiles
-- =====================================================

-- Política para permitir leitura pública de todos os arquivos
INSERT INTO storage.policies (name, definition, bucket_id, operation)
VALUES (
  'Public Read Access for market-profiles',
  '(bucket_id = ''market-profiles''::text)',
  'market-profiles',
  'SELECT'
);

-- Política para permitir upload de arquivos por usuários autenticados
INSERT INTO storage.policies (name, definition, bucket_id, operation)
VALUES (
  'Authenticated Users can upload to market-profiles',
  '(bucket_id = ''market-profiles''::text) AND (auth.role() = ''authenticated''::text)',
  'market-profiles',
  'INSERT'
);

-- Política para permitir atualização de arquivos pelos próprios usuários
INSERT INTO storage.policies (name, definition, bucket_id, operation)
VALUES (
  'Users can update their own market-profiles files',
  '(bucket_id = ''market-profiles''::text) AND (auth.role() = ''authenticated''::text) AND (
    (storage.foldername(name))[1] = auth.uid()::text OR
    (storage.foldername(name))[1] = ''shared''
  )',
  'market-profiles',
  'UPDATE'
);

-- Política para permitir exclusão de arquivos pelos próprios usuários
INSERT INTO storage.policies (name, definition, bucket_id, operation)
VALUES (
  'Users can delete their own market-profiles files',
  '(bucket_id = ''market-profiles''::text) AND (auth.role() = ''authenticated''::text) AND (
    (storage.foldername(name))[1] = auth.uid()::text OR
    (storage.foldername(name))[1] = ''shared''
  )',
  'market-profiles',
  'DELETE'
);

-- =====================================================
-- 3. ESTRUTURA DE PASTAS RECOMENDADA
-- =====================================================
-- A estrutura de pastas será organizada da seguinte forma:
-- 
-- market-profiles/
-- ├── {user_id}/
-- │   ├── profile-photos/
-- │   │   ├── main-photo.jpg
-- │   │   └── additional-{timestamp}.jpg
-- │   ├── videos/
-- │   │   ├── highlights-{timestamp}.mp4
-- │   │   └── full-game-{timestamp}.mp4
-- │   ├── documents/
-- │   │   ├── resume-{timestamp}.pdf
-- │   │   └── certificates-{timestamp}.pdf
-- │   └── gallery/
-- │       ├── action-photo-1.jpg
-- │       ├── action-photo-2.jpg
-- │       └── ...
-- └── shared/
--     ├── default-avatars/
--     └── templates/

-- =====================================================
-- 4. CONFIGURAÇÕES DE LIMITE E SEGURANÇA
-- =====================================================

-- Configurar limites de upload (executar no console do Supabase)
-- UPDATE storage.buckets 
-- SET file_size_limit = 10485760 -- 10MB por arquivo
-- WHERE id = 'market-profiles';

-- =====================================================
-- 5. FUNÇÕES AUXILIARES PARA STORAGE
-- =====================================================

-- Função para gerar caminho de arquivo para fotos de perfil
CREATE OR REPLACE FUNCTION generate_market_profile_photo_path(
  user_id UUID,
  file_extension TEXT DEFAULT 'jpg'
)
RETURNS TEXT AS $$
BEGIN
  RETURN user_id::text || '/profile-photos/main-photo.' || file_extension;
END;
$$ LANGUAGE plpgsql;

-- Função para gerar caminho de arquivo para fotos adicionais
CREATE OR REPLACE FUNCTION generate_market_additional_photo_path(
  user_id UUID,
  file_extension TEXT DEFAULT 'jpg'
)
RETURNS TEXT AS $$
BEGIN
  RETURN user_id::text || '/profile-photos/additional-' || 
         extract(epoch from now())::bigint || '.' || file_extension;
END;
$$ LANGUAGE plpgsql;

-- Função para gerar caminho de arquivo para vídeos
CREATE OR REPLACE FUNCTION generate_market_video_path(
  user_id UUID,
  video_type TEXT, -- 'highlights' ou 'full-game'
  file_extension TEXT DEFAULT 'mp4'
)
RETURNS TEXT AS $$
BEGIN
  RETURN user_id::text || '/videos/' || video_type || '-' || 
         extract(epoch from now())::bigint || '.' || file_extension;
END;
$$ LANGUAGE plpgsql;

-- Função para gerar caminho de arquivo para documentos
CREATE OR REPLACE FUNCTION generate_market_document_path(
  user_id UUID,
  document_type TEXT, -- 'resume', 'certificate', etc.
  file_extension TEXT DEFAULT 'pdf'
)
RETURNS TEXT AS $$
BEGIN
  RETURN user_id::text || '/documents/' || document_type || '-' || 
         extract(epoch from now())::bigint || '.' || file_extension;
END;
$$ LANGUAGE plpgsql;

-- Função para gerar caminho de arquivo para galeria
CREATE OR REPLACE FUNCTION generate_market_gallery_path(
  user_id UUID,
  file_extension TEXT DEFAULT 'jpg'
)
RETURNS TEXT AS $$
BEGIN
  RETURN user_id::text || '/gallery/photo-' || 
         extract(epoch from now())::bigint || '.' || file_extension;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 6. TRIGGERS PARA LIMPEZA AUTOMÁTICA
-- =====================================================

-- Função para limpar arquivos órfãos quando um perfil é deletado
CREATE OR REPLACE FUNCTION cleanup_market_profile_files()
RETURNS TRIGGER AS $$
BEGIN
  -- Esta função pode ser expandida para deletar arquivos do storage
  -- quando um perfil é removido
  -- Por enquanto, apenas registra o evento
  INSERT INTO audit_logs (
    table_name,
    operation,
    old_data,
    user_id,
    timestamp
  ) VALUES (
    'market_profiles',
    'DELETE',
    row_to_json(OLD),
    auth.uid(),
    NOW()
  );
  
  RETURN OLD;
END;
$$ LANGUAGE plpgsql;

-- Trigger para limpeza automática
CREATE TRIGGER trigger_cleanup_market_profile_files
  AFTER DELETE ON market_profiles
  FOR EACH ROW
  EXECUTE FUNCTION cleanup_market_profile_files();

-- =====================================================
-- 7. VIEWS PARA RELATÓRIOS DE STORAGE
-- =====================================================

-- View para monitorar uso de storage por usuário
CREATE OR REPLACE VIEW market_storage_usage AS
SELECT 
  mp.created_by as user_id,
  mp.full_name,
  mp.email,
  COUNT(CASE WHEN mp.profile_photo_url IS NOT NULL THEN 1 END) as profile_photos,
  array_length(mp.additional_photos, 1) as additional_photos_count,
  COUNT(CASE WHEN mp.highlight_video_url IS NOT NULL THEN 1 END) as highlight_videos,
  COUNT(CASE WHEN mp.full_game_video_url IS NOT NULL THEN 1 END) as full_game_videos,
  COUNT(CASE WHEN mp.resume_document_url IS NOT NULL THEN 1 END) as resume_documents,
  mp.created_at,
  mp.subscription_status
FROM market_profiles mp
WHERE mp.is_active = true
GROUP BY mp.created_by, mp.full_name, mp.email, mp.created_at, mp.subscription_status
ORDER BY mp.created_at DESC;

-- =====================================================
-- 8. INSTRUÇÕES DE CONFIGURAÇÃO MANUAL
-- =====================================================

/*
INSTRUÇÕES PARA CONFIGURAÇÃO MANUAL NO SUPABASE:

1. Acesse o console do Supabase
2. Vá para Storage > Buckets
3. Clique em "Create bucket"
4. Configure:
   - Name: market-profiles
   - Public: true (para permitir acesso público às imagens)
   - File size limit: 10 MB
   - Allowed MIME types: image/*, video/*, application/pdf

5. Após criar o bucket, execute as políticas SQL acima

6. Teste o upload fazendo upload de um arquivo de teste

7. Verifique se as URLs públicas estão funcionando corretamente
*/
