-- =====================================================
-- SCRIPT: Criação das Tabelas Master para SaaS
-- DESCRIÇÃO: <PERSON><PERSON> todas as tabelas necessárias para o sistema master
-- VERSÃO: 1.0
-- DATA: 2025-01-28
-- =====================================================

-- 1. Tabela de organizações master (nossa empresa)
CREATE TABLE IF NOT EXISTS master_organizations (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  email VARCHAR(255) NOT NULL UNIQUE,
  phone VARCHAR(20),
  document VARCHAR(50), -- CNPJ
  address JSONB DEFAULT '{}',
  logo_url TEXT,
  website VARCHAR(255),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Tabela de planos disponíveis
CREATE TABLE IF NOT EXISTS master_plans (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  price DECIMAL(10,2) NOT NULL DEFAULT 0,
  billing_cycle VARCHAR(20) DEFAULT 'monthly' CHECK (billing_cycle IN ('monthly', 'yearly')),
  max_users INTEGER, -- NULL = ilimitado
  max_players INTEGER, -- NULL = ilimitado
  modules JSONB NOT NULL DEFAULT '{}', -- módulos disponíveis
  features JSONB NOT NULL DEFAULT '{}', -- recursos específicos
  is_active BOOLEAN DEFAULT true,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Tabela de usuários master (super admins)
CREATE TABLE IF NOT EXISTS master_users (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  organization_id INTEGER REFERENCES master_organizations(id) ON DELETE SET NULL,
  name VARCHAR(100) NOT NULL,
  email VARCHAR(255) NOT NULL UNIQUE,
  role VARCHAR(50) DEFAULT 'admin' CHECK (role IN ('super_admin', 'admin', 'support', 'viewer')),
  permissions JSONB DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  last_login_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Tabela de pagamentos master
CREATE TABLE IF NOT EXISTS master_payments (
  id SERIAL PRIMARY KEY,
  club_id INTEGER NOT NULL REFERENCES club_info(id) ON DELETE CASCADE,
  plan_id INTEGER NOT NULL REFERENCES master_plans(id) ON DELETE RESTRICT,
  amount DECIMAL(10,2) NOT NULL,
  due_date DATE NOT NULL,
  paid_date TIMESTAMP WITH TIME ZONE,
  status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'paid', 'overdue', 'cancelled')),
  payment_method VARCHAR(50), -- pix, boleto, cartao, transferencia, etc.
  transaction_id VARCHAR(255), -- ID da transação no gateway
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id)
);

-- 5. Tabela de logs de auditoria master
CREATE TABLE IF NOT EXISTS master_audit_logs (
  id SERIAL PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  action VARCHAR(100) NOT NULL, -- create_club, suspend_club, etc.
  entity_type VARCHAR(50) NOT NULL, -- club, plan, payment, etc.
  entity_id INTEGER,
  old_values JSONB,
  new_values JSONB,
  details JSONB DEFAULT '{}',
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. Tabela de logs de notificações
CREATE TABLE IF NOT EXISTS master_notification_logs (
  id SERIAL PRIMARY KEY,
  type VARCHAR(50) NOT NULL, -- payment_reminder, access_suspended, etc.
  recipient_email VARCHAR(255) NOT NULL,
  subject VARCHAR(255) NOT NULL,
  status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'failed')),
  error_message TEXT,
  club_id INTEGER REFERENCES club_info(id) ON DELETE SET NULL,
  payment_id INTEGER REFERENCES master_payments(id) ON DELETE SET NULL,
  sent_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. Tabela de configurações master
CREATE TABLE IF NOT EXISTS master_settings (
  id SERIAL PRIMARY KEY,
  key VARCHAR(100) NOT NULL UNIQUE,
  value JSONB NOT NULL,
  description TEXT,
  is_public BOOLEAN DEFAULT false, -- se pode ser acessado pelos clubes
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- ÍNDICES PARA PERFORMANCE
-- =====================================================

-- Índices para master_plans
CREATE INDEX IF NOT EXISTS idx_master_plans_active ON master_plans(is_active);
CREATE INDEX IF NOT EXISTS idx_master_plans_price ON master_plans(price);

-- Índices para master_users
CREATE INDEX IF NOT EXISTS idx_master_users_email ON master_users(email);
CREATE INDEX IF NOT EXISTS idx_master_users_role ON master_users(role);
CREATE INDEX IF NOT EXISTS idx_master_users_active ON master_users(is_active);

-- Índices para master_payments
CREATE INDEX IF NOT EXISTS idx_master_payments_club_id ON master_payments(club_id);
CREATE INDEX IF NOT EXISTS idx_master_payments_plan_id ON master_payments(plan_id);
CREATE INDEX IF NOT EXISTS idx_master_payments_status ON master_payments(status);
CREATE INDEX IF NOT EXISTS idx_master_payments_due_date ON master_payments(due_date);
CREATE INDEX IF NOT EXISTS idx_master_payments_status_due_date ON master_payments(status, due_date);

-- Índices para master_audit_logs
CREATE INDEX IF NOT EXISTS idx_master_audit_logs_user_id ON master_audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_master_audit_logs_action ON master_audit_logs(action);
CREATE INDEX IF NOT EXISTS idx_master_audit_logs_entity ON master_audit_logs(entity_type, entity_id);
CREATE INDEX IF NOT EXISTS idx_master_audit_logs_created_at ON master_audit_logs(created_at);

-- Índices para master_notification_logs
CREATE INDEX IF NOT EXISTS idx_master_notification_logs_type ON master_notification_logs(type);
CREATE INDEX IF NOT EXISTS idx_master_notification_logs_status ON master_notification_logs(status);
CREATE INDEX IF NOT EXISTS idx_master_notification_logs_club_id ON master_notification_logs(club_id);
CREATE INDEX IF NOT EXISTS idx_master_notification_logs_sent_at ON master_notification_logs(sent_at);

-- =====================================================
-- TRIGGERS PARA UPDATED_AT
-- =====================================================

-- Função para atualizar updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers para as tabelas
CREATE TRIGGER update_master_organizations_updated_at 
    BEFORE UPDATE ON master_organizations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_master_plans_updated_at 
    BEFORE UPDATE ON master_plans
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_master_users_updated_at 
    BEFORE UPDATE ON master_users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_master_payments_updated_at 
    BEFORE UPDATE ON master_payments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_master_settings_updated_at 
    BEFORE UPDATE ON master_settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- COMENTÁRIOS NAS TABELAS
-- =====================================================

COMMENT ON TABLE master_organizations IS 'Organizações master (nossa empresa)';
COMMENT ON TABLE master_plans IS 'Planos disponíveis para os clubes';
COMMENT ON TABLE master_users IS 'Usuários com acesso ao painel master';
COMMENT ON TABLE master_payments IS 'Pagamentos dos clubes pelos planos';
COMMENT ON TABLE master_audit_logs IS 'Logs de auditoria das ações no painel master';
COMMENT ON TABLE master_notification_logs IS 'Logs de notificações enviadas';
COMMENT ON TABLE master_settings IS 'Configurações globais do sistema master';

-- Comentários em colunas importantes
COMMENT ON COLUMN master_plans.modules IS 'JSON com módulos disponíveis no plano';
COMMENT ON COLUMN master_plans.features IS 'JSON com recursos específicos do plano';
COMMENT ON COLUMN master_users.permissions IS 'JSON com permissões específicas do usuário';
COMMENT ON COLUMN master_payments.status IS 'Status do pagamento: pending, paid, overdue, cancelled';
COMMENT ON COLUMN master_audit_logs.details IS 'JSON com detalhes adicionais da ação';

-- =====================================================
-- DADOS INICIAIS
-- =====================================================

-- Inserir organização master padrão
INSERT INTO master_organizations (name, email, phone, document) 
VALUES ('Game Day Nexus', '<EMAIL>', '+55 11 99999-9999', '00.000.000/0001-00')
ON CONFLICT (email) DO NOTHING;

-- Inserir planos padrão
INSERT INTO master_plans (name, description, price, billing_cycle, max_users, max_players, modules, features, sort_order) VALUES
(
  'Básico', 
  'Plano básico para clubes pequenos e escolinhas', 
  99.90, 
  'monthly',
  5,
  50,
  '{"dashboard": true, "players": true, "matches": true, "trainings": true}',
  '{"storage_limit": 5, "api_calls_limit": 1000, "custom_branding": false, "priority_support": false}',
  1
),
(
  'Profissional', 
  'Plano completo para clubes médios', 
  199.90,
  'monthly',
  20,
  200,
  '{"dashboard": true, "players": true, "matches": true, "trainings": true, "medical": true, "finances": true, "administrative": true, "accommodations": true}',
  '{"storage_limit": 20, "api_calls_limit": 5000, "custom_branding": true, "priority_support": false, "advanced_reports": true}',
  2
),
(
  'Enterprise', 
  'Plano completo para clubes grandes e profissionais', 
  399.90,
  'monthly',
  NULL, -- ilimitado
  NULL, -- ilimitado
  '{"dashboard": true, "players": true, "matches": true, "trainings": true, "medical": true, "finances": true, "administrative": true, "accommodations": true, "callups": true, "categories": true, "reports": true, "analytics": true, "communication": true, "inventory": true, "billing": true}',
  '{"storage_limit": null, "api_calls_limit": null, "custom_branding": true, "priority_support": true, "advanced_reports": true, "integrations": true, "backup_frequency": "daily"}',
  3
)
ON CONFLICT DO NOTHING;

-- Inserir configurações padrão
INSERT INTO master_settings (key, value, description, is_public) VALUES
('payment_grace_period_days', '7', 'Dias de tolerância após vencimento antes de suspender', false),
('trial_period_days', '14', 'Dias de período de teste gratuito', true),
('notification_sender_email', '"<EMAIL>"', 'Email remetente das notificações', false),
('notification_sender_name', '"Game Day Nexus"', 'Nome remetente das notificações', false),
('support_email', '"<EMAIL>"', 'Email de suporte', true),
('support_phone', '"+55 11 99999-9999"', 'Telefone de suporte', true),
('maintenance_mode', 'false', 'Modo de manutenção ativo', true),
('max_login_attempts', '5', 'Máximo de tentativas de login', false),
('session_timeout_minutes', '480', 'Timeout da sessão em minutos', false)
ON CONFLICT (key) DO NOTHING;

-- =====================================================
-- VERIFICAÇÃO FINAL
-- =====================================================

-- Verificar se todas as tabelas foram criadas
DO $$
DECLARE
    table_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO table_count
    FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name LIKE 'master_%';
    
    RAISE NOTICE 'Tabelas master criadas: %', table_count;
    
    IF table_count < 7 THEN
        RAISE EXCEPTION 'Nem todas as tabelas master foram criadas corretamente';
    END IF;
END $$;

RAISE NOTICE 'Script master-001-create-master-tables.sql executado com sucesso!';