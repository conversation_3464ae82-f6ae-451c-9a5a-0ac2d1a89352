-- =====================================================
-- SCRIPT: Atualização da Tabela club_info para SaaS
-- DESCRIÇÃO: Adiciona colunas necessárias para o sistema master
-- VERSÃO: 1.0
-- DATA: 2025-01-28
-- =====================================================

-- Adicionar colunas relacionadas ao sistema master na tabela club_info
ALTER TABLE club_info 
ADD COLUMN IF NOT EXISTS master_plan_id INTEGER REFERENCES master_plans(id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS subscription_status VARCHAR(20) DEFAULT 'active' CHECK (subscription_status IN ('active', 'suspended', 'cancelled', 'trial')),
ADD COLUMN IF NOT EXISTS subscription_start_date DATE,
ADD COLUMN IF NOT EXISTS subscription_end_date DATE,
ADD COLUMN IF NOT EXISTS payment_status VARCHAR(20) DEFAULT 'current' CHECK (payment_status IN ('current', 'overdue', 'cancelled')),
ADD COLUMN IF NOT EXISTS last_payment_date DATE,
ADD COLUMN IF NOT EXISTS next_payment_date DATE,
ADD COLUMN IF NOT EXISTS custom_modules JSONB DEFAULT '{}', -- módulos customizados além do plano
ADD COLUMN IF NOT EXISTS usage_limits JSONB DEFAULT '{}', -- limites específicos do clube
ADD COLUMN IF NOT EXISTS is_trial BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS trial_start_date DATE,
ADD COLUMN IF NOT EXISTS trial_end_date DATE,
ADD COLUMN IF NOT EXISTS billing_email VARCHAR(255), -- email específico para cobrança
ADD COLUMN IF NOT EXISTS billing_contact VARCHAR(100), -- contato responsável pela cobrança
ADD COLUMN IF NOT EXISTS notes TEXT, -- observações internas
ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES auth.users(id), -- quem criou o clube
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Adicionar trigger para updated_at se não existir
CREATE TRIGGER update_club_info_updated_at 
    BEFORE UPDATE ON club_info
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Criar índices para performance
CREATE INDEX IF NOT EXISTS idx_club_info_master_plan_id ON club_info(master_plan_id);
CREATE INDEX IF NOT EXISTS idx_club_info_subscription_status ON club_info(subscription_status);
CREATE INDEX IF NOT EXISTS idx_club_info_payment_status ON club_info(payment_status);
CREATE INDEX IF NOT EXISTS idx_club_info_trial ON club_info(is_trial);
CREATE INDEX IF NOT EXISTS idx_club_info_next_payment_date ON club_info(next_payment_date);

-- Atualizar clubes existentes com dados padrão
UPDATE club_info 
SET 
  subscription_status = COALESCE(subscription_status, 'active'),
  payment_status = COALESCE(payment_status, 'current'),
  billing_email = COALESCE(billing_email, email),
  updated_at = NOW()
WHERE master_plan_id IS NULL;

-- Atribuir plano básico para clubes sem plano (opcional)
-- UPDATE club_info 
-- SET master_plan_id = (SELECT id FROM master_plans WHERE name = 'Básico' LIMIT 1)
-- WHERE master_plan_id IS NULL;

-- Comentários nas novas colunas
COMMENT ON COLUMN club_info.master_plan_id IS 'Referência ao plano contratado pelo clube';
COMMENT ON COLUMN club_info.subscription_status IS 'Status da assinatura: active, suspended, cancelled, trial';
COMMENT ON COLUMN club_info.payment_status IS 'Status do pagamento: current, overdue, cancelled';
COMMENT ON COLUMN club_info.custom_modules IS 'JSON com módulos customizados além do plano padrão';
COMMENT ON COLUMN club_info.usage_limits IS 'JSON com limites específicos do clube';
COMMENT ON COLUMN club_info.is_trial IS 'Indica se o clube está em período de teste';
COMMENT ON COLUMN club_info.billing_email IS 'Email específico para questões de cobrança';
COMMENT ON COLUMN club_info.notes IS 'Observações internas sobre o clube';

-- =====================================================
-- FUNÇÕES AUXILIARES
-- =====================================================

-- Função para calcular próxima data de pagamento
CREATE OR REPLACE FUNCTION calculate_next_payment_date(
  club_id INTEGER,
  plan_billing_cycle VARCHAR(20) DEFAULT 'monthly'
)
RETURNS DATE AS $$
DECLARE
  last_payment DATE;
  next_payment DATE;
BEGIN
  -- Buscar última data de pagamento
  SELECT COALESCE(last_payment_date, subscription_start_date, created_at::date)
  INTO last_payment
  FROM club_info
  WHERE id = club_id;
  
  -- Calcular próxima data baseada no ciclo
  IF plan_billing_cycle = 'yearly' THEN
    next_payment := last_payment + INTERVAL '1 year';
  ELSE
    next_payment := last_payment + INTERVAL '1 month';
  END IF;
  
  RETURN next_payment;
END;
$$ LANGUAGE plpgsql;

-- Função para verificar se clube está em trial
CREATE OR REPLACE FUNCTION is_club_in_trial(club_id INTEGER)
RETURNS BOOLEAN AS $$
DECLARE
  club_trial BOOLEAN;
  trial_end DATE;
BEGIN
  SELECT is_trial, trial_end_date
  INTO club_trial, trial_end
  FROM club_info
  WHERE id = club_id;
  
  -- Se não está marcado como trial, retornar false
  IF NOT club_trial THEN
    RETURN FALSE;
  END IF;
  
  -- Se não tem data de fim do trial, considerar como trial ativo
  IF trial_end IS NULL THEN
    RETURN TRUE;
  END IF;
  
  -- Verificar se trial ainda está válido
  RETURN trial_end >= CURRENT_DATE;
END;
$$ LANGUAGE plpgsql;

-- Função para obter status consolidado do clube
CREATE OR REPLACE FUNCTION get_club_access_status(club_id INTEGER)
RETURNS TABLE(
  has_access BOOLEAN,
  status_reason VARCHAR(100),
  days_until_suspension INTEGER
) AS $$
DECLARE
  club_record RECORD;
  overdue_days INTEGER;
  grace_period INTEGER := 7; -- dias de tolerância
BEGIN
  -- Buscar dados do clube
  SELECT 
    c.*,
    p.name as plan_name,
    p.billing_cycle
  INTO club_record
  FROM club_info c
  LEFT JOIN master_plans p ON c.master_plan_id = p.id
  WHERE c.id = club_id;
  
  -- Se clube não existe
  IF NOT FOUND THEN
    RETURN QUERY SELECT FALSE, 'Clube não encontrado'::VARCHAR(100), 0;
    RETURN;
  END IF;
  
  -- Se assinatura cancelada
  IF club_record.subscription_status = 'cancelled' THEN
    RETURN QUERY SELECT FALSE, 'Assinatura cancelada'::VARCHAR(100), 0;
    RETURN;
  END IF;
  
  -- Se suspenso manualmente
  IF club_record.subscription_status = 'suspended' THEN
    RETURN QUERY SELECT FALSE, 'Acesso suspenso'::VARCHAR(100), 0;
    RETURN;
  END IF;
  
  -- Se em trial expirado
  IF club_record.is_trial AND club_record.trial_end_date < CURRENT_DATE THEN
    RETURN QUERY SELECT FALSE, 'Trial expirado'::VARCHAR(100), 0;
    RETURN;
  END IF;
  
  -- Se pagamento em atraso
  IF club_record.payment_status = 'overdue' THEN
    -- Calcular dias em atraso baseado na próxima data de pagamento
    overdue_days := CURRENT_DATE - COALESCE(club_record.next_payment_date, CURRENT_DATE);
    
    IF overdue_days > grace_period THEN
      RETURN QUERY SELECT FALSE, 'Pagamento em atraso'::VARCHAR(100), 0;
      RETURN;
    ELSE
      -- Ainda no período de tolerância
      RETURN QUERY SELECT TRUE, 'Pagamento em atraso - período de tolerância'::VARCHAR(100), grace_period - overdue_days;
      RETURN;
    END IF;
  END IF;
  
  -- Se chegou até aqui, tem acesso
  RETURN QUERY SELECT TRUE, 'Acesso liberado'::VARCHAR(100), NULL::INTEGER;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- VIEWS ÚTEIS
-- =====================================================

-- View com informações consolidadas dos clubes
CREATE OR REPLACE VIEW master_clubs_view AS
SELECT 
  c.id,
  c.name,
  c.email,
  c.phone,
  c.subscription_status,
  c.payment_status,
  c.is_trial,
  c.trial_end_date,
  c.last_payment_date,
  c.next_payment_date,
  c.updated_at,
  p.id as plan_id,
  p.name as plan_name,
  p.price as plan_price,
  p.billing_cycle,
  p.max_users,
  p.max_players,
  -- Contar usuários ativos
  (SELECT COUNT(*) FROM club_members cm WHERE cm.club_id = c.id AND cm.status = 'ativo') as current_users,
  -- Contar jogadores ativos
  (SELECT COUNT(*) FROM players pl WHERE pl.club_id = c.id AND pl.status != 'inativo') as current_players,
  -- Status de acesso
  (SELECT has_access FROM get_club_access_status(c.id)) as has_access,
  (SELECT status_reason FROM get_club_access_status(c.id)) as access_status_reason
FROM club_info c
LEFT JOIN master_plans p ON c.master_plan_id = p.id;

-- View com estatísticas de pagamentos por clube
CREATE OR REPLACE VIEW master_club_payment_stats AS
SELECT 
  c.id as club_id,
  c.name as club_name,
  COUNT(mp.id) as total_payments,
  COUNT(CASE WHEN mp.status = 'paid' THEN 1 END) as paid_payments,
  COUNT(CASE WHEN mp.status = 'overdue' THEN 1 END) as overdue_payments,
  COALESCE(SUM(CASE WHEN mp.status = 'paid' THEN mp.amount END), 0) as total_paid,
  COALESCE(SUM(CASE WHEN mp.status = 'overdue' THEN mp.amount END), 0) as total_overdue,
  MAX(mp.paid_date) as last_payment_date,
  MIN(CASE WHEN mp.status IN ('pending', 'overdue') THEN mp.due_date END) as next_due_date
FROM club_info c
LEFT JOIN master_payments mp ON c.id = mp.club_id
GROUP BY c.id, c.name;

-- =====================================================
-- VERIFICAÇÃO FINAL
-- =====================================================

-- Verificar se as colunas foram adicionadas
DO $$
DECLARE
    column_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO column_count
    FROM information_schema.columns 
    WHERE table_name = 'club_info' 
    AND column_name IN (
        'master_plan_id', 'subscription_status', 'payment_status', 
        'is_trial', 'custom_modules', 'usage_limits'
    );
    
    RAISE NOTICE 'Colunas master adicionadas à club_info: %', column_count;
    
    IF column_count < 6 THEN
        RAISE EXCEPTION 'Nem todas as colunas foram adicionadas corretamente';
    END IF;
END $$;