-- =====================================================
-- SCRIPT: Políticas RLS para Sistema Master
-- DESCRIÇÃO: Cria políticas de segurança para tabelas master
-- VERSÃO: 1.0
-- DATA: 2025-01-28
-- =====================================================

-- =====================================================
-- HABILITAR RLS NAS TABELAS MASTER
-- =====================================================

ALTER TABLE master_organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE master_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE master_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE master_payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE master_audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE master_notification_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE master_settings ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- POLÍTICAS PARA MASTER_ORGANIZATIONS
-- =====================================================

-- Usuários master podem ver todas as organizações
CREATE POLICY "master_users_can_view_organizations" ON master_organizations
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM master_users mu 
      WHERE mu.id = auth.uid() 
      AND mu.is_active = true
    )
  );

-- Apenas super_admin pode modificar organizações
CREATE POLICY "super_admin_can_modify_organizations" ON master_organizations
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM master_users mu 
      WHERE mu.id = auth.uid() 
      AND mu.role = 'super_admin' 
      AND mu.is_active = true
    )
  );

-- =====================================================
-- POLÍTICAS PARA MASTER_PLANS
-- =====================================================

-- Usuários master podem ver todos os planos
CREATE POLICY "master_users_can_view_plans" ON master_plans
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM master_users mu 
      WHERE mu.id = auth.uid() 
      AND mu.is_active = true
    )
  );

-- Usuários com permissão podem modificar planos
CREATE POLICY "authorized_users_can_modify_plans" ON master_plans
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM master_users mu 
      WHERE mu.id = auth.uid() 
      AND mu.role IN ('super_admin', 'admin')
      AND mu.is_active = true
    )
  );

-- =====================================================
-- POLÍTICAS PARA MASTER_USERS
-- =====================================================

-- Usuários master podem ver outros usuários master
CREATE POLICY "master_users_can_view_users" ON master_users
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM master_users mu 
      WHERE mu.id = auth.uid() 
      AND mu.is_active = true
    )
  );

-- Apenas super_admin pode criar/modificar usuários master
CREATE POLICY "super_admin_can_modify_users" ON master_users
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM master_users mu 
      WHERE mu.id = auth.uid() 
      AND mu.role = 'super_admin' 
      AND mu.is_active = true
    )
  );

-- Usuários podem atualizar seus próprios dados básicos
CREATE POLICY "users_can_update_own_data" ON master_users
  FOR UPDATE
  TO authenticated
  USING (id = auth.uid())
  WITH CHECK (
    id = auth.uid() 
    AND OLD.role = NEW.role -- não pode alterar próprio role
    AND OLD.is_active = NEW.is_active -- não pode alterar próprio status
  );

-- =====================================================
-- POLÍTICAS PARA MASTER_PAYMENTS
-- =====================================================

-- Usuários master podem ver todos os pagamentos
CREATE POLICY "master_users_can_view_payments" ON master_payments
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM master_users mu 
      WHERE mu.id = auth.uid() 
      AND mu.is_active = true
    )
  );

-- Usuários com permissão podem modificar pagamentos
CREATE POLICY "authorized_users_can_modify_payments" ON master_payments
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM master_users mu 
      WHERE mu.id = auth.uid() 
      AND mu.role IN ('super_admin', 'admin')
      AND mu.is_active = true
    )
  );

-- =====================================================
-- POLÍTICAS PARA MASTER_AUDIT_LOGS
-- =====================================================

-- Usuários master podem ver logs de auditoria
CREATE POLICY "master_users_can_view_audit_logs" ON master_audit_logs
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM master_users mu 
      WHERE mu.id = auth.uid() 
      AND mu.is_active = true
    )
  );

-- Sistema pode inserir logs (sem restrição para inserção)
CREATE POLICY "system_can_insert_audit_logs" ON master_audit_logs
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

-- Apenas super_admin pode deletar logs
CREATE POLICY "super_admin_can_delete_audit_logs" ON master_audit_logs
  FOR DELETE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM master_users mu 
      WHERE mu.id = auth.uid() 
      AND mu.role = 'super_admin' 
      AND mu.is_active = true
    )
  );

-- =====================================================
-- POLÍTICAS PARA MASTER_NOTIFICATION_LOGS
-- =====================================================

-- Usuários master podem ver logs de notificação
CREATE POLICY "master_users_can_view_notification_logs" ON master_notification_logs
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM master_users mu 
      WHERE mu.id = auth.uid() 
      AND mu.is_active = true
    )
  );

-- Sistema pode inserir/atualizar logs de notificação
CREATE POLICY "system_can_modify_notification_logs" ON master_notification_logs
  FOR ALL
  TO authenticated
  USING (true)
  WITH CHECK (true);

-- =====================================================
-- POLÍTICAS PARA MASTER_SETTINGS
-- =====================================================

-- Usuários master podem ver configurações
CREATE POLICY "master_users_can_view_settings" ON master_settings
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM master_users mu 
      WHERE mu.id = auth.uid() 
      AND mu.is_active = true
    )
  );

-- Apenas super_admin pode modificar configurações
CREATE POLICY "super_admin_can_modify_settings" ON master_settings
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM master_users mu 
      WHERE mu.id = auth.uid() 
      AND mu.role = 'super_admin' 
      AND mu.is_active = true
    )
  );

-- =====================================================
-- POLÍTICAS PARA CLUB_INFO (Acesso Master)
-- =====================================================

-- Usuários master podem ver todos os clubes
CREATE POLICY "master_users_can_view_all_clubs" ON club_info
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM master_users mu 
      WHERE mu.id = auth.uid() 
      AND mu.is_active = true
    )
  );

-- Usuários master podem modificar clubes
CREATE POLICY "master_users_can_modify_clubs" ON club_info
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM master_users mu 
      WHERE mu.id = auth.uid() 
      AND mu.role IN ('super_admin', 'admin')
      AND mu.is_active = true
    )
  );

-- =====================================================
-- FUNÇÕES AUXILIARES PARA RLS
-- =====================================================

-- Função para verificar se usuário é master
CREATE OR REPLACE FUNCTION is_master_user()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM master_users 
    WHERE id = auth.uid() 
    AND is_active = true
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Função para verificar role do usuário master
CREATE OR REPLACE FUNCTION get_master_user_role()
RETURNS VARCHAR(50) AS $$
BEGIN
  RETURN (
    SELECT role FROM master_users 
    WHERE id = auth.uid() 
    AND is_active = true
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Função para verificar se usuário tem permissão específica
CREATE OR REPLACE FUNCTION master_user_has_permission(permission_key TEXT)
RETURNS BOOLEAN AS $$
DECLARE
  user_permissions JSONB;
  user_role VARCHAR(50);
BEGIN
  -- Buscar role e permissões do usuário
  SELECT role, permissions 
  INTO user_role, user_permissions
  FROM master_users 
  WHERE id = auth.uid() 
  AND is_active = true;
  
  -- Se não encontrou usuário, não tem permissão
  IF user_role IS NULL THEN
    RETURN FALSE;
  END IF;
  
  -- Super admin tem todas as permissões
  IF user_role = 'super_admin' THEN
    RETURN TRUE;
  END IF;
  
  -- Verificar permissão específica no JSON
  RETURN COALESCE((user_permissions ->> permission_key)::BOOLEAN, FALSE);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- GRANTS PARA FUNÇÕES
-- =====================================================

-- Permitir que usuários autenticados executem as funções
GRANT EXECUTE ON FUNCTION is_master_user() TO authenticated;
GRANT EXECUTE ON FUNCTION get_master_user_role() TO authenticated;
GRANT EXECUTE ON FUNCTION master_user_has_permission(TEXT) TO authenticated;

-- Permitir execução das funções principais
GRANT EXECUTE ON FUNCTION generate_monthly_payments() TO authenticated;
GRANT EXECUTE ON FUNCTION update_overdue_payments() TO authenticated;
GRANT EXECUTE ON FUNCTION process_payment(INTEGER, VARCHAR(50), VARCHAR(255)) TO authenticated;
GRANT EXECUTE ON FUNCTION create_club_with_plan(VARCHAR(255), VARCHAR(255), VARCHAR(20), VARCHAR(50), INTEGER, BOOLEAN, INTEGER, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_master_dashboard_stats() TO authenticated;
GRANT EXECUTE ON FUNCTION cleanup_old_data(INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION daily_maintenance() TO authenticated;

-- =====================================================
-- POLÍTICAS ESPECIAIS PARA VIEWS
-- =====================================================

-- Permitir acesso às views para usuários master
GRANT SELECT ON master_clubs_view TO authenticated;
GRANT SELECT ON master_club_payment_stats TO authenticated;

-- =====================================================
-- VERIFICAÇÃO DAS POLÍTICAS
-- =====================================================

-- Função para verificar se todas as políticas foram criadas
CREATE OR REPLACE FUNCTION verify_master_rls_policies()
RETURNS TABLE(
  table_name TEXT,
  policies_count BIGINT,
  rls_enabled BOOLEAN
) AS $
BEGIN
  RETURN QUERY
  SELECT 
    t.tablename::TEXT,
    COUNT(p.policyname) as policies_count,
    t.rowsecurity as rls_enabled
  FROM pg_tables t
  LEFT JOIN pg_policies p ON t.tablename = p.tablename
  WHERE t.schemaname = 'public' 
  AND t.tablename LIKE 'master_%'
  GROUP BY t.tablename, t.rowsecurity
  ORDER BY t.tablename;
END;
$ LANGUAGE plpgsql;

-- Executar verificação
DO $
DECLARE
  policy_check RECORD;
BEGIN
  RAISE NOTICE 'Verificando políticas RLS criadas:';
  
  FOR policy_check IN SELECT * FROM verify_master_rls_policies() LOOP
    RAISE NOTICE 'Tabela: %, Políticas: %, RLS: %', 
      policy_check.table_name, 
      policy_check.policies_count, 
      policy_check.rls_enabled;
  END LOOP;
END $;

-- =====================================================
-- LIMPEZA
-- =====================================================

-- Remover função de verificação (não é mais necessária)
DROP FUNCTION IF EXISTS verify_master_rls_policies();

RAISE NOTICE 'Script master-003-create-rls-policies.sql executado com sucesso!';