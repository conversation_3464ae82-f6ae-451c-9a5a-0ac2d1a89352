-- =====================================================
-- SCRIPT: Configuração Simples para Teste Master
-- DESCRIÇÃO: Configuração básica sem RLS para teste
-- VERSÃO: 1.0
-- DATA: 2025-01-28
-- =====================================================

-- Desabilitar RLS temporariamente para teste
ALTER TABLE master_organizations DISABLE ROW LEVEL SECURITY;
ALTER TABLE master_plans DISABLE ROW LEVEL SECURITY;
ALTER TABLE master_users DISABLE ROW LEVEL SECURITY;
ALTER TABLE master_payments DISABLE ROW LEVEL SECURITY;
ALTER TABLE master_audit_logs DISABLE ROW LEVEL SECURITY;
ALTER TABLE master_notification_logs DISABLE ROW LEVEL SECURITY;
ALTER TABLE master_settings DISABLE ROW LEVEL SECURITY;

-- <PERSON><PERSON><PERSON><PERSON> que as tabelas existem
SELECT 'Tabelas master verificadas' as status;

-- Verificar se o usuário master existe
SELECT 
  id, 
  name, 
  email, 
  role, 
  is_active 
FROM master_users 
WHERE email = '<EMAIL>';