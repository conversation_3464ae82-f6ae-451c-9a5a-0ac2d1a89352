-- =====================================================
-- SCRIPT: Dados de Teste para Sistema Master
-- DESCRIÇÃO: Cria dados de exemplo para desenvolvimento e testes
-- VERSÃO: 1.0
-- DATA: 2025-01-28
-- ATENÇÃO: Execute apenas em ambiente de desenvolvimento!
-- =====================================================

-- Verificar se estamos em ambiente de desenvolvimento
DO $
BEGIN
  -- Comentar esta linha para permitir execução em produção (NÃO RECOMENDADO)
  IF current_database() NOT LIKE '%dev%' AND current_database() NOT LIKE '%test%' THEN
    RAISE EXCEPTION 'Este script só deve ser executado em ambiente de desenvolvimento!';
  END IF;
END $;

-- =====================================================
-- USUÁRIOS MASTER DE TESTE
-- =====================================================

-- Inserir usuários master de teste (você precisará criar estes usuários no Supabase Auth primeiro)
INSERT INTO master_users (id, organization_id, name, email, role, permissions, is_active) VALUES
(
  '00000000-0000-0000-0000-000000000001'::UUID, -- Substitua por UUID real do Supabase Auth
  1,
  'Super Admin',
  '<EMAIL>',
  'super_admin',
  '{"all": true}',
  true
),
(
  '00000000-0000-0000-0000-000000000002'::UUID, -- Substitua por UUID real do Supabase Auth
  1,
  'Admin Master',
  '<EMAIL>',
  'admin',
  '{"clubs": true, "plans": true, "billing": true, "analytics": true}',
  true
),
(
  '00000000-0000-0000-0000-000000000003'::UUID, -- Substitua por UUID real do Supabase Auth
  1,
  'Suporte Técnico',
  '<EMAIL>',
  'support',
  '{"clubs": true, "billing": false, "analytics": true}',
  true
)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  email = EXCLUDED.email,
  role = EXCLUDED.role,
  permissions = EXCLUDED.permissions,
  updated_at = NOW();

-- =====================================================
-- CLUBES DE TESTE
-- =====================================================

-- Inserir clubes de teste com diferentes status
INSERT INTO club_info (
  name, email, phone, document, 
  master_plan_id, subscription_status, subscription_start_date, 
  payment_status, is_trial, trial_start_date, trial_end_date,
  billing_email, created_by
) VALUES
-- Clube ativo no plano básico
(
  'FC Exemplo Básico',
  '<EMAIL>',
  '+55 11 98765-4321',
  '12.345.678/0001-90',
  (SELECT id FROM master_plans WHERE name = 'Básico' LIMIT 1),
  'active',
  CURRENT_DATE - INTERVAL '30 days',
  'current',
  false,
  NULL,
  NULL,
  '<EMAIL>',
  '00000000-0000-0000-0000-000000000001'::UUID
),
-- Clube em trial
(
  'Esporte Clube Trial',
  '<EMAIL>',
  '+55 21 99876-5432',
  '98.765.432/0001-10',
  (SELECT id FROM master_plans WHERE name = 'Profissional' LIMIT 1),
  'trial',
  CURRENT_DATE - INTERVAL '5 days',
  'current',
  true,
  CURRENT_DATE - INTERVAL '5 days',
  CURRENT_DATE + INTERVAL '9 days',
  '<EMAIL>',
  '00000000-0000-0000-0000-000000000001'::UUID
),
-- Clube com pagamento em atraso
(
  'Clube Atlético Atraso',
  '<EMAIL>',
  '+55 31 97654-3210',
  '11.222.333/0001-44',
  (SELECT id FROM master_plans WHERE name = 'Profissional' LIMIT 1),
  'active',
  CURRENT_DATE - INTERVAL '60 days',
  'overdue',
  false,
  NULL,
  NULL,
  '<EMAIL>',
  '00000000-0000-0000-0000-000000000002'::UUID
),
-- Clube suspenso
(
  'FC Suspenso',
  '<EMAIL>',
  '+55 41 96543-2109',
  '55.666.777/0001-88',
  (SELECT id FROM master_plans WHERE name = 'Básico' LIMIT 1),
  'suspended',
  CURRENT_DATE - INTERVAL '90 days',
  'overdue',
  false,
  NULL,
  NULL,
  '<EMAIL>',
  '00000000-0000-0000-0000-000000000002'::UUID
),
-- Clube enterprise ativo
(
  'Grande Clube FC',
  '<EMAIL>',
  '+55 51 95432-1098',
  '99.888.777/0001-66',
  (SELECT id FROM master_plans WHERE name = 'Enterprise' LIMIT 1),
  'active',
  CURRENT_DATE - INTERVAL '180 days',
  'current',
  false,
  NULL,
  NULL,
  '<EMAIL>',
  '00000000-0000-0000-0000-000000000001'::UUID
)
ON CONFLICT (email) DO UPDATE SET
  name = EXCLUDED.name,
  phone = EXCLUDED.phone,
  master_plan_id = EXCLUDED.master_plan_id,
  subscription_status = EXCLUDED.subscription_status,
  updated_at = NOW();

-- =====================================================
-- PAGAMENTOS DE TESTE
-- =====================================================

-- Gerar histórico de pagamentos para os clubes
DO $
DECLARE
  club_record RECORD;
  payment_date DATE;
  i INTEGER;
BEGIN
  -- Para cada clube ativo, criar histórico de pagamentos
  FOR club_record IN 
    SELECT c.id, c.name, c.master_plan_id, c.subscription_start_date, p.price, p.billing_cycle
    FROM club_info c
    INNER JOIN master_plans p ON c.master_plan_id = p.id
    WHERE c.subscription_status IN ('active', 'suspended')
    AND NOT c.is_trial
  LOOP
    -- Criar pagamentos mensais desde o início da assinatura
    payment_date := club_record.subscription_start_date;
    i := 0;
    
    WHILE payment_date <= CURRENT_DATE AND i < 12 LOOP -- máximo 12 pagamentos
      INSERT INTO master_payments (
        club_id,
        plan_id,
        amount,
        due_date,
        paid_date,
        status,
        payment_method,
        transaction_id,
        created_at
      ) VALUES (
        club_record.id,
        club_record.master_plan_id,
        club_record.price,
        payment_date + INTERVAL '5 days', -- vencimento 5 dias após
        CASE 
          -- Clubes suspensos têm pagamentos não pagos
          WHEN club_record.name LIKE '%Suspenso%' OR club_record.name LIKE '%Atraso%' THEN
            CASE WHEN payment_date < CURRENT_DATE - INTERVAL '30 days' THEN payment_date + INTERVAL '3 days' ELSE NULL END
          ELSE payment_date + INTERVAL '2 days' -- pagamentos normais
        END,
        CASE 
          WHEN club_record.name LIKE '%Suspenso%' OR club_record.name LIKE '%Atraso%' THEN
            CASE WHEN payment_date < CURRENT_DATE - INTERVAL '30 days' THEN 'paid' ELSE 'overdue' END
          ELSE 'paid'
        END,
        CASE 
          WHEN club_record.name LIKE '%Suspenso%' OR club_record.name LIKE '%Atraso%' THEN
            CASE WHEN payment_date < CURRENT_DATE - INTERVAL '30 days' THEN 'pix' ELSE NULL END
          ELSE 'pix'
        END,
        CASE 
          WHEN club_record.name LIKE '%Suspenso%' OR club_record.name LIKE '%Atraso%' THEN
            CASE WHEN payment_date < CURRENT_DATE - INTERVAL '30 days' THEN 'TXN_' || LPAD(i::TEXT, 6, '0') ELSE NULL END
          ELSE 'TXN_' || LPAD(i::TEXT, 6, '0')
        END,
        payment_date
      );
      
      -- Próximo pagamento
      IF club_record.billing_cycle = 'yearly' THEN
        payment_date := payment_date + INTERVAL '1 year';
      ELSE
        payment_date := payment_date + INTERVAL '1 month';
      END IF;
      
      i := i + 1;
    END LOOP;
  END LOOP;
END $;

-- =====================================================
-- LOGS DE AUDITORIA DE TESTE
-- =====================================================

-- Inserir alguns logs de auditoria de exemplo
INSERT INTO master_audit_logs (
  user_id, action, entity_type, entity_id, 
  old_values, new_values, details, created_at
) VALUES
(
  '00000000-0000-0000-0000-000000000001'::UUID,
  'club_created',
  'club',
  (SELECT id FROM club_info WHERE name = 'FC Exemplo Básico' LIMIT 1),
  NULL,
  '{"name": "FC Exemplo Básico", "plan": "Básico"}',
  '{"created_via": "master_panel", "initial_setup": true}',
  CURRENT_DATE - INTERVAL '30 days'
),
(
  '00000000-0000-0000-0000-000000000002'::UUID,
  'club_suspended',
  'club',
  (SELECT id FROM club_info WHERE name = 'FC Suspenso' LIMIT 1),
  '{"subscription_status": "active"}',
  '{"subscription_status": "suspended"}',
  '{"reason": "Pagamento em atraso há mais de 7 dias", "automatic": true}',
  CURRENT_DATE - INTERVAL '15 days'
),
(
  '00000000-0000-0000-0000-000000000001'::UUID,
  'payment_processed',
  'payment',
  (SELECT id FROM master_payments WHERE status = 'paid' LIMIT 1),
  '{"status": "pending"}',
  '{"status": "paid", "paid_date": "2025-01-15"}',
  '{"payment_method": "pix", "processed_by": "admin"}',
  CURRENT_DATE - INTERVAL '10 days'
);

-- =====================================================
-- LOGS DE NOTIFICAÇÃO DE TESTE
-- =====================================================

-- Inserir logs de notificações enviadas
INSERT INTO master_notification_logs (
  type, recipient_email, subject, status, 
  club_id, payment_id, sent_at, created_at
) VALUES
(
  'payment_reminder',
  '<EMAIL>',
  'Lembrete de Pagamento - Game Day Nexus',
  'sent',
  (SELECT id FROM club_info WHERE name = 'Clube Atlético Atraso' LIMIT 1),
  (SELECT id FROM master_payments WHERE status = 'overdue' LIMIT 1),
  CURRENT_DATE - INTERVAL '2 days',
  CURRENT_DATE - INTERVAL '2 days'
),
(
  'access_suspended',
  '<EMAIL>',
  'Acesso Suspenso - Game Day Nexus',
  'sent',
  (SELECT id FROM club_info WHERE name = 'FC Suspenso' LIMIT 1),
  NULL,
  CURRENT_DATE - INTERVAL '15 days',
  CURRENT_DATE - INTERVAL '15 days'
),
(
  'trial_ending',
  '<EMAIL>',
  'Seu período de teste está terminando - Game Day Nexus',
  'sent',
  (SELECT id FROM club_info WHERE name = 'Esporte Clube Trial' LIMIT 1),
  NULL,
  CURRENT_DATE - INTERVAL '1 day',
  CURRENT_DATE - INTERVAL '1 day'
);

-- =====================================================
-- ATUALIZAR DATAS DE PRÓXIMO PAGAMENTO
-- =====================================================

-- Atualizar próximas datas de pagamento baseado no histórico
UPDATE club_info 
SET 
  last_payment_date = (
    SELECT MAX(paid_date) 
    FROM master_payments 
    WHERE club_id = club_info.id 
    AND status = 'paid'
  ),
  next_payment_date = (
    SELECT MIN(due_date) 
    FROM master_payments 
    WHERE club_id = club_info.id 
    AND status IN ('pending', 'overdue')
  )
WHERE master_plan_id IS NOT NULL;

-- =====================================================
-- CONFIGURAÇÕES ADICIONAIS DE TESTE
-- =====================================================

-- Inserir algumas configurações específicas para teste
INSERT INTO master_settings (key, value, description, is_public) VALUES
('test_mode', 'true', 'Indica se o sistema está em modo de teste', false),
('demo_data_loaded', 'true', 'Indica se os dados de demonstração foram carregados', false),
('last_demo_reset', '"2025-01-28"', 'Data da última reinicialização dos dados demo', false)
ON CONFLICT (key) DO UPDATE SET
  value = EXCLUDED.value,
  updated_at = NOW();

-- =====================================================
-- ESTATÍSTICAS FINAIS
-- =====================================================

-- Mostrar estatísticas dos dados criados
DO $
DECLARE
  stats RECORD;
BEGIN
  SELECT * INTO stats FROM get_master_dashboard_stats();
  
  RAISE NOTICE '=== DADOS DE TESTE CRIADOS ===';
  RAISE NOTICE 'Total de clubes: %', stats.total_clubs;
  RAISE NOTICE 'Clubes ativos: %', stats.active_clubs;
  RAISE NOTICE 'Clubes em trial: %', stats.trial_clubs;
  RAISE NOTICE 'Clubes suspensos: %', stats.suspended_clubs;
  RAISE NOTICE 'Receita total: R$ %', stats.total_revenue;
  RAISE NOTICE 'Pagamentos pendentes: %', stats.pending_payments;
  RAISE NOTICE 'Pagamentos em atraso: %', stats.overdue_payments;
  RAISE NOTICE '================================';
END $;

-- =====================================================
-- INSTRUÇÕES FINAIS
-- =====================================================

RAISE NOTICE 'DADOS DE TESTE CRIADOS COM SUCESSO!';
RAISE NOTICE '';
RAISE NOTICE 'PRÓXIMOS PASSOS:';
RAISE NOTICE '1. Criar os usuários no Supabase Auth com os emails:';
RAISE NOTICE '   - <EMAIL>';
RAISE NOTICE '   - <EMAIL>';
RAISE NOTICE '   - <EMAIL>';
RAISE NOTICE '';
RAISE NOTICE '2. Atualizar os UUIDs na tabela master_users com os IDs reais';
RAISE NOTICE '';
RAISE NOTICE '3. Testar o login no painel master';
RAISE NOTICE '';
RAISE NOTICE 'ATENÇÃO: Este script criou dados fictícios apenas para desenvolvimento!';

RAISE NOTICE 'Script master-004-test-data.sql executado com sucesso!';