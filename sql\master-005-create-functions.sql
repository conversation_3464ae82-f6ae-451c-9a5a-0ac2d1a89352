-- =====================================================
-- SCRIPT: Funções e Procedures para Sistema Master
-- DESCRIÇÃO: Cria funções auxiliares para automação
-- VERSÃO: 1.0
-- DATA: 2025-01-28
-- =====================================================

-- =====================================================
-- FUNÇÃO: <PERSON><PERSON><PERSON> Mensais Automaticamente
-- =====================================================

CREATE OR REPLACE FUNCTION generate_monthly_payments()
RETURNS TABLE(count INTEGER, message TEXT) AS $$
DECLARE
  club_record RECORD;
  payment_count INTEGER := 0;
  current_date DATE := CURRENT_DATE;
  due_date DATE;
BEGIN
  -- Buscar clubes ativos que precisam de cobrança
  FOR club_record IN
    SELECT 
      c.id,
      c.name,
      c.master_plan_id,
      c.subscription_status,
      c.payment_status,
      c.next_payment_date,
      c.is_trial,
      c.trial_end_date,
      p.price,
      p.billing_cycle
    FROM club_info c
    INNER JOIN master_plans p ON c.master_plan_id = p.id
    WHERE c.subscription_status = 'active'
    AND c.payment_status IN ('current', 'overdue')
    AND (
      -- Clubes com próxima data de pagamento hoje ou no passado
      c.next_payment_date <= current_date
      OR
      -- Clubes sem próxima data de pagamento definida
      c.next_payment_date IS NULL
    )
    AND (
      -- Não está em trial ou trial já expirou
      NOT c.is_trial OR c.trial_end_date < current_date
    )
  LOOP
    -- Calcular data de vencimento (5 dias após a data atual)
    due_date := current_date + INTERVAL '5 days';
    
    -- Verificar se já existe cobrança pendente para este mês
    IF NOT EXISTS (
      SELECT 1 FROM master_payments 
      WHERE club_id = club_record.id 
      AND status IN ('pending', 'overdue')
      AND due_date >= current_date
    ) THEN
      -- Criar nova cobrança
      INSERT INTO master_payments (
        club_id,
        plan_id,
        amount,
        due_date,
        status,
        created_at
      ) VALUES (
        club_record.id,
        club_record.master_plan_id,
        club_record.price,
        due_date,
        'pending',
        NOW()
      );
      
      -- Atualizar próxima data de pagamento do clube
      UPDATE club_info 
      SET 
        next_payment_date = CASE 
          WHEN club_record.billing_cycle = 'yearly' THEN current_date + INTERVAL '1 year'
          ELSE current_date + INTERVAL '1 month'
        END,
        updated_at = NOW()
      WHERE id = club_record.id;
      
      payment_count := payment_count + 1;
    END IF;
  END LOOP;
  
  RETURN QUERY SELECT payment_count, format('Geradas %s cobranças', payment_count);
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FUNÇÃO: Atualizar Status de Pagamentos em Atraso
-- =====================================================

CREATE OR REPLACE FUNCTION update_overdue_payments()
RETURNS TABLE(updated_count INTEGER, suspended_count INTEGER) AS $$
DECLARE
  updated_payments INTEGER := 0;
  suspended_clubs INTEGER := 0;
  grace_period INTEGER := 7; -- dias de tolerância
  payment_record RECORD;
BEGIN
  -- Marcar pagamentos como overdue
  UPDATE master_payments 
  SET 
    status = 'overdue',
    updated_at = NOW()
  WHERE status = 'pending' 
  AND due_date < CURRENT_DATE;
  
  GET DIAGNOSTICS updated_payments = ROW_COUNT;
  
  -- Buscar clubes com pagamentos muito em atraso para suspender
  FOR payment_record IN
    SELECT DISTINCT
      mp.club_id,
      c.name as club_name,
      MIN(mp.due_date) as oldest_due_date
    FROM master_payments mp
    INNER JOIN club_info c ON mp.club_id = c.id
    WHERE mp.status = 'overdue'
    AND mp.due_date < (CURRENT_DATE - grace_period)
    AND c.subscription_status = 'active'
    GROUP BY mp.club_id, c.name
  LOOP
    -- Suspender clube
    UPDATE club_info 
    SET 
      subscription_status = 'suspended',
      payment_status = 'overdue',
      updated_at = NOW()
    WHERE id = payment_record.club_id;
    
    -- Registrar log de auditoria
    INSERT INTO master_audit_logs (
      action,
      entity_type,
      entity_id,
      details,
      created_at
    ) VALUES (
      'club_auto_suspended',
      'club',
      payment_record.club_id,
      jsonb_build_object(
        'reason', 'Pagamento em atraso há mais de ' || grace_period || ' dias',
        'oldest_due_date', payment_record.oldest_due_date,
        'club_name', payment_record.club_name
      ),
      NOW()
    );
    
    suspended_clubs := suspended_clubs + 1;
  END LOOP;
  
  RETURN QUERY SELECT updated_payments, suspended_clubs;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FUNÇÃO: Processar Pagamento
-- =====================================================

CREATE OR REPLACE FUNCTION process_payment(
  payment_id INTEGER,
  payment_method VARCHAR(50) DEFAULT NULL,
  transaction_id VARCHAR(255) DEFAULT NULL
)
RETURNS TABLE(success BOOLEAN, message TEXT) AS $$
DECLARE
  payment_record RECORD;
  club_record RECORD;
BEGIN
  -- Buscar dados do pagamento
  SELECT mp.*, c.name as club_name
  INTO payment_record
  FROM master_payments mp
  INNER JOIN club_info c ON mp.club_id = c.id
  WHERE mp.id = payment_id;
  
  IF NOT FOUND THEN
    RETURN QUERY SELECT FALSE, 'Pagamento não encontrado';
    RETURN;
  END IF;
  
  IF payment_record.status = 'paid' THEN
    RETURN QUERY SELECT FALSE, 'Pagamento já foi processado';
    RETURN;
  END IF;
  
  -- Marcar pagamento como pago
  UPDATE master_payments 
  SET 
    status = 'paid',
    paid_date = NOW(),
    payment_method = process_payment.payment_method,
    transaction_id = process_payment.transaction_id,
    updated_at = NOW()
  WHERE id = payment_id;
  
  -- Atualizar status do clube
  UPDATE club_info 
  SET 
    subscription_status = 'active',
    payment_status = 'current',
    last_payment_date = CURRENT_DATE,
    updated_at = NOW()
  WHERE id = payment_record.club_id;
  
  -- Registrar log de auditoria
  INSERT INTO master_audit_logs (
    action,
    entity_type,
    entity_id,
    details,
    created_at
  ) VALUES (
    'payment_processed',
    'payment',
    payment_id,
    jsonb_build_object(
      'club_id', payment_record.club_id,
      'club_name', payment_record.club_name,
      'amount', payment_record.amount,
      'payment_method', process_payment.payment_method,
      'transaction_id', process_payment.transaction_id
    ),
    NOW()
  );
  
  RETURN QUERY SELECT TRUE, format('Pagamento de %s processado com sucesso', payment_record.club_name);
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FUNÇÃO: Criar Clube com Plano
-- =====================================================

CREATE OR REPLACE FUNCTION create_club_with_plan(
  club_name VARCHAR(255),
  club_email VARCHAR(255),
  club_phone VARCHAR(20) DEFAULT NULL,
  club_document VARCHAR(50) DEFAULT NULL,
  plan_id INTEGER,
  is_trial BOOLEAN DEFAULT FALSE,
  trial_days INTEGER DEFAULT 14,
  created_by_user UUID DEFAULT NULL
)
RETURNS TABLE(club_id INTEGER, success BOOLEAN, message TEXT) AS $$
DECLARE
  new_club_id INTEGER;
  plan_record RECORD;
  trial_end DATE;
BEGIN
  -- Verificar se plano existe
  SELECT * INTO plan_record FROM master_plans WHERE id = plan_id AND is_active = true;
  
  IF NOT FOUND THEN
    RETURN QUERY SELECT NULL::INTEGER, FALSE, 'Plano não encontrado ou inativo';
    RETURN;
  END IF;
  
  -- Verificar se email já existe
  IF EXISTS (SELECT 1 FROM club_info WHERE email = club_email) THEN
    RETURN QUERY SELECT NULL::INTEGER, FALSE, 'Email já cadastrado';
    RETURN;
  END IF;
  
  -- Calcular data de fim do trial se aplicável
  IF is_trial THEN
    trial_end := CURRENT_DATE + (trial_days || ' days')::INTERVAL;
  END IF;
  
  -- Criar clube
  INSERT INTO club_info (
    name,
    email,
    phone,
    document,
    master_plan_id,
    subscription_status,
    subscription_start_date,
    payment_status,
    is_trial,
    trial_start_date,
    trial_end_date,
    billing_email,
    created_by,
    created_at,
    updated_at
  ) VALUES (
    club_name,
    club_email,
    club_phone,
    club_document,
    plan_id,
    CASE WHEN is_trial THEN 'trial' ELSE 'active' END,
    CURRENT_DATE,
    CASE WHEN is_trial THEN 'current' ELSE 'pending' END,
    is_trial,
    CASE WHEN is_trial THEN CURRENT_DATE END,
    trial_end,
    club_email,
    created_by_user,
    NOW(),
    NOW()
  ) RETURNING id INTO new_club_id;
  
  -- Se não é trial, criar primeira cobrança
  IF NOT is_trial THEN
    INSERT INTO master_payments (
      club_id,
      plan_id,
      amount,
      due_date,
      status,
      created_at
    ) VALUES (
      new_club_id,
      plan_id,
      plan_record.price,
      CURRENT_DATE + INTERVAL '5 days', -- 5 dias para pagamento
      'pending',
      NOW()
    );
  END IF;
  
  -- Registrar log de auditoria
  INSERT INTO master_audit_logs (
    user_id,
    action,
    entity_type,
    entity_id,
    details,
    created_at
  ) VALUES (
    created_by_user,
    'club_created',
    'club',
    new_club_id,
    jsonb_build_object(
      'club_name', club_name,
      'plan_id', plan_id,
      'plan_name', plan_record.name,
      'is_trial', is_trial,
      'trial_days', CASE WHEN is_trial THEN trial_days END
    ),
    NOW()
  );
  
  RETURN QUERY SELECT new_club_id, TRUE, format('Clube %s criado com sucesso', club_name);
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FUNÇÃO: Estatísticas do Dashboard Master
-- =====================================================

CREATE OR REPLACE FUNCTION get_master_dashboard_stats()
RETURNS TABLE(
  total_clubs INTEGER,
  active_clubs INTEGER,
  trial_clubs INTEGER,
  suspended_clubs INTEGER,
  total_revenue DECIMAL(10,2),
  monthly_revenue DECIMAL(10,2),
  pending_payments INTEGER,
  overdue_payments INTEGER,
  overdue_amount DECIMAL(10,2)
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    -- Contadores de clubes
    (SELECT COUNT(*)::INTEGER FROM club_info) as total_clubs,
    (SELECT COUNT(*)::INTEGER FROM club_info WHERE subscription_status = 'active') as active_clubs,
    (SELECT COUNT(*)::INTEGER FROM club_info WHERE is_trial = true AND trial_end_date >= CURRENT_DATE) as trial_clubs,
    (SELECT COUNT(*)::INTEGER FROM club_info WHERE subscription_status = 'suspended') as suspended_clubs,
    
    -- Receitas
    (SELECT COALESCE(SUM(amount), 0) FROM master_payments WHERE status = 'paid') as total_revenue,
    (SELECT COALESCE(SUM(amount), 0) FROM master_payments 
     WHERE status = 'paid' 
     AND paid_date >= date_trunc('month', CURRENT_DATE)) as monthly_revenue,
    
    -- Pagamentos
    (SELECT COUNT(*)::INTEGER FROM master_payments WHERE status = 'pending') as pending_payments,
    (SELECT COUNT(*)::INTEGER FROM master_payments WHERE status = 'overdue') as overdue_payments,
    (SELECT COALESCE(SUM(amount), 0) FROM master_payments WHERE status = 'overdue') as overdue_amount;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FUNÇÃO: Limpar Dados Antigos
-- =====================================================

CREATE OR REPLACE FUNCTION cleanup_old_data(days_to_keep INTEGER DEFAULT 365)
RETURNS TABLE(
  audit_logs_deleted INTEGER,
  notification_logs_deleted INTEGER,
  message TEXT
) AS $$
DECLARE
  audit_deleted INTEGER;
  notification_deleted INTEGER;
  cutoff_date TIMESTAMP WITH TIME ZONE;
BEGIN
  cutoff_date := NOW() - (days_to_keep || ' days')::INTERVAL;
  
  -- Limpar logs de auditoria antigos
  DELETE FROM master_audit_logs 
  WHERE created_at < cutoff_date;
  GET DIAGNOSTICS audit_deleted = ROW_COUNT;
  
  -- Limpar logs de notificação antigos
  DELETE FROM master_notification_logs 
  WHERE created_at < cutoff_date;
  GET DIAGNOSTICS notification_deleted = ROW_COUNT;
  
  RETURN QUERY SELECT 
    audit_deleted,
    notification_deleted,
    format('Limpeza concluída: %s logs de auditoria e %s logs de notificação removidos', 
           audit_deleted, notification_deleted);
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FUNÇÃO: Backup de Configurações
-- =====================================================

CREATE OR REPLACE FUNCTION backup_master_settings()
RETURNS TABLE(settings_json JSONB) AS $$
BEGIN
  RETURN QUERY
  SELECT jsonb_object_agg(key, value) as settings_json
  FROM master_settings;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- TRIGGERS AUTOMÁTICOS
-- =====================================================

-- Trigger para atualizar status de pagamentos automaticamente
CREATE OR REPLACE FUNCTION trigger_update_payment_status()
RETURNS TRIGGER AS $$
BEGIN
  -- Se pagamento foi marcado como pago, atualizar clube
  IF NEW.status = 'paid' AND OLD.status != 'paid' THEN
    UPDATE club_info 
    SET 
      payment_status = 'current',
      last_payment_date = CURRENT_DATE,
      updated_at = NOW()
    WHERE id = NEW.club_id;
  END IF;
  
  -- Se pagamento foi marcado como overdue, atualizar clube
  IF NEW.status = 'overdue' AND OLD.status != 'overdue' THEN
    UPDATE club_info 
    SET 
      payment_status = 'overdue',
      updated_at = NOW()
    WHERE id = NEW.club_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Criar trigger
DROP TRIGGER IF EXISTS trigger_payment_status_update ON master_payments;
CREATE TRIGGER trigger_payment_status_update
  AFTER UPDATE ON master_payments
  FOR EACH ROW
  EXECUTE FUNCTION trigger_update_payment_status();

-- =====================================================
-- JOBS AUTOMÁTICOS (Para ser executado via cron)
-- =====================================================

-- Função para ser chamada diariamente
CREATE OR REPLACE FUNCTION daily_maintenance()
RETURNS TEXT AS $$
DECLARE
  result_text TEXT := '';
  overdue_result RECORD;
  payment_result RECORD;
BEGIN
  -- Atualizar pagamentos em atraso
  SELECT * INTO overdue_result FROM update_overdue_payments();
  result_text := result_text || format('Pagamentos atualizados: %s em atraso, %s clubes suspensos. ', 
                                      overdue_result.updated_count, overdue_result.suspended_count);
  
  -- Gerar cobranças mensais (apenas no dia 1 do mês)
  IF EXTRACT(DAY FROM CURRENT_DATE) = 1 THEN
    SELECT * INTO payment_result FROM generate_monthly_payments();
    result_text := result_text || format('Cobranças geradas: %s. ', payment_result.count);
  END IF;
  
  -- Limpar logs antigos (apenas no domingo)
  IF EXTRACT(DOW FROM CURRENT_DATE) = 0 THEN
    PERFORM cleanup_old_data(90); -- manter 90 dias
    result_text := result_text || 'Limpeza de logs executada. ';
  END IF;
  
  RETURN result_text;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- VERIFICAÇÃO FINAL
-- =====================================================

-- Testar algumas funções
DO $$
DECLARE
  stats_result RECORD;
BEGIN
  -- Testar função de estatísticas
  SELECT * INTO stats_result FROM get_master_dashboard_stats();
  RAISE NOTICE 'Estatísticas: % clubes totais, % ativos', stats_result.total_clubs, stats_result.active_clubs;
  
  -- Testar função de backup
  PERFORM backup_master_settings();
  RAISE NOTICE 'Função de backup testada com sucesso';
END $$;

RAISE NOTICE 'Script master-005-create-functions.sql executado com sucesso!';