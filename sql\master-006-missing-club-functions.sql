-- =====================================================
-- SCRIPT: Funções Faltantes para Gerenciamento de Clubes
-- DESCRIÇÃO: Cria funções para suspender e reativar clubes
-- VERSÃO: 1.0
-- DATA: 2025-01-31
-- =====================================================

-- =====================================================
-- FUNÇÃO: Suspender Clube por Falta de Pagamento
-- =====================================================

CREATE OR REPLACE FUNCTION suspend_club_for_non_payment(
  p_club_id INTEGER
)
RETURNS VOID AS $$
DECLARE
  v_club_name VARCHAR(255);
  v_current_user UUID;
BEGIN
  -- Verificar se o clube existe
  SELECT name INTO v_club_name
  FROM club_info
  WHERE id = p_club_id;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Clube com ID % não encontrado', p_club_id;
  END IF;
  
  -- Obter usuário atual
  SELECT auth.uid() INTO v_current_user;
  
  -- Atualizar status do clube
  UPDATE club_info 
  SET 
    subscription_status = 'suspended',
    payment_status = 'overdue',
    notes = COALESCE(notes, '') || ' | Suspenso em ' || NOW()::date || ' por falta de pagamento',
    updated_at = NOW()
  WHERE id = p_club_id;
  
  -- Registrar log de auditoria
  INSERT INTO master_audit_logs (
    user_id,
    action,
    entity_type,
    entity_id,
    details,
    created_at
  ) VALUES (
    v_current_user,
    'suspend_club',
    'club',
    p_club_id,
    jsonb_build_object(
      'club_name', v_club_name,
      'reason', 'Falta de pagamento'
    ),
    NOW()
  );
  
  RAISE NOTICE 'Clube % suspenso com sucesso', v_club_name;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FUNÇÃO: Reativar Clube
-- =====================================================

CREATE OR REPLACE FUNCTION reactivate_club(
  p_club_id INTEGER
)
RETURNS VOID AS $$
DECLARE
  v_club_name VARCHAR(255);
  v_current_user UUID;
  v_subscription_status VARCHAR(20);
BEGIN
  -- Verificar se o clube existe e obter dados
  SELECT name, subscription_status 
  INTO v_club_name, v_subscription_status
  FROM club_info
  WHERE id = p_club_id;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Clube com ID % não encontrado', p_club_id;
  END IF;
  
  -- Verificar se o clube está suspenso
  IF v_subscription_status != 'suspended' THEN
    RAISE EXCEPTION 'Clube % não está suspenso (status atual: %)', v_club_name, v_subscription_status;
  END IF;
  
  -- Obter usuário atual
  SELECT auth.uid() INTO v_current_user;
  
  -- Reativar clube
  UPDATE club_info 
  SET 
    subscription_status = 'active',
    payment_status = 'current',
    notes = COALESCE(notes, '') || ' | Reativado em ' || NOW()::date,
    updated_at = NOW()
  WHERE id = p_club_id;
  
  -- Registrar log de auditoria
  INSERT INTO master_audit_logs (
    user_id,
    action,
    entity_type,
    entity_id,
    details,
    created_at
  ) VALUES (
    v_current_user,
    'reactivate_club',
    'club',
    p_club_id,
    jsonb_build_object(
      'club_name', v_club_name,
      'previous_status', v_subscription_status
    ),
    NOW()
  );
  
  RAISE NOTICE 'Clube % reativado com sucesso', v_club_name;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FUNÇÃO: Suspender Clube com Motivo Personalizado
-- =====================================================

CREATE OR REPLACE FUNCTION suspend_club_with_reason(
  p_club_id INTEGER,
  p_reason TEXT DEFAULT 'Suspensão manual'
)
RETURNS VOID AS $$
DECLARE
  v_club_name VARCHAR(255);
  v_current_user UUID;
BEGIN
  -- Verificar se o clube existe
  SELECT name INTO v_club_name
  FROM club_info
  WHERE id = p_club_id;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Clube com ID % não encontrado', p_club_id;
  END IF;
  
  -- Obter usuário atual
  SELECT auth.uid() INTO v_current_user;
  
  -- Atualizar status do clube
  UPDATE club_info 
  SET 
    subscription_status = 'suspended',
    notes = COALESCE(notes, '') || ' | Suspenso em ' || NOW()::date || ' - ' || p_reason,
    updated_at = NOW()
  WHERE id = p_club_id;
  
  -- Registrar log de auditoria
  INSERT INTO master_audit_logs (
    user_id,
    action,
    entity_type,
    entity_id,
    details,
    created_at
  ) VALUES (
    v_current_user,
    'suspend_club',
    'club',
    p_club_id,
    jsonb_build_object(
      'club_name', v_club_name,
      'reason', p_reason
    ),
    NOW()
  );
  
  RAISE NOTICE 'Clube % suspenso com sucesso. Motivo: %', v_club_name, p_reason;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- VERIFICAÇÃO E TESTE DAS FUNÇÕES
-- =====================================================

-- Verificar se as funções foram criadas
DO $$
DECLARE
  func_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO func_count
  FROM pg_proc p
  JOIN pg_namespace n ON p.pronamespace = n.oid
  WHERE n.nspname = 'public'
  AND p.proname IN ('suspend_club_for_non_payment', 'reactivate_club', 'suspend_club_with_reason');
  
  IF func_count = 3 THEN
    RAISE NOTICE 'Todas as 3 funções foram criadas com sucesso!';
  ELSE
    RAISE NOTICE 'Atenção: apenas % de 3 funções foram criadas', func_count;
  END IF;
END $$;
