-- =====================================================
-- SCRIPT: Add missing columns to master_plans table
-- DESCRIÇÃO: Adiciona colunas is_trial, trial_days e max_storage_gb
-- VERSÃO: 1.0
-- DATA: 2025-01-29
-- =====================================================

-- Add missing columns to master_plans table
ALTER TABLE master_plans 
ADD COLUMN IF NOT EXISTS is_trial BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS trial_days INTEGER DEFAULT 14,
ADD COLUMN IF NOT EXISTS max_storage_gb INTEGER DEFAULT 10;

-- Update existing master_plans records with appropriate default values
UPDATE master_plans 
SET 
    is_trial = false,
    trial_days = 14,
    max_storage_gb = CASE 
        WHEN name = 'Básico' THEN 5
        WHEN name = 'Profissional' THEN 20
        WHEN name = 'Enterprise' THEN NULL -- unlimited
        ELSE 10
    END
WHERE is_trial IS NULL OR trial_days IS NULL OR max_storage_gb IS NULL;

-- Create indexes for new columns for performance
CREATE INDEX IF NOT EXISTS idx_master_plans_is_trial ON master_plans(is_trial);
CREATE INDEX IF NOT EXISTS idx_master_plans_trial_days ON master_plans(trial_days);
CREATE INDEX IF NOT EXISTS idx_master_plans_max_storage_gb ON master_plans(max_storage_gb);

-- Add comments for the new columns
COMMENT ON COLUMN master_plans.is_trial IS 'Indica se o plano é um plano de trial/teste';
COMMENT ON COLUMN master_plans.trial_days IS 'Número de dias do período de trial';
COMMENT ON COLUMN master_plans.max_storage_gb IS 'Limite de armazenamento em GB (NULL = ilimitado)';

-- Verification query
DO $$
DECLARE
    column_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO column_count
    FROM information_schema.columns 
    WHERE table_name = 'master_plans' 
    AND table_schema = 'public'
    AND column_name IN ('is_trial', 'trial_days', 'max_storage_gb');
    
    IF column_count = 3 THEN
        RAISE NOTICE 'Todas as 3 colunas foram adicionadas com sucesso à tabela master_plans';
    ELSE
        RAISE EXCEPTION 'Erro: Apenas % de 3 colunas foram adicionadas', column_count;
    END IF;
END $$;

RAISE NOTICE 'Script master-account-fixes-001-add-missing-columns.sql executado com sucesso!';