-- =====================================================
-- SCRIPT: Fix get_master_dashboard_stats function
-- DESCRIÇÃO: Atualiza função para incluir new_clubs_this_month e melhor tratamento de erros
-- VERSÃO: 1.0
-- DATA: 2025-01-29
-- =====================================================

-- Drop and recreate the function with improved structure
CREATE OR REPLACE FUNCTION get_master_dashboard_stats()
RETURNS TABLE(
  total_clubs INTEGER,
  active_clubs INTEGER,
  trial_clubs INTEGER,
  suspended_clubs INTEGER,
  monthly_revenue DECIMAL(10,2),
  overdue_payments INTEGER,
  new_clubs_this_month INTEGER
) AS $$
DECLARE
  current_month_start DATE;
BEGIN
  -- Calculate start of current month
  current_month_start := date_trunc('month', CURRENT_DATE)::DATE;
  
  -- Return statistics with proper error handling
  RETURN QUERY
  SELECT 
    -- Total clubs count (handle empty table)
    COALESCE((SELECT COUNT(*)::INTEGER FROM club_info), 0) as total_clubs,
    
    -- Active clubs count
    COALESCE((SELECT COUNT(*)::INTEGER FROM club_info WHERE subscription_status = 'active'), 0) as active_clubs,
    
    -- Trial clubs count (active trials only)
    COALESCE((SELECT COUNT(*)::INTEGER FROM club_info 
              WHERE is_trial = true 
              AND trial_end_date >= CURRENT_DATE 
              AND subscription_status IN ('trial', 'active')), 0) as trial_clubs,
    
    -- Suspended clubs count
    COALESCE((SELECT COUNT(*)::INTEGER FROM club_info WHERE subscription_status = 'suspended'), 0) as suspended_clubs,
    
    -- Monthly revenue (current month paid payments)
    COALESCE((SELECT SUM(amount) FROM master_payments 
              WHERE status = 'paid' 
              AND paid_date >= current_month_start
              AND paid_date < (current_month_start + INTERVAL '1 month')), 0.00) as monthly_revenue,
    
    -- Overdue payments count
    COALESCE((SELECT COUNT(*)::INTEGER FROM master_payments WHERE status = 'overdue'), 0) as overdue_payments,
    
    -- New clubs this month
    COALESCE((SELECT COUNT(*)::INTEGER FROM club_info 
              WHERE created_at >= current_month_start
              AND created_at < (current_month_start + INTERVAL '1 month')), 0) as new_clubs_this_month;

EXCEPTION
  WHEN OTHERS THEN
    -- Return zeros if any error occurs to prevent function failure
    RETURN QUERY SELECT 0, 0, 0, 0, 0.00::DECIMAL(10,2), 0, 0;
END;
$$ LANGUAGE plpgsql;

-- Add comment to the function
COMMENT ON FUNCTION get_master_dashboard_stats() IS 'Retorna estatísticas do dashboard master com tratamento de erros e novos clubes do mês';

-- Test the function to ensure it works
DO $$
DECLARE
  test_result RECORD;
BEGIN
  -- Test function execution
  SELECT * INTO test_result FROM get_master_dashboard_stats();
  
  RAISE NOTICE 'Função testada com sucesso:';
  RAISE NOTICE '  Total de clubes: %', test_result.total_clubs;
  RAISE NOTICE '  Clubes ativos: %', test_result.active_clubs;
  RAISE NOTICE '  Clubes em trial: %', test_result.trial_clubs;
  RAISE NOTICE '  Clubes suspensos: %', test_result.suspended_clubs;
  RAISE NOTICE '  Receita mensal: %', test_result.monthly_revenue;
  RAISE NOTICE '  Pagamentos em atraso: %', test_result.overdue_payments;
  RAISE NOTICE '  Novos clubes este mês: %', test_result.new_clubs_this_month;
  
EXCEPTION WHEN OTHERS THEN
  RAISE EXCEPTION 'Erro ao testar função get_master_dashboard_stats: %', SQLERRM;
END $$;

RAISE NOTICE 'Script master-account-fixes-002-fix-dashboard-stats-function.sql executado com sucesso!';