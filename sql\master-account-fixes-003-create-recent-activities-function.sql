-- =====================================================
-- SCRIPT: Create get_master_recent_activities function
-- DESCRIÇÃO: Cria função para buscar atividades recentes com joins corretos
-- VERSÃO: 1.0
-- DATA: 2025-01-29
-- =====================================================

-- Create function to fetch recent activities from master_audit_logs with proper user joins
CREATE OR REPLACE FUNCTION get_master_recent_activities(
  limit_count INTEGER DEFAULT 20,
  offset_count INTEGER DEFAULT 0
)
RETURNS TABLE(
  id INTEGER,
  action VARCHAR(100),
  entity_type VARCHAR(50),
  entity_id INTEGER,
  user_name VARCHAR(100),
  user_email VARCHAR(255),
  created_at TIMESTAMP WITH TIME ZONE,
  details JSONB,
  description TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    mal.id,
    mal.action,
    mal.entity_type,
    mal.entity_id,
    -- Handle missing user relationships gracefully
    COALESCE(
      CASE 
        WHEN au.raw_user_meta_data->>'name' IS NOT NULL 
        THEN au.raw_user_meta_data->>'name'
        WHEN au.raw_user_meta_data->>'full_name' IS NOT NULL 
        THEN au.raw_user_meta_data->>'full_name'
        ELSE SPLIT_PART(au.email, '@', 1)
      END,
      'Sistema'
    ) as user_name,
    COALESCE(au.email, '<EMAIL>') as user_email,
    mal.created_at,
    COALESCE(mal.details, '{}'::jsonb) as details,
    -- Generate user-friendly description
    CASE 
      WHEN mal.action = 'club_created' THEN 
        'Criou o clube ' || COALESCE(mal.details->>'club_name', 'N/A')
      WHEN mal.action = 'club_suspended' THEN 
        'Suspendeu o clube ' || COALESCE(mal.details->>'club_name', 'N/A')
      WHEN mal.action = 'club_auto_suspended' THEN 
        'Sistema suspendeu automaticamente o clube por pagamento em atraso'
      WHEN mal.action = 'payment_processed' THEN 
        'Processou pagamento de R$ ' || COALESCE(mal.details->>'amount', '0') || ' do clube ' || COALESCE(mal.details->>'club_name', 'N/A')
      WHEN mal.action = 'plan_updated' THEN 
        'Atualizou plano do clube'
      WHEN mal.action = 'user_created' THEN 
        'Criou usuário master'
      WHEN mal.action = 'settings_updated' THEN 
        'Atualizou configurações do sistema'
      ELSE 
        mal.action
    END as description
  FROM master_audit_logs mal
  LEFT JOIN auth.users au ON mal.user_id = au.id
  ORDER BY mal.created_at DESC
  LIMIT limit_count
  OFFSET offset_count;

EXCEPTION
  WHEN OTHERS THEN
    -- Return empty result set if any error occurs
    RETURN;
END;
$$ LANGUAGE plpgsql;

-- Add comment to the function
COMMENT ON FUNCTION get_master_recent_activities(INTEGER, INTEGER) IS 'Retorna atividades recentes do sistema master com joins seguros para usuários';

-- Create a simpler version without parameters for easier calling
CREATE OR REPLACE FUNCTION get_master_recent_activities()
RETURNS TABLE(
  id INTEGER,
  action VARCHAR(100),
  entity_type VARCHAR(50),
  entity_id INTEGER,
  user_name VARCHAR(100),
  user_email VARCHAR(255),
  created_at TIMESTAMP WITH TIME ZONE,
  details JSONB,
  description TEXT
) AS $$
BEGIN
  RETURN QUERY SELECT * FROM get_master_recent_activities(20, 0);
END;
$$ LANGUAGE plpgsql;

-- Test the function with various data scenarios
DO $$
DECLARE
  activity_record RECORD;
  activity_count INTEGER := 0;
BEGIN
  -- Test function execution
  FOR activity_record IN SELECT * FROM get_master_recent_activities(5, 0)
  LOOP
    activity_count := activity_count + 1;
    RAISE NOTICE 'Atividade %: % - % (%)', 
      activity_count, 
      activity_record.user_name, 
      activity_record.description,
      activity_record.created_at;
  END LOOP;
  
  IF activity_count = 0 THEN
    RAISE NOTICE 'Nenhuma atividade encontrada (normal se não há dados de auditoria)';
  ELSE
    RAISE NOTICE 'Função testada com sucesso: % atividades retornadas', activity_count;
  END IF;
  
EXCEPTION WHEN OTHERS THEN
  RAISE EXCEPTION 'Erro ao testar função get_master_recent_activities: %', SQLERRM;
END $$;

-- Create some sample audit log entries for testing (only if table is empty)
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM master_audit_logs LIMIT 1) THEN
    INSERT INTO master_audit_logs (action, entity_type, entity_id, details, created_at) VALUES
    ('system_started', 'system', 1, '{"version": "1.0", "environment": "production"}', NOW() - INTERVAL '1 hour'),
    ('settings_updated', 'settings', 1, '{"key": "maintenance_mode", "old_value": "true", "new_value": "false"}', NOW() - INTERVAL '30 minutes'),
    ('cleanup_executed', 'system', 1, '{"logs_deleted": 0, "type": "automatic"}', NOW() - INTERVAL '10 minutes');
    
    RAISE NOTICE 'Dados de exemplo inseridos na tabela master_audit_logs para teste';
  END IF;
END $$;

RAISE NOTICE 'Script master-account-fixes-003-create-recent-activities-function.sql executado com sucesso!';