-- =====================================================
-- SCRIPT: Fix master_audit_logs user relationship
-- DESCRIÇÃO: Verifica e otimiza relacionamento com auth.users
-- VERSÃO: 1.0
-- DATA: 2025-01-29
-- =====================================================

-- Verify current foreign key constraint exists
DO $$
DECLARE
  constraint_exists BOOLEAN;
  constraint_name TEXT;
BEGIN
  -- Check if foreign key constraint exists
  SELECT EXISTS (
    SELECT 1 
    FROM information_schema.table_constraints tc
    JOIN information_schema.key_column_usage kcu ON tc.constraint_name = kcu.constraint_name
    WHERE tc.table_name = 'master_audit_logs'
    AND tc.constraint_type = 'FOREIGN KEY'
    AND kcu.column_name = 'user_id'
    AND kcu.referenced_table_name = 'users'
  ) INTO constraint_exists;
  
  IF constraint_exists THEN
    RAISE NOTICE 'Foreign key constraint já existe para master_audit_logs.user_id';
  ELSE
    RAISE NOTICE 'Foreign key constraint não encontrada, será criada';
    
    -- Add foreign key constraint if it doesn't exist
    ALTER TABLE master_audit_logs 
    ADD CONSTRAINT fk_master_audit_logs_user_id 
    FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE SET NULL;
    
    RAISE NOTICE 'Foreign key constraint criada com sucesso';
  END IF;
END $$;

-- Create indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_master_audit_logs_user_id_not_null 
ON master_audit_logs(user_id) WHERE user_id IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_master_audit_logs_created_at_desc 
ON master_audit_logs(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_master_audit_logs_action_created_at 
ON master_audit_logs(action, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_master_audit_logs_entity_type_id 
ON master_audit_logs(entity_type, entity_id);

-- Verify data integrity - check for orphaned records
DO $$
DECLARE
  orphaned_count INTEGER;
  total_count INTEGER;
BEGIN
  -- Count total audit logs
  SELECT COUNT(*) INTO total_count FROM master_audit_logs;
  
  -- Count orphaned records (user_id not null but user doesn't exist)
  SELECT COUNT(*) INTO orphaned_count
  FROM master_audit_logs mal
  WHERE mal.user_id IS NOT NULL
  AND NOT EXISTS (SELECT 1 FROM auth.users au WHERE au.id = mal.user_id);
  
  RAISE NOTICE 'Total de registros em master_audit_logs: %', total_count;
  RAISE NOTICE 'Registros órfãos (user_id inválido): %', orphaned_count;
  
  -- Clean up orphaned records by setting user_id to NULL
  IF orphaned_count > 0 THEN
    UPDATE master_audit_logs 
    SET user_id = NULL 
    WHERE user_id IS NOT NULL
    AND NOT EXISTS (SELECT 1 FROM auth.users au WHERE au.id = user_id);
    
    RAISE NOTICE 'Registros órfãos corrigidos (user_id definido como NULL)';
  END IF;
END $$;

-- Create a helper function to safely insert audit logs
CREATE OR REPLACE FUNCTION insert_master_audit_log(
  p_user_id UUID DEFAULT NULL,
  p_action VARCHAR(100),
  p_entity_type VARCHAR(50),
  p_entity_id INTEGER DEFAULT NULL,
  p_old_values JSONB DEFAULT NULL,
  p_new_values JSONB DEFAULT NULL,
  p_details JSONB DEFAULT '{}'::jsonb,
  p_ip_address INET DEFAULT NULL,
  p_user_agent TEXT DEFAULT NULL
)
RETURNS INTEGER AS $$
DECLARE
  log_id INTEGER;
  safe_user_id UUID;
BEGIN
  -- Verify user exists if user_id is provided
  IF p_user_id IS NOT NULL THEN
    SELECT id INTO safe_user_id 
    FROM auth.users 
    WHERE id = p_user_id;
    
    -- If user doesn't exist, set to NULL
    IF safe_user_id IS NULL THEN
      safe_user_id := NULL;
    ELSE
      safe_user_id := p_user_id;
    END IF;
  END IF;
  
  -- Insert audit log
  INSERT INTO master_audit_logs (
    user_id,
    action,
    entity_type,
    entity_id,
    old_values,
    new_values,
    details,
    ip_address,
    user_agent,
    created_at
  ) VALUES (
    safe_user_id,
    p_action,
    p_entity_type,
    p_entity_id,
    p_old_values,
    p_new_values,
    p_details,
    p_ip_address,
    p_user_agent,
    NOW()
  ) RETURNING id INTO log_id;
  
  RETURN log_id;
END;
$$ LANGUAGE plpgsql;

-- Add comment to the helper function
COMMENT ON FUNCTION insert_master_audit_log IS 'Insere log de auditoria com verificação segura de user_id';

-- Test the relationship and indexes
DO $$
DECLARE
  test_log_id INTEGER;
  test_activities RECORD;
BEGIN
  -- Test inserting a log entry
  SELECT insert_master_audit_log(
    NULL, -- no user
    'relationship_test',
    'system',
    1,
    NULL,
    NULL,
    '{"test": "relationship verification"}'::jsonb
  ) INTO test_log_id;
  
  RAISE NOTICE 'Log de teste inserido com ID: %', test_log_id;
  
  -- Test querying with the relationship
  SELECT * INTO test_activities 
  FROM get_master_recent_activities(1, 0);
  
  RAISE NOTICE 'Query de atividades recentes executada com sucesso';
  
  -- Clean up test log
  DELETE FROM master_audit_logs WHERE id = test_log_id;
  RAISE NOTICE 'Log de teste removido';
  
EXCEPTION WHEN OTHERS THEN
  RAISE EXCEPTION 'Erro ao testar relacionamento: %', SQLERRM;
END $$;

-- Verify all indexes were created
DO $$
DECLARE
  index_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO index_count
  FROM pg_indexes 
  WHERE tablename = 'master_audit_logs'
  AND indexname LIKE 'idx_master_audit_logs_%';
  
  RAISE NOTICE 'Índices criados para master_audit_logs: %', index_count;
END $$;

RAISE NOTICE 'Script master-account-fixes-004-fix-audit-logs-relationship.sql executado com sucesso!';