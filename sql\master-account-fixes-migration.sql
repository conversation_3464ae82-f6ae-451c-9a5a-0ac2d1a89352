-- =====================================================
-- COMPREHENSIVE MASTER ACCOUNT FIXES MIGRATION SCRIPT
-- DESCRIÇÃO: Migração completa para corrigir problemas no sistema master
-- VERSÃO: 1.0
-- DATA: 2025-01-29
-- AUTOR: <PERSON>ro AI Assistant
-- =====================================================

-- =====================================================
-- COMPREHENSIVE MASTER ACCOUNT FIXES MIGRATION SCRIPT
-- NOTA: Execute este script diretamente no SQL Editor do Supabase
-- =====================================================

-- Start transaction for atomic execution
BEGIN;

-- Create a temporary table to track migration progress
CREATE TEMP TABLE migration_progress (
    step_number INTEGER PRIMARY KEY,
    step_name TEXT NOT NULL,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'failed')),
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT
);

-- Insert all migration steps
INSERT INTO migration_progress (step_number, step_name) VALUES
(1, 'Add missing columns to master_plans'),
(2, 'Update existing master_plans records'),
(3, 'Create indexes for new columns'),
(4, 'Fix get_master_dashboard_stats function'),
(5, 'Create get_master_recent_activities function'),
(6, 'Fix master_audit_logs relationships'),
(7, 'Create helper functions'),
(8, 'Verify data integrity'),
(9, 'Run comprehensive tests');

-- Function to update migration progress
CREATE OR REPLACE FUNCTION update_migration_step(
    p_step_number INTEGER,
    p_status TEXT,
    p_error_message TEXT DEFAULT NULL
) RETURNS VOID AS $func$
BEGIN
    UPDATE migration_progress 
    SET 
        status = p_status,
        started_at = CASE WHEN p_status = 'running' THEN NOW() ELSE started_at END,
        completed_at = CASE WHEN p_status IN ('completed', 'failed') THEN NOW() ELSE completed_at END,
        error_message = p_error_message
    WHERE step_number = p_step_number;
    
    RAISE NOTICE '[STEP %] %: %', p_step_number, 
        (SELECT step_name FROM migration_progress WHERE step_number = p_step_number), 
        p_status;
END;
$func$ LANGUAGE plpgsql;

-- =====================================================
-- STEP 1: ADD MISSING COLUMNS TO MASTER_PLANS
-- =====================================================

DO $step1$
BEGIN
    PERFORM update_migration_step(1, 'running');
    
    -- Check if columns already exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'master_plans' AND column_name = 'is_trial'
    ) THEN
        ALTER TABLE master_plans ADD COLUMN is_trial BOOLEAN DEFAULT false;
        RAISE NOTICE 'Added column is_trial to master_plans';
    ELSE
        RAISE NOTICE 'Column is_trial already exists in master_plans';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'master_plans' AND column_name = 'trial_days'
    ) THEN
        ALTER TABLE master_plans ADD COLUMN trial_days INTEGER DEFAULT 14;
        RAISE NOTICE 'Added column trial_days to master_plans';
    ELSE
        RAISE NOTICE 'Column trial_days already exists in master_plans';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'master_plans' AND column_name = 'max_storage_gb'
    ) THEN
        ALTER TABLE master_plans ADD COLUMN max_storage_gb INTEGER DEFAULT 10;
        RAISE NOTICE 'Added column max_storage_gb to master_plans';
    ELSE
        RAISE NOTICE 'Column max_storage_gb already exists in master_plans';
    END IF;
    
    PERFORM update_migration_step(1, 'completed');
    
EXCEPTION WHEN OTHERS THEN
    PERFORM update_migration_step(1, 'failed', SQLERRM);
    RAISE;
END;
$step1$;

-- =====================================================
-- STEP 2: UPDATE EXISTING MASTER_PLANS RECORDS
-- =====================================================

DO $step2$
DECLARE
    updated_count INTEGER;
BEGIN
    PERFORM update_migration_step(2, 'running');
    
    -- Update existing records with appropriate default values
    UPDATE master_plans 
    SET 
        is_trial = COALESCE(is_trial, false),
        trial_days = COALESCE(trial_days, 14),
        max_storage_gb = COALESCE(max_storage_gb, 
            CASE 
                WHEN name ILIKE '%básico%' OR name ILIKE '%basic%' THEN 5
                WHEN name ILIKE '%profissional%' OR name ILIKE '%professional%' THEN 20
                WHEN name ILIKE '%enterprise%' OR name ILIKE '%premium%' THEN NULL -- unlimited
                ELSE 10
            END
        )
    WHERE is_trial IS NULL OR trial_days IS NULL OR max_storage_gb IS NULL;
    
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RAISE NOTICE 'Updated % master_plans records with default values', updated_count;
    
    PERFORM update_migration_step(2, 'completed');
    
EXCEPTION WHEN OTHERS THEN
    PERFORM update_migration_step(2, 'failed', SQLERRM);
    RAISE;
END;
$step2$;

-- =====================================================
-- STEP 3: CREATE INDEXES FOR NEW COLUMNS
-- =====================================================

DO $step3$
BEGIN
    PERFORM update_migration_step(3, 'running');
    
    -- Create indexes for performance
    CREATE INDEX IF NOT EXISTS idx_master_plans_is_trial ON master_plans(is_trial);
    CREATE INDEX IF NOT EXISTS idx_master_plans_trial_days ON master_plans(trial_days);
    CREATE INDEX IF NOT EXISTS idx_master_plans_max_storage_gb ON master_plans(max_storage_gb);
    
    -- Add column comments
    COMMENT ON COLUMN master_plans.is_trial IS 'Indica se o plano é um plano de trial/teste';
    COMMENT ON COLUMN master_plans.trial_days IS 'Número de dias do período de trial';
    COMMENT ON COLUMN master_plans.max_storage_gb IS 'Limite de armazenamento em GB (NULL = ilimitado)';
    
    RAISE NOTICE 'Created indexes and comments for new master_plans columns';
    
    PERFORM update_migration_step(3, 'completed');
    
EXCEPTION WHEN OTHERS THEN
    PERFORM update_migration_step(3, 'failed', SQLERRM);
    RAISE;
END;
$step3$;

-- =====================================================
-- STEP 4: FIX GET_MASTER_DASHBOARD_STATS FUNCTION
-- =====================================================

DO $step4$
BEGIN
    PERFORM update_migration_step(4, 'running');
    
    -- Drop and recreate the function with improved structure
    CREATE OR REPLACE FUNCTION get_master_dashboard_stats()
    RETURNS TABLE(
      total_clubs INTEGER,
      active_clubs INTEGER,
      trial_clubs INTEGER,
      suspended_clubs INTEGER,
      monthly_revenue DECIMAL(10,2),
      overdue_payments INTEGER,
      new_clubs_this_month INTEGER
    ) AS $func$
    DECLARE
      current_month_start DATE;
    BEGIN
      -- Calculate start of current month
      current_month_start := date_trunc('month', CURRENT_DATE)::DATE;
      
      -- Return statistics with proper error handling
      RETURN QUERY
      SELECT 
        -- Total clubs count (handle empty table)
        COALESCE((SELECT COUNT(*)::INTEGER FROM club_info), 0) as total_clubs,
        
        -- Active clubs count
        COALESCE((SELECT COUNT(*)::INTEGER FROM club_info WHERE subscription_status = 'active'), 0) as active_clubs,
        
        -- Trial clubs count (active trials only)
        COALESCE((SELECT COUNT(*)::INTEGER FROM club_info 
                  WHERE is_trial = true 
                  AND trial_end_date >= CURRENT_DATE 
                  AND subscription_status IN ('trial', 'active')), 0) as trial_clubs,
        
        -- Suspended clubs count
        COALESCE((SELECT COUNT(*)::INTEGER FROM club_info WHERE subscription_status = 'suspended'), 0) as suspended_clubs,
        
        -- Monthly revenue (current month paid payments)
        COALESCE((SELECT SUM(amount) FROM master_payments 
                  WHERE status = 'paid' 
                  AND paid_date >= current_month_start
                  AND paid_date < (current_month_start + INTERVAL '1 month')), 0.00) as monthly_revenue,
        
        -- Overdue payments count
        COALESCE((SELECT COUNT(*)::INTEGER FROM master_payments WHERE status = 'overdue'), 0) as overdue_payments,
        
        -- New clubs this month
        COALESCE((SELECT COUNT(*)::INTEGER FROM club_info 
                  WHERE created_at >= current_month_start
                  AND created_at < (current_month_start + INTERVAL '1 month')), 0) as new_clubs_this_month;

    EXCEPTION
      WHEN OTHERS THEN
        -- Return zeros if any error occurs to prevent function failure
        RETURN QUERY SELECT 0, 0, 0, 0, 0.00::DECIMAL(10,2), 0, 0;
    END;
    $func$ LANGUAGE plpgsql;
    
    -- Add comment to the function
    COMMENT ON FUNCTION get_master_dashboard_stats() IS 'Retorna estatísticas do dashboard master com tratamento de erros e novos clubes do mês';
    
    RAISE NOTICE 'Fixed get_master_dashboard_stats function';
    
    PERFORM update_migration_step(4, 'completed');
    
EXCEPTION WHEN OTHERS THEN
    PERFORM update_migration_step(4, 'failed', SQLERRM);
    RAISE;
END;
$step4$;

-- =====================================================
-- STEP 5: CREATE GET_MASTER_RECENT_ACTIVITIES FUNCTION
-- =====================================================

DO $step5$
BEGIN
    PERFORM update_migration_step(5, 'running');
    
    -- Create function to fetch recent activities with proper user joins
    CREATE OR REPLACE FUNCTION get_master_recent_activities(
      limit_count INTEGER DEFAULT 20,
      offset_count INTEGER DEFAULT 0
    )
    RETURNS TABLE(
      id INTEGER,
      action VARCHAR(100),
      entity_type VARCHAR(50),
      entity_id INTEGER,
      user_name VARCHAR(100),
      user_email VARCHAR(255),
      created_at TIMESTAMP WITH TIME ZONE,
      details JSONB,
      description TEXT
    ) AS $func$
    BEGIN
      RETURN QUERY
      SELECT 
        mal.id,
        mal.action,
        mal.entity_type,
        mal.entity_id,
        -- Handle missing user relationships gracefully
        COALESCE(
          CASE 
            WHEN au.raw_user_meta_data->>'name' IS NOT NULL 
            THEN au.raw_user_meta_data->>'name'
            WHEN au.raw_user_meta_data->>'full_name' IS NOT NULL 
            THEN au.raw_user_meta_data->>'full_name'
            ELSE SPLIT_PART(au.email, '@', 1)
          END,
          'Sistema'
        ) as user_name,
        COALESCE(au.email, '<EMAIL>') as user_email,
        mal.created_at,
        COALESCE(mal.details, '{}'::jsonb) as details,
        -- Generate user-friendly description
        CASE 
          WHEN mal.action = 'club_created' THEN 
            'Criou o clube ' || COALESCE(mal.details->>'club_name', 'N/A')
          WHEN mal.action = 'club_suspended' THEN 
            'Suspendeu o clube ' || COALESCE(mal.details->>'club_name', 'N/A')
          WHEN mal.action = 'club_auto_suspended' THEN 
            'Sistema suspendeu automaticamente o clube por pagamento em atraso'
          WHEN mal.action = 'payment_processed' THEN 
            'Processou pagamento de R$ ' || COALESCE(mal.details->>'amount', '0') || ' do clube ' || COALESCE(mal.details->>'club_name', 'N/A')
          WHEN mal.action = 'plan_updated' THEN 
            'Atualizou plano do clube'
          WHEN mal.action = 'user_created' THEN 
            'Criou usuário master'
          WHEN mal.action = 'settings_updated' THEN 
            'Atualizou configurações do sistema'
          ELSE 
            mal.action
        END as description
      FROM master_audit_logs mal
      LEFT JOIN auth.users au ON mal.user_id = au.id
      ORDER BY mal.created_at DESC
      LIMIT limit_count
      OFFSET offset_count;

    EXCEPTION
      WHEN OTHERS THEN
        -- Return empty result set if any error occurs
        RETURN;
    END;
    $func$ LANGUAGE plpgsql;
    
    -- Create a simpler version without parameters for easier calling
    CREATE OR REPLACE FUNCTION get_master_recent_activities()
    RETURNS TABLE(
      id INTEGER,
      action VARCHAR(100),
      entity_type VARCHAR(50),
      entity_id INTEGER,
      user_name VARCHAR(100),
      user_email VARCHAR(255),
      created_at TIMESTAMP WITH TIME ZONE,
      details JSONB,
      description TEXT
    ) AS $func$
    BEGIN
      RETURN QUERY SELECT * FROM get_master_recent_activities(20, 0);
    END;
    $func$ LANGUAGE plpgsql;
    
    -- Add comment to the function
    COMMENT ON FUNCTION get_master_recent_activities(INTEGER, INTEGER) IS 'Retorna atividades recentes do sistema master com joins seguros para usuários';
    
    RAISE NOTICE 'Created get_master_recent_activities functions';
    
    PERFORM update_migration_step(5, 'completed');
    
EXCEPTION WHEN OTHERS THEN
    PERFORM update_migration_step(5, 'failed', SQLERRM);
    RAISE;
END;
$step5$;

-- =====================================================
-- STEP 6: FIX MASTER_AUDIT_LOGS RELATIONSHIPS
-- =====================================================

DO $step6$
DECLARE
    constraint_exists BOOLEAN;
    orphaned_count INTEGER;
BEGIN
    PERFORM update_migration_step(6, 'running');
    
    -- Check if foreign key constraint exists
    SELECT EXISTS (
        SELECT 1 
        FROM information_schema.table_constraints tc
        JOIN information_schema.key_column_usage kcu ON tc.constraint_name = kcu.constraint_name
        WHERE tc.table_name = 'master_audit_logs'
        AND tc.constraint_type = 'FOREIGN KEY'
        AND kcu.column_name = 'user_id'
        AND tc.constraint_name = 'fk_master_audit_logs_user_id'
    ) INTO constraint_exists;
    
    IF NOT constraint_exists THEN
        -- Add foreign key constraint if it doesn't exist
        ALTER TABLE master_audit_logs 
        ADD CONSTRAINT fk_master_audit_logs_user_id 
        FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE SET NULL;
        
        RAISE NOTICE 'Created foreign key constraint for master_audit_logs.user_id';
    ELSE
        RAISE NOTICE 'Foreign key constraint already exists for master_audit_logs.user_id';
    END IF;
    
    -- Create performance indexes
    CREATE INDEX IF NOT EXISTS idx_master_audit_logs_user_id_not_null 
    ON master_audit_logs(user_id) WHERE user_id IS NOT NULL;
    
    CREATE INDEX IF NOT EXISTS idx_master_audit_logs_created_at_desc 
    ON master_audit_logs(created_at DESC);
    
    CREATE INDEX IF NOT EXISTS idx_master_audit_logs_action_created_at 
    ON master_audit_logs(action, created_at DESC);
    
    CREATE INDEX IF NOT EXISTS idx_master_audit_logs_entity_type_id 
    ON master_audit_logs(entity_type, entity_id);
    
    -- Clean up orphaned records
    SELECT COUNT(*) INTO orphaned_count
    FROM master_audit_logs mal
    WHERE mal.user_id IS NOT NULL
    AND NOT EXISTS (SELECT 1 FROM auth.users au WHERE au.id = mal.user_id);
    
    IF orphaned_count > 0 THEN
        UPDATE master_audit_logs 
        SET user_id = NULL 
        WHERE user_id IS NOT NULL
        AND NOT EXISTS (SELECT 1 FROM auth.users au WHERE au.id = user_id);
        
        RAISE NOTICE 'Cleaned up % orphaned audit log records', orphaned_count;
    ELSE
        RAISE NOTICE 'No orphaned audit log records found';
    END IF;
    
    PERFORM update_migration_step(6, 'completed');
    
EXCEPTION WHEN OTHERS THEN
    PERFORM update_migration_step(6, 'failed', SQLERRM);
    RAISE;
END;
$step6$;

-- =====================================================
-- STEP 7: CREATE HELPER FUNCTIONS
-- =====================================================

DO $step7$
BEGIN
    PERFORM update_migration_step(7, 'running');
    
    -- Create a helper function to safely insert audit logs
    CREATE OR REPLACE FUNCTION insert_master_audit_log(
      p_action VARCHAR(100),
      p_entity_type VARCHAR(50),
      p_user_id UUID DEFAULT NULL,
      p_entity_id INTEGER DEFAULT NULL,
      p_old_values JSONB DEFAULT NULL,
      p_new_values JSONB DEFAULT NULL,
      p_details JSONB DEFAULT '{}'::jsonb,
      p_ip_address INET DEFAULT NULL,
      p_user_agent TEXT DEFAULT NULL
    )
    RETURNS INTEGER AS $func$
    DECLARE
      log_id INTEGER;
      safe_user_id UUID;
    BEGIN
      -- Verify user exists if user_id is provided
      IF p_user_id IS NOT NULL THEN
        SELECT id INTO safe_user_id 
        FROM auth.users 
        WHERE id = p_user_id;
        
        -- If user doesn't exist, set to NULL
        IF safe_user_id IS NULL THEN
          safe_user_id := NULL;
        ELSE
          safe_user_id := p_user_id;
        END IF;
      END IF;
      
      -- Insert audit log
      INSERT INTO master_audit_logs (
        user_id,
        action,
        entity_type,
        entity_id,
        old_values,
        new_values,
        details,
        ip_address,
        user_agent,
        created_at
      ) VALUES (
        safe_user_id,
        p_action,
        p_entity_type,
        p_entity_id,
        p_old_values,
        p_new_values,
        p_details,
        p_ip_address,
        p_user_agent,
        NOW()
      ) RETURNING id INTO log_id;
      
      RETURN log_id;
    END;
    $func$ LANGUAGE plpgsql;
    
    -- Add comment to the helper function
    COMMENT ON FUNCTION insert_master_audit_log IS 'Insere log de auditoria com verificação segura de user_id';
    
    RAISE NOTICE 'Created helper functions';
    
    PERFORM update_migration_step(7, 'completed');
    
EXCEPTION WHEN OTHERS THEN
    PERFORM update_migration_step(7, 'failed', SQLERRM);
    RAISE;
END;
$step7$;

-- =====================================================
-- STEP 8: VERIFY DATA INTEGRITY
-- =====================================================

DO $step8$
DECLARE
    column_count INTEGER;
    function_count INTEGER;
    index_count INTEGER;
BEGIN
    PERFORM update_migration_step(8, 'running');
    
    -- Verify all columns were added
    SELECT COUNT(*) INTO column_count
    FROM information_schema.columns 
    WHERE table_name = 'master_plans' 
    AND table_schema = 'public'
    AND column_name IN ('is_trial', 'trial_days', 'max_storage_gb');
    
    IF column_count != 3 THEN
        RAISE EXCEPTION 'Missing columns in master_plans table. Expected 3, found %', column_count;
    END IF;
    
    -- Verify functions exist
    SELECT COUNT(*) INTO function_count
    FROM information_schema.routines
    WHERE routine_schema = 'public'
    AND routine_name IN ('get_master_dashboard_stats', 'get_master_recent_activities', 'insert_master_audit_log');
    
    IF function_count < 3 THEN
        RAISE EXCEPTION 'Missing functions. Expected at least 3, found %', function_count;
    END IF;
    
    -- Verify indexes were created
    SELECT COUNT(*) INTO index_count
    FROM pg_indexes 
    WHERE tablename = 'master_audit_logs'
    AND indexname LIKE 'idx_master_audit_logs_%';
    
    IF index_count < 4 THEN
        RAISE EXCEPTION 'Missing indexes on master_audit_logs. Expected at least 4, found %', index_count;
    END IF;
    
    RAISE NOTICE 'Data integrity verification passed';
    RAISE NOTICE '  - Columns added: %', column_count;
    RAISE NOTICE '  - Functions created: %', function_count;
    RAISE NOTICE '  - Indexes created: %', index_count;
    
    PERFORM update_migration_step(8, 'completed');
    
EXCEPTION WHEN OTHERS THEN
    PERFORM update_migration_step(8, 'failed', SQLERRM);
    RAISE;
END;
$step8$;

-- =====================================================
-- STEP 9: RUN COMPREHENSIVE TESTS
-- =====================================================

DO $step9$
DECLARE
    test_log_id INTEGER;
    test_stats RECORD;
    test_activities RECORD;
    activity_count INTEGER := 0;
BEGIN
    PERFORM update_migration_step(9, 'running');
    
    -- Test 1: Insert audit log function
    SELECT insert_master_audit_log(
        'migration_test',       -- action
        'system',               -- entity_type
        NULL,                   -- user_id (no user)
        1,
        NULL,
        NULL,
        '{"test": "comprehensive migration test"}'::jsonb
    ) INTO test_log_id;
    
    IF test_log_id IS NULL THEN
        RAISE EXCEPTION 'Failed to insert test audit log';
    END IF;
    
    -- Test 2: Dashboard stats function
    SELECT * INTO test_stats FROM get_master_dashboard_stats();
    
    IF test_stats IS NULL THEN
        RAISE EXCEPTION 'Dashboard stats function returned NULL';
    END IF;
    
    -- Test 3: Recent activities function
    FOR test_activities IN SELECT * FROM get_master_recent_activities(5, 0)
    LOOP
        activity_count := activity_count + 1;
        EXIT WHEN activity_count >= 5; -- Limit test output
    END LOOP;
    
    -- Clean up test data
    DELETE FROM master_audit_logs WHERE id = test_log_id;
    
    RAISE NOTICE 'Comprehensive tests passed';
    RAISE NOTICE '  - Audit log insertion: SUCCESS';
    RAISE NOTICE '  - Dashboard stats: SUCCESS (total_clubs: %)', test_stats.total_clubs;
    RAISE NOTICE '  - Recent activities: SUCCESS (% activities found)', activity_count;
    
    PERFORM update_migration_step(9, 'completed');
    
EXCEPTION WHEN OTHERS THEN
    PERFORM update_migration_step(9, 'failed', SQLERRM);
    RAISE;
END;
$step9$;

-- =====================================================
-- MIGRATION SUMMARY
-- =====================================================

DO $summary$
DECLARE
    completed_steps INTEGER;
    failed_steps INTEGER;
    step_record RECORD;
BEGIN
    -- Count completed and failed steps
    SELECT 
        COUNT(*) FILTER (WHERE status = 'completed'),
        COUNT(*) FILTER (WHERE status = 'failed')
    INTO completed_steps, failed_steps
    FROM migration_progress;
    
    RAISE NOTICE '';
    RAISE NOTICE '========================================';
    RAISE NOTICE 'MASTER ACCOUNT FIXES MIGRATION SUMMARY';
    RAISE NOTICE '========================================';
    RAISE NOTICE 'Completed steps: %', completed_steps;
    RAISE NOTICE 'Failed steps: %', failed_steps;
    RAISE NOTICE '';
    
    -- Show detailed step results
    FOR step_record IN 
        SELECT step_number, step_name, status, 
               EXTRACT(EPOCH FROM (completed_at - started_at)) as duration_seconds,
               error_message
        FROM migration_progress 
        ORDER BY step_number
    LOOP
        RAISE NOTICE '[STEP %] % - % (%.2fs)', 
            step_record.step_number, 
            step_record.step_name, 
            step_record.status,
            COALESCE(step_record.duration_seconds, 0);
            
        IF step_record.error_message IS NOT NULL THEN
            RAISE NOTICE '  ERROR: %', step_record.error_message;
        END IF;
    END LOOP;
    
    IF failed_steps > 0 THEN
        RAISE EXCEPTION 'Migration failed with % failed steps', failed_steps;
    END IF;
    
    RAISE NOTICE '';
    RAISE NOTICE 'Migration completed successfully!';
    RAISE NOTICE 'All master account fixes have been applied.';
END;
$summary$;

-- Log successful migration
SELECT insert_master_audit_log(
    'migration_completed', -- action
    'system',              -- entity_type
    NULL,                  -- user_id
    1,
    NULL,
    NULL,
    '{"migration": "master-account-fixes", "version": "1.0", "steps_completed": 9}'::jsonb
);

-- Commit the transaction
COMMIT;

RAISE NOTICE '';
RAISE NOTICE '✅ Master Account Fixes Migration completed successfully!';
RAISE NOTICE 'Transaction committed. All changes are now permanent.';