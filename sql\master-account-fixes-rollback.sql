-- =====================================================
-- MASTER ACCOUNT FIXES ROLLBACK SCRIPT
-- DESCRIÇÃO: <PERSON><PERSON>t para reverter as mudan<PERSON><PERSON> da migração master-account-fixes
-- VERSÃO: 1.0
-- DATA: 2025-01-29
-- AUTOR: <PERSON>ro AI Assistant
-- =====================================================

-- =====================================================
-- MASTER ACCOUNT FIXES ROLLBACK SCRIPT
-- NOTA: Execute este script diretamente no SQL Editor do Supabase
-- =====================================================

-- Start transaction for atomic execution
BEGIN;

-- Create a temporary table to track rollback progress
CREATE TEMP TABLE rollback_progress (
    step_number INTEGER PRIMARY KEY,
    step_name TEXT NOT NULL,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'failed')),
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT
);

-- Insert all rollback steps (in reverse order)
INSERT INTO rollback_progress (step_number, step_name) VALUES
(1, 'Drop helper functions'),
(2, 'Drop indexes from master_audit_logs'),
(3, 'Remove foreign key constraint from master_audit_logs'),
(4, 'Drop get_master_recent_activities functions'),
(5, 'Drop get_master_dashboard_stats function'),
(6, 'Drop indexes from master_plans'),
(7, 'Remove columns from master_plans'),
(8, 'Verify rollback completion'),
(9, 'Log rollback completion');

-- Function to update rollback progress
CREATE OR REPLACE FUNCTION update_rollback_step(
    p_step_number INTEGER,
    p_status TEXT,
    p_error_message TEXT DEFAULT NULL
) RETURNS VOID AS $func$
BEGIN
    UPDATE rollback_progress 
    SET 
        status = p_status,
        started_at = CASE WHEN p_status = 'running' THEN NOW() ELSE started_at END,
        completed_at = CASE WHEN p_status IN ('completed', 'failed') THEN NOW() ELSE completed_at END,
        error_message = p_error_message
    WHERE step_number = p_step_number;
    
    RAISE NOTICE '[ROLLBACK STEP %] %: %', p_step_number, 
        (SELECT step_name FROM rollback_progress WHERE step_number = p_step_number), 
        p_status;
END;
$func$ LANGUAGE plpgsql;

-- =====================================================
-- STEP 1: DROP HELPER FUNCTIONS
-- =====================================================

DO $step1$
BEGIN
    PERFORM update_rollback_step(1, 'running');
    
    -- Drop helper function
    DROP FUNCTION IF EXISTS insert_master_audit_log(UUID, VARCHAR(100), VARCHAR(50), INTEGER, JSONB, JSONB, JSONB, INET, TEXT);
    
    RAISE NOTICE 'Dropped insert_master_audit_log function';
    
    PERFORM update_rollback_step(1, 'completed');
    
EXCEPTION WHEN OTHERS THEN
    PERFORM update_rollback_step(1, 'failed', SQLERRM);
    RAISE;
END;
$step1$;

-- =====================================================
-- STEP 2: DROP INDEXES FROM MASTER_AUDIT_LOGS
-- =====================================================

DO $step2$
BEGIN
    PERFORM update_rollback_step(2, 'running');
    
    -- Drop performance indexes
    DROP INDEX IF EXISTS idx_master_audit_logs_user_id_not_null;
    DROP INDEX IF EXISTS idx_master_audit_logs_created_at_desc;
    DROP INDEX IF EXISTS idx_master_audit_logs_action_created_at;
    DROP INDEX IF EXISTS idx_master_audit_logs_entity_type_id;
    
    RAISE NOTICE 'Dropped performance indexes from master_audit_logs';
    
    PERFORM update_rollback_step(2, 'completed');
    
EXCEPTION WHEN OTHERS THEN
    PERFORM update_rollback_step(2, 'failed', SQLERRM);
    RAISE;
END;
$step2$;

-- =====================================================
-- STEP 3: REMOVE FOREIGN KEY CONSTRAINT
-- =====================================================

DO $step3$
DECLARE
    constraint_exists BOOLEAN;
BEGIN
    PERFORM update_rollback_step(3, 'running');
    
    -- Check if foreign key constraint exists
    SELECT EXISTS (
        SELECT 1 
        FROM information_schema.table_constraints tc
        WHERE tc.table_name = 'master_audit_logs'
        AND tc.constraint_type = 'FOREIGN KEY'
        AND tc.constraint_name = 'fk_master_audit_logs_user_id'
    ) INTO constraint_exists;
    
    IF constraint_exists THEN
        ALTER TABLE master_audit_logs DROP CONSTRAINT fk_master_audit_logs_user_id;
        RAISE NOTICE 'Dropped foreign key constraint fk_master_audit_logs_user_id';
    ELSE
        RAISE NOTICE 'Foreign key constraint fk_master_audit_logs_user_id does not exist';
    END IF;
    
    PERFORM update_rollback_step(3, 'completed');
    
EXCEPTION WHEN OTHERS THEN
    PERFORM update_rollback_step(3, 'failed', SQLERRM);
    RAISE;
END;
$step3$;

-- =====================================================
-- STEP 4: DROP GET_MASTER_RECENT_ACTIVITIES FUNCTIONS
-- =====================================================

DO $step4$
BEGIN
    PERFORM update_rollback_step(4, 'running');
    
    -- Drop both versions of the function
    DROP FUNCTION IF EXISTS get_master_recent_activities();
    DROP FUNCTION IF EXISTS get_master_recent_activities(INTEGER, INTEGER);
    
    RAISE NOTICE 'Dropped get_master_recent_activities functions';
    
    PERFORM update_rollback_step(4, 'completed');
    
EXCEPTION WHEN OTHERS THEN
    PERFORM update_rollback_step(4, 'failed', SQLERRM);
    RAISE;
END;
$step4$;

-- =====================================================
-- STEP 5: DROP GET_MASTER_DASHBOARD_STATS FUNCTION
-- =====================================================

DO $step5$
BEGIN
    PERFORM update_rollback_step(5, 'running');
    
    -- Drop dashboard stats function
    DROP FUNCTION IF EXISTS get_master_dashboard_stats();
    
    RAISE NOTICE 'Dropped get_master_dashboard_stats function';
    
    PERFORM update_rollback_step(5, 'completed');
    
EXCEPTION WHEN OTHERS THEN
    PERFORM update_rollback_step(5, 'failed', SQLERRM);
    RAISE;
END;
$step5$;

-- =====================================================
-- STEP 6: DROP INDEXES FROM MASTER_PLANS
-- =====================================================

DO $step6$
BEGIN
    PERFORM update_rollback_step(6, 'running');
    
    -- Drop indexes for new columns
    DROP INDEX IF EXISTS idx_master_plans_is_trial;
    DROP INDEX IF EXISTS idx_master_plans_trial_days;
    DROP INDEX IF EXISTS idx_master_plans_max_storage_gb;
    
    RAISE NOTICE 'Dropped indexes from master_plans';
    
    PERFORM update_rollback_step(6, 'completed');
    
EXCEPTION WHEN OTHERS THEN
    PERFORM update_rollback_step(6, 'failed', SQLERRM);
    RAISE;
END;
$step6$;

-- =====================================================
-- STEP 7: REMOVE COLUMNS FROM MASTER_PLANS
-- =====================================================

DO $step7$
DECLARE
    column_exists BOOLEAN;
BEGIN
    PERFORM update_rollback_step(7, 'running');
    
    -- Check and drop is_trial column
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'master_plans' AND column_name = 'is_trial'
    ) INTO column_exists;
    
    IF column_exists THEN
        ALTER TABLE master_plans DROP COLUMN is_trial;
        RAISE NOTICE 'Dropped column is_trial from master_plans';
    END IF;
    
    -- Check and drop trial_days column
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'master_plans' AND column_name = 'trial_days'
    ) INTO column_exists;
    
    IF column_exists THEN
        ALTER TABLE master_plans DROP COLUMN trial_days;
        RAISE NOTICE 'Dropped column trial_days from master_plans';
    END IF;
    
    -- Check and drop max_storage_gb column
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'master_plans' AND column_name = 'max_storage_gb'
    ) INTO column_exists;
    
    IF column_exists THEN
        ALTER TABLE master_plans DROP COLUMN max_storage_gb;
        RAISE NOTICE 'Dropped column max_storage_gb from master_plans';
    END IF;
    
    PERFORM update_rollback_step(7, 'completed');
    
EXCEPTION WHEN OTHERS THEN
    PERFORM update_rollback_step(7, 'failed', SQLERRM);
    RAISE;
END;
$step7$;

-- =====================================================
-- STEP 8: VERIFY ROLLBACK COMPLETION
-- =====================================================

DO $step8$
DECLARE
    column_count INTEGER;
    function_count INTEGER;
    index_count INTEGER;
BEGIN
    PERFORM update_rollback_step(8, 'running');
    
    -- Verify columns were removed
    SELECT COUNT(*) INTO column_count
    FROM information_schema.columns 
    WHERE table_name = 'master_plans' 
    AND table_schema = 'public'
    AND column_name IN ('is_trial', 'trial_days', 'max_storage_gb');
    
    IF column_count > 0 THEN
        RAISE EXCEPTION 'Columns still exist in master_plans table. Found %', column_count;
    END IF;
    
    -- Verify functions were removed
    SELECT COUNT(*) INTO function_count
    FROM information_schema.routines
    WHERE routine_schema = 'public'
    AND routine_name IN ('get_master_dashboard_stats', 'get_master_recent_activities', 'insert_master_audit_log');
    
    IF function_count > 0 THEN
        RAISE EXCEPTION 'Functions still exist. Found %', function_count;
    END IF;
    
    -- Verify indexes were removed
    SELECT COUNT(*) INTO index_count
    FROM pg_indexes 
    WHERE tablename IN ('master_plans', 'master_audit_logs')
    AND indexname IN (
        'idx_master_plans_is_trial',
        'idx_master_plans_trial_days', 
        'idx_master_plans_max_storage_gb',
        'idx_master_audit_logs_user_id_not_null',
        'idx_master_audit_logs_created_at_desc',
        'idx_master_audit_logs_action_created_at',
        'idx_master_audit_logs_entity_type_id'
    );
    
    IF index_count > 0 THEN
        RAISE EXCEPTION 'Indexes still exist. Found %', index_count;
    END IF;
    
    RAISE NOTICE 'Rollback verification passed';
    RAISE NOTICE '  - Columns removed: 3';
    RAISE NOTICE '  - Functions removed: 3';
    RAISE NOTICE '  - Indexes removed: 7';
    
    PERFORM update_rollback_step(8, 'completed');
    
EXCEPTION WHEN OTHERS THEN
    PERFORM update_rollback_step(8, 'failed', SQLERRM);
    RAISE;
END;
$step8$;

-- =====================================================
-- STEP 9: LOG ROLLBACK COMPLETION
-- =====================================================

DO $step9$
BEGIN
    PERFORM update_rollback_step(9, 'running');
    
    -- Insert rollback log (if master_audit_logs still exists and has basic structure)
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'master_audit_logs') THEN
        INSERT INTO master_audit_logs (action, entity_type, entity_id, details, created_at) 
        VALUES (
            'migration_rollback_completed',
            'system',
            1,
            '{"migration": "master-account-fixes", "version": "1.0", "rollback_completed": true}'::jsonb,
            NOW()
        );
    END IF;
    
    PERFORM update_rollback_step(9, 'completed');
    
EXCEPTION WHEN OTHERS THEN
    PERFORM update_rollback_step(9, 'failed', SQLERRM);
    -- Don't raise here as this is just logging
END;
$step9$;

-- =====================================================
-- ROLLBACK SUMMARY
-- =====================================================

DO $summary$
DECLARE
    completed_steps INTEGER;
    failed_steps INTEGER;
    step_record RECORD;
BEGIN
    -- Count completed and failed steps
    SELECT 
        COUNT(*) FILTER (WHERE status = 'completed'),
        COUNT(*) FILTER (WHERE status = 'failed')
    INTO completed_steps, failed_steps
    FROM rollback_progress;
    
    RAISE NOTICE '';
    RAISE NOTICE '========================================';
    RAISE NOTICE 'MASTER ACCOUNT FIXES ROLLBACK SUMMARY';
    RAISE NOTICE '========================================';
    RAISE NOTICE 'Completed steps: %', completed_steps;
    RAISE NOTICE 'Failed steps: %', failed_steps;
    RAISE NOTICE '';
    
    -- Show detailed step results
    FOR step_record IN 
        SELECT step_number, step_name, status, 
               EXTRACT(EPOCH FROM (completed_at - started_at)) as duration_seconds,
               error_message
        FROM rollback_progress 
        ORDER BY step_number
    LOOP
        RAISE NOTICE '[ROLLBACK STEP %] % - % (%.2fs)', 
            step_record.step_number, 
            step_record.step_name, 
            step_record.status,
            COALESCE(step_record.duration_seconds, 0);
            
        IF step_record.error_message IS NOT NULL THEN
            RAISE NOTICE '  ERROR: %', step_record.error_message;
        END IF;
    END LOOP;
    
    IF failed_steps > 0 THEN
        RAISE EXCEPTION 'Rollback failed with % failed steps', failed_steps;
    END IF;
    
    RAISE NOTICE '';
    RAISE NOTICE 'Rollback completed successfully!';
    RAISE NOTICE 'All master account fixes have been reverted.';
END;
$summary$;

-- Commit the rollback transaction
COMMIT;

RAISE NOTICE '';
RAISE NOTICE '✅ Master Account Fixes Rollback completed successfully!';
RAISE NOTICE 'Transaction committed. All changes have been reverted.';