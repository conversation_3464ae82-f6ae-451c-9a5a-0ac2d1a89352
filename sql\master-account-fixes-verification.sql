-- =====================================================
-- MASTER ACCOUNT FIXES VERIFICATION SCRIPT
-- DESCRIÇÃO: Script para verificar se todas as mudanças foram aplicadas corretamente
-- VERSÃO: 1.0
-- DATA: 2025-01-29
-- AUTOR: Kiro AI Assistant
-- =====================================================

-- =====================================================
-- MASTER ACCOUNT FIXES VERIFICATION SCRIPT
-- NOTA: Execute este script diretamente no SQL Editor do Supabase
-- =====================================================

-- Create a temporary table to track verification results
CREATE TEMP TABLE verification_results (
    check_number INTEGER PRIMARY KEY,
    check_name TEXT NOT NULL,
    status TEXT CHECK (status IN ('PASS', 'FAIL', 'WARNING')),
    details TEXT,
    expected_value TEXT,
    actual_value TEXT
);

-- Function to add verification result
CREATE OR REPLACE FUNCTION add_verification_result(
    p_check_number INTEGER,
    p_check_name TEXT,
    p_status TEXT,
    p_details TEXT DEFAULT NULL,
    p_expected_value TEXT DEFAULT NULL,
    p_actual_value TEXT DEFAULT NULL
) RETURNS VOID AS $func$
BEGIN
    INSERT INTO verification_results (check_number, check_name, status, details, expected_value, actual_value)
    VALUES (p_check_number, p_check_name, p_status, p_details, p_expected_value, p_actual_value);
    
    RAISE NOTICE '[CHECK %] % - %', p_check_number, p_check_name, p_status;
    IF p_details IS NOT NULL THEN
        RAISE NOTICE '  Details: %', p_details;
    END IF;
END;
$func$ LANGUAGE plpgsql;

RAISE NOTICE '';
RAISE NOTICE '========================================';
RAISE NOTICE 'MASTER ACCOUNT FIXES VERIFICATION';
RAISE NOTICE '========================================';
RAISE NOTICE '';

-- =====================================================
-- CHECK 1: VERIFY MASTER_PLANS COLUMNS
-- =====================================================

DO $check1$
DECLARE
    column_count INTEGER;
    missing_columns TEXT[];
BEGIN
    -- Check if all required columns exist
    SELECT COUNT(*) INTO column_count
    FROM information_schema.columns 
    WHERE table_name = 'master_plans' 
    AND table_schema = 'public'
    AND column_name IN ('is_trial', 'trial_days', 'max_storage_gb');
    
    IF column_count = 3 THEN
        PERFORM add_verification_result(1, 'master_plans columns', 'PASS', 
            'All 3 required columns exist', '3', column_count::TEXT);
    ELSE
        -- Find which columns are missing
        SELECT ARRAY_AGG(col) INTO missing_columns
        FROM (VALUES ('is_trial'), ('trial_days'), ('max_storage_gb')) AS required(col)
        WHERE NOT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'master_plans' AND column_name = required.col
        );
        
        PERFORM add_verification_result(1, 'master_plans columns', 'FAIL', 
            'Missing columns: ' || ARRAY_TO_STRING(missing_columns, ', '), '3', column_count::TEXT);
    END IF;
END;
$check1$;

-- =====================================================
-- CHECK 2: VERIFY MASTER_PLANS INDEXES
-- =====================================================

DO $check2$
DECLARE
    index_count INTEGER;
    missing_indexes TEXT[];
BEGIN
    SELECT COUNT(*) INTO index_count
    FROM pg_indexes 
    WHERE tablename = 'master_plans'
    AND indexname IN ('idx_master_plans_is_trial', 'idx_master_plans_trial_days', 'idx_master_plans_max_storage_gb');
    
    IF index_count = 3 THEN
        PERFORM add_verification_result(2, 'master_plans indexes', 'PASS', 
            'All 3 required indexes exist', '3', index_count::TEXT);
    ELSE
        -- Find which indexes are missing
        SELECT ARRAY_AGG(idx) INTO missing_indexes
        FROM (VALUES ('idx_master_plans_is_trial'), ('idx_master_plans_trial_days'), ('idx_master_plans_max_storage_gb')) AS required(idx)
        WHERE NOT EXISTS (
            SELECT 1 FROM pg_indexes 
            WHERE tablename = 'master_plans' AND indexname = required.idx
        );
        
        PERFORM add_verification_result(2, 'master_plans indexes', 'FAIL', 
            'Missing indexes: ' || ARRAY_TO_STRING(missing_indexes, ', '), '3', index_count::TEXT);
    END IF;
END;
$check2$;

-- =====================================================
-- CHECK 3: VERIFY GET_MASTER_DASHBOARD_STATS FUNCTION
-- =====================================================

DO $check3$
DECLARE
    function_exists BOOLEAN;
    test_result RECORD;
BEGIN
    -- Check if function exists
    SELECT EXISTS (
        SELECT 1 FROM information_schema.routines
        WHERE routine_schema = 'public'
        AND routine_name = 'get_master_dashboard_stats'
        AND routine_type = 'FUNCTION'
    ) INTO function_exists;
    
    IF function_exists THEN
        -- Test function execution
        BEGIN
            SELECT * INTO test_result FROM get_master_dashboard_stats();
            
            IF test_result IS NOT NULL THEN
                PERFORM add_verification_result(3, 'get_master_dashboard_stats function', 'PASS', 
                    'Function exists and executes successfully', 'EXISTS + EXECUTES', 'EXISTS + EXECUTES');
            ELSE
                PERFORM add_verification_result(3, 'get_master_dashboard_stats function', 'WARNING', 
                    'Function exists but returned NULL', 'EXISTS + EXECUTES', 'EXISTS + NULL');
            END IF;
        EXCEPTION WHEN OTHERS THEN
            PERFORM add_verification_result(3, 'get_master_dashboard_stats function', 'FAIL', 
                'Function exists but execution failed: ' || SQLERRM, 'EXISTS + EXECUTES', 'EXISTS + ERROR');
        END;
    ELSE
        PERFORM add_verification_result(3, 'get_master_dashboard_stats function', 'FAIL', 
            'Function does not exist', 'EXISTS', 'NOT EXISTS');
    END IF;
END;
$check3$;

-- =====================================================
-- CHECK 4: VERIFY GET_MASTER_RECENT_ACTIVITIES FUNCTION
-- =====================================================

DO $check4$
DECLARE
    function_exists BOOLEAN;
    parameterized_exists BOOLEAN;
    test_count INTEGER;
BEGIN
    -- Check if parameterless function exists
    SELECT EXISTS (
        SELECT 1 FROM information_schema.routines
        WHERE routine_schema = 'public'
        AND routine_name = 'get_master_recent_activities'
        AND routine_type = 'FUNCTION'
        AND array_length(string_to_array(routine_definition, 'RETURNS'), 1) > 0
    ) INTO function_exists;
    
    -- Check if parameterized function exists
    SELECT EXISTS (
        SELECT 1 FROM pg_proc p
        JOIN pg_namespace n ON p.pronamespace = n.oid
        WHERE n.nspname = 'public'
        AND p.proname = 'get_master_recent_activities'
        AND p.pronargs = 2
    ) INTO parameterized_exists;
    
    IF function_exists AND parameterized_exists THEN
        -- Test function execution
        BEGIN
            SELECT COUNT(*) INTO test_count FROM get_master_recent_activities(5, 0);
            
            PERFORM add_verification_result(4, 'get_master_recent_activities function', 'PASS', 
                'Both function versions exist and execute successfully', 'EXISTS + EXECUTES', 'EXISTS + EXECUTES');
        EXCEPTION WHEN OTHERS THEN
            PERFORM add_verification_result(4, 'get_master_recent_activities function', 'FAIL', 
                'Functions exist but execution failed: ' || SQLERRM, 'EXISTS + EXECUTES', 'EXISTS + ERROR');
        END;
    ELSE
        PERFORM add_verification_result(4, 'get_master_recent_activities function', 'FAIL', 
            'Missing function versions. Parameterless: ' || function_exists || ', Parameterized: ' || parameterized_exists, 
            'BOTH EXISTS', 'MISSING');
    END IF;
END;
$check4$;

-- =====================================================
-- CHECK 5: VERIFY INSERT_MASTER_AUDIT_LOG FUNCTION
-- =====================================================

DO $check5$
DECLARE
    function_exists BOOLEAN;
    test_log_id INTEGER;
BEGIN
    -- Check if function exists
    SELECT EXISTS (
        SELECT 1 FROM information_schema.routines
        WHERE routine_schema = 'public'
        AND routine_name = 'insert_master_audit_log'
        AND routine_type = 'FUNCTION'
    ) INTO function_exists;
    
    IF function_exists THEN
        -- Test function execution
        BEGIN
            SELECT insert_master_audit_log(
                NULL,
                'verification_test',
                'system',
                1,
                NULL,
                NULL,
                '{"test": "verification"}'::jsonb
            ) INTO test_log_id;
            
            IF test_log_id IS NOT NULL THEN
                -- Clean up test data
                DELETE FROM master_audit_logs WHERE id = test_log_id;
                
                PERFORM add_verification_result(5, 'insert_master_audit_log function', 'PASS', 
                    'Function exists and executes successfully', 'EXISTS + EXECUTES', 'EXISTS + EXECUTES');
            ELSE
                PERFORM add_verification_result(5, 'insert_master_audit_log function', 'WARNING', 
                    'Function exists but returned NULL', 'EXISTS + EXECUTES', 'EXISTS + NULL');
            END IF;
        EXCEPTION WHEN OTHERS THEN
            PERFORM add_verification_result(5, 'insert_master_audit_log function', 'FAIL', 
                'Function exists but execution failed: ' || SQLERRM, 'EXISTS + EXECUTES', 'EXISTS + ERROR');
        END;
    ELSE
        PERFORM add_verification_result(5, 'insert_master_audit_log function', 'FAIL', 
            'Function does not exist', 'EXISTS', 'NOT EXISTS');
    END IF;
END;
$check5$;

-- =====================================================
-- CHECK 6: VERIFY MASTER_AUDIT_LOGS FOREIGN KEY
-- =====================================================

DO $check6$
DECLARE
    constraint_exists BOOLEAN;
BEGIN
    -- Check if foreign key constraint exists
    SELECT EXISTS (
        SELECT 1 
        FROM information_schema.table_constraints tc
        WHERE tc.table_name = 'master_audit_logs'
        AND tc.constraint_type = 'FOREIGN KEY'
        AND tc.constraint_name = 'fk_master_audit_logs_user_id'
    ) INTO constraint_exists;
    
    IF constraint_exists THEN
        PERFORM add_verification_result(6, 'master_audit_logs foreign key', 'PASS', 
            'Foreign key constraint exists', 'EXISTS', 'EXISTS');
    ELSE
        PERFORM add_verification_result(6, 'master_audit_logs foreign key', 'FAIL', 
            'Foreign key constraint missing', 'EXISTS', 'NOT EXISTS');
    END IF;
END;
$check6$;

-- =====================================================
-- CHECK 7: VERIFY MASTER_AUDIT_LOGS INDEXES
-- =====================================================

DO $check7$
DECLARE
    index_count INTEGER;
    expected_indexes TEXT[] := ARRAY[
        'idx_master_audit_logs_user_id_not_null',
        'idx_master_audit_logs_created_at_desc',
        'idx_master_audit_logs_action_created_at',
        'idx_master_audit_logs_entity_type_id'
    ];
    missing_indexes TEXT[];
BEGIN
    SELECT COUNT(*) INTO index_count
    FROM pg_indexes 
    WHERE tablename = 'master_audit_logs'
    AND indexname = ANY(expected_indexes);
    
    IF index_count = 4 THEN
        PERFORM add_verification_result(7, 'master_audit_logs indexes', 'PASS', 
            'All 4 required indexes exist', '4', index_count::TEXT);
    ELSE
        -- Find which indexes are missing
        SELECT ARRAY_AGG(idx) INTO missing_indexes
        FROM UNNEST(expected_indexes) AS idx
        WHERE NOT EXISTS (
            SELECT 1 FROM pg_indexes 
            WHERE tablename = 'master_audit_logs' AND indexname = idx
        );
        
        PERFORM add_verification_result(7, 'master_audit_logs indexes', 'FAIL', 
            'Missing indexes: ' || ARRAY_TO_STRING(missing_indexes, ', '), '4', index_count::TEXT);
    END IF;
END;
$check7$;

-- =====================================================
-- CHECK 8: VERIFY DATA INTEGRITY
-- =====================================================

DO $check8$
DECLARE
    orphaned_count INTEGER;
    null_trial_count INTEGER;
BEGIN
    -- Check for orphaned audit logs
    SELECT COUNT(*) INTO orphaned_count
    FROM master_audit_logs mal
    WHERE mal.user_id IS NOT NULL
    AND NOT EXISTS (SELECT 1 FROM auth.users au WHERE au.id = mal.user_id);
    
    -- Check for NULL trial values in master_plans
    SELECT COUNT(*) INTO null_trial_count
    FROM master_plans
    WHERE is_trial IS NULL OR trial_days IS NULL;
    
    IF orphaned_count = 0 AND null_trial_count = 0 THEN
        PERFORM add_verification_result(8, 'data integrity', 'PASS', 
            'No orphaned audit logs or NULL trial values', '0 orphaned, 0 NULL', '0 orphaned, 0 NULL');
    ELSE
        PERFORM add_verification_result(8, 'data integrity', 'WARNING', 
            'Found ' || orphaned_count || ' orphaned audit logs and ' || null_trial_count || ' NULL trial values', 
            '0 orphaned, 0 NULL', orphaned_count || ' orphaned, ' || null_trial_count || ' NULL');
    END IF;
END;
$check8$;

-- =====================================================
-- CHECK 9: VERIFY COLUMN COMMENTS
-- =====================================================

DO $check9$
DECLARE
    comment_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO comment_count
    FROM information_schema.columns c
    JOIN pg_description d ON d.objoid = (
        SELECT oid FROM pg_class WHERE relname = c.table_name
    ) AND d.objsubid = c.ordinal_position
    WHERE c.table_name = 'master_plans'
    AND c.column_name IN ('is_trial', 'trial_days', 'max_storage_gb');
    
    IF comment_count = 3 THEN
        PERFORM add_verification_result(9, 'column comments', 'PASS', 
            'All 3 columns have comments', '3', comment_count::TEXT);
    ELSE
        PERFORM add_verification_result(9, 'column comments', 'WARNING', 
            'Some columns missing comments', '3', comment_count::TEXT);
    END IF;
END;
$check9$;

-- =====================================================
-- VERIFICATION SUMMARY
-- =====================================================

DO $summary$
DECLARE
    total_checks INTEGER;
    passed_checks INTEGER;
    failed_checks INTEGER;
    warning_checks INTEGER;
    check_record RECORD;
    overall_status TEXT;
BEGIN
    -- Count results
    SELECT 
        COUNT(*),
        COUNT(*) FILTER (WHERE status = 'PASS'),
        COUNT(*) FILTER (WHERE status = 'FAIL'),
        COUNT(*) FILTER (WHERE status = 'WARNING')
    INTO total_checks, passed_checks, failed_checks, warning_checks
    FROM verification_results;
    
    -- Determine overall status
    IF failed_checks = 0 AND warning_checks = 0 THEN
        overall_status := 'ALL CHECKS PASSED';
    ELSIF failed_checks = 0 THEN
        overall_status := 'PASSED WITH WARNINGS';
    ELSE
        overall_status := 'FAILED';
    END IF;
    
    RAISE NOTICE '';
    RAISE NOTICE '========================================';
    RAISE NOTICE 'VERIFICATION SUMMARY';
    RAISE NOTICE '========================================';
    RAISE NOTICE 'Overall Status: %', overall_status;
    RAISE NOTICE 'Total Checks: %', total_checks;
    RAISE NOTICE 'Passed: %', passed_checks;
    RAISE NOTICE 'Failed: %', failed_checks;
    RAISE NOTICE 'Warnings: %', warning_checks;
    RAISE NOTICE '';
    
    -- Show detailed results
    RAISE NOTICE 'Detailed Results:';
    FOR check_record IN 
        SELECT check_number, check_name, status, details
        FROM verification_results 
        ORDER BY check_number
    LOOP
        RAISE NOTICE '[%] % - %', 
            check_record.check_number, 
            check_record.check_name, 
            check_record.status;
        IF check_record.details IS NOT NULL THEN
            RAISE NOTICE '    %', check_record.details;
        END IF;
    END LOOP;
    
    RAISE NOTICE '';
    IF failed_checks = 0 THEN
        RAISE NOTICE '✅ Migration verification completed successfully!';
        IF warning_checks > 0 THEN
            RAISE NOTICE '⚠️  Some warnings were found but they do not affect functionality.';
        END IF;
    ELSE
        RAISE NOTICE '❌ Migration verification failed!';
        RAISE NOTICE 'Please review the failed checks and re-run the migration if necessary.';
    END IF;
END;
$summary$;