-- Script simples para migrar TODOS os jogadores existentes para o sistema global
-- Este script usa apenas colunas que sabemos que existem

-- 1. Verificar quantos jogadores temos para migrar
SELECT 'Status antes da migração:' as info;
SELECT 
  COUNT(*) as total_players,
  COUNT(CASE WHEN cpf_number IS NOT NULL AND cpf_number != '' THEN 1 END) as players_with_cpf,
  COUNT(CASE WHEN global_player_id IS NOT NULL THEN 1 END) as already_migrated
FROM players;

-- 2. Migrar TODOS os jogadores que têm CPF
DO $$
DECLARE
  player_record RECORD;
  v_global_player_id UUID;
  v_migrated_count INTEGER := 0;
  v_linked_count INTEGER := 0;
  v_error_count INTEGER := 0;
BEGIN
  RAISE NOTICE 'Iniciando migração de todos os jogadores...';
  
  -- Iterar sobre TODOS os jogadores que têm CPF
  FOR player_record IN 
    SELECT 
      id, club_id, name, cpf_number, birthdate, birthplace, nationality,
      rg_number, father_name, mother_name, phone, email, height, weight
    FROM players 
    WHERE global_player_id IS NULL
      AND cpf_number IS NOT NULL 
      AND cpf_number != ''
    ORDER BY club_id, name
  LOOP
    BEGIN
      -- Verificar se já existe um jogador global com este CPF
      SELECT id INTO v_global_player_id 
      FROM global_players 
      WHERE cpf_number = player_record.cpf_number;
      
      IF v_global_player_id IS NULL THEN
        -- Criar novo jogador global
        INSERT INTO global_players (
          cpf_number, name, birthdate, birthplace, nationality,
          rg_number, father_name, mother_name, phone, email, height, weight
        ) VALUES (
          player_record.cpf_number,
          player_record.name,
          CASE 
            WHEN player_record.birthdate IS NOT NULL AND player_record.birthdate != '' AND player_record.birthdate != '0000-00-00'
            THEN player_record.birthdate::DATE 
            ELSE NULL 
          END,
          player_record.birthplace,
          COALESCE(player_record.nationality, 'Brasil'),
          player_record.rg_number,
          player_record.father_name,
          player_record.mother_name,
          player_record.phone,
          player_record.email,
          player_record.height,
          player_record.weight
        ) RETURNING id INTO v_global_player_id;
        
        v_migrated_count := v_migrated_count + 1;
        
        IF v_migrated_count % 10 = 0 THEN
          RAISE NOTICE 'Migrados: % jogadores...', v_migrated_count;
        END IF;
      ELSE
        v_linked_count := v_linked_count + 1;
      END IF;
      
      -- Atualizar o jogador com o global_player_id
      UPDATE players 
      SET global_player_id = v_global_player_id 
      WHERE id = player_record.id;
      
      -- Migrar documentos para a tabela global (se existirem)
      INSERT INTO global_player_documents (
        global_player_id, document_type, file_url, original_club_id, uploaded_at
      )
      SELECT 
        v_global_player_id,
        pd.document_type,
        pd.file_url,
        pd.club_id,
        pd.uploaded_at
      FROM player_documents pd
      WHERE pd.player_id = player_record.id
        AND NOT EXISTS (
          SELECT 1 FROM global_player_documents gpd
          WHERE gpd.global_player_id = v_global_player_id
            AND gpd.document_type = pd.document_type
            AND gpd.file_url = pd.file_url
        );
      
    EXCEPTION WHEN OTHERS THEN
      RAISE NOTICE 'Erro ao migrar jogador % (CPF: %): %', 
        player_record.name, player_record.cpf_number, SQLERRM;
      v_error_count := v_error_count + 1;
    END;
  END LOOP;
  
  RAISE NOTICE 'Migração concluída!';
  RAISE NOTICE 'Novos jogadores globais criados: %', v_migrated_count;
  RAISE NOTICE 'Jogadores vinculados a registros existentes: %', v_linked_count;
  RAISE NOTICE 'Erros encontrados: %', v_error_count;
END;
$$;

-- 3. Verificar resultado da migração
SELECT 'Status após migração completa:' as info;
SELECT * FROM check_migration_status();

-- 4. Mostrar estatísticas por clube
SELECT 'Jogadores migrados por clube:' as info;
SELECT 
  ci.name as club_name,
  COUNT(*) as total_players,
  COUNT(CASE WHEN p.global_player_id IS NOT NULL THEN 1 END) as migrated_players,
  COUNT(CASE WHEN p.cpf_number IS NULL OR p.cpf_number = '' THEN 1 END) as without_cpf
FROM players p
JOIN club_info ci ON ci.id = p.club_id
GROUP BY ci.id, ci.name
ORDER BY ci.name;

-- 5. Verificar se há duplicatas que precisam ser resolvidas
SELECT 'Verificando duplicatas após migração:' as info;
SELECT * FROM find_duplicate_cpf_players();

-- 6. Mostrar alguns exemplos de jogadores migrados
SELECT 'Exemplos de jogadores migrados:' as info;
SELECT 
  p.name,
  p.cpf_number,
  ci.name as club_name,
  p.status,
  CASE WHEN p.global_player_id IS NOT NULL THEN 'Migrado' ELSE 'Pendente' END as migration_status
FROM players p
JOIN club_info ci ON ci.id = p.club_id
WHERE p.global_player_id IS NOT NULL
ORDER BY ci.name, p.name
LIMIT 20;

SELECT 'Migração completa finalizada!' as status;