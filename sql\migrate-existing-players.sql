-- Script de migração para jogadores existentes
-- Este script migra jogadores existentes para o sistema global de transferências

-- 1. Função para migrar todos os jogadores de um clube
CREATE OR REPLACE FUNCTION migrate_club_players_to_global(p_club_id INTEGER)
RETURNS TABLE (
  player_id UUID,
  global_player_id UUID,
  player_name TEXT,
  cpf_number TEXT,
  status TEXT
) AS $$
DECLARE
  player_record RECORD;
  v_global_player_id UUID;
  v_existing_global_id UUID;
BEGIN
  -- Iterar sobre todos os jogadores do clube que ainda não foram migrados
  FOR player_record IN 
    SELECT * FROM players 
    WHERE club_id = p_club_id 
      AND global_player_id IS NULL
      AND cpf_number IS NOT NULL
      AND cpf_number != ''
  LOOP
    BEGIN
      -- Verificar se já existe um jogador global com este CPF
      SELECT id INTO v_existing_global_id 
      FROM global_players 
      WHERE cpf_number = player_record.cpf_number;
      
      IF v_existing_global_id IS NOT NULL THEN
        -- Usar o jogador global existente
        v_global_player_id := v_existing_global_id;
        
        -- Atualizar o jogador com o global_player_id
        UPDATE players 
        SET global_player_id = v_global_player_id 
        WHERE id = player_record.id;
        
        RETURN QUERY SELECT 
          player_record.id,
          v_global_player_id,
          player_record.name,
          player_record.cpf_number,
          'LINKED_TO_EXISTING'::TEXT;
      ELSE
        -- Criar novo jogador global
        INSERT INTO global_players (
          cpf_number, name, birthdate, birthplace, nationality,
          rg_number, father_name, mother_name, phone, email, height, weight
        ) VALUES (
          player_record.cpf_number,
          player_record.name,
          player_record.birthdate::DATE,
          player_record.birthplace,
          COALESCE(player_record.nationality, 'Brasil'),
          player_record.rg_number,
          player_record.father_name,
          player_record.mother_name,
          player_record.phone,
          player_record.email,
          player_record.height,
          player_record.weight
        ) RETURNING id INTO v_global_player_id;
        
        -- Atualizar o jogador com o global_player_id
        UPDATE players 
        SET global_player_id = v_global_player_id 
        WHERE id = player_record.id;
        
        -- Migrar documentos para a tabela global
        INSERT INTO global_player_documents (
          global_player_id, document_type, file_url, original_club_id, uploaded_at
        )
        SELECT 
          v_global_player_id,
          pd.document_type,
          pd.file_url,
          pd.club_id,
          pd.uploaded_at
        FROM player_documents pd
        WHERE pd.player_id = player_record.id;
        
        RETURN QUERY SELECT 
          player_record.id,
          v_global_player_id,
          player_record.name,
          player_record.cpf_number,
          'MIGRATED'::TEXT;
      END IF;
      
    EXCEPTION WHEN OTHERS THEN
      -- Em caso de erro, retornar o erro
      RETURN QUERY SELECT 
        player_record.id,
        NULL::UUID,
        player_record.name,
        player_record.cpf_number,
        ('ERROR: ' || SQLERRM)::TEXT;
    END;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- 2. Função para migrar todos os jogadores do sistema
CREATE OR REPLACE FUNCTION migrate_all_players_to_global()
RETURNS TABLE (
  club_id INTEGER,
  club_name TEXT,
  migrated_count INTEGER,
  linked_count INTEGER,
  error_count INTEGER
) AS $$
DECLARE
  club_record RECORD;
  migration_result RECORD;
  v_migrated_count INTEGER := 0;
  v_linked_count INTEGER := 0;
  v_error_count INTEGER := 0;
BEGIN
  -- Iterar sobre todos os clubes
  FOR club_record IN 
    SELECT id, name FROM club_info ORDER BY id
  LOOP
    v_migrated_count := 0;
    v_linked_count := 0;
    v_error_count := 0;
    
    -- Migrar jogadores do clube
    FOR migration_result IN 
      SELECT * FROM migrate_club_players_to_global(club_record.id)
    LOOP
      CASE migration_result.status
        WHEN 'MIGRATED' THEN v_migrated_count := v_migrated_count + 1;
        WHEN 'LINKED_TO_EXISTING' THEN v_linked_count := v_linked_count + 1;
        ELSE v_error_count := v_error_count + 1;
      END CASE;
    END LOOP;
    
    -- Retornar resultado do clube se houve alguma atividade
    IF (v_migrated_count + v_linked_count + v_error_count) > 0 THEN
      RETURN QUERY SELECT 
        club_record.id,
        club_record.name,
        v_migrated_count,
        v_linked_count,
        v_error_count;
    END IF;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- 3. Função para verificar o status da migração
CREATE OR REPLACE FUNCTION check_migration_status()
RETURNS TABLE (
  total_players INTEGER,
  migrated_players INTEGER,
  pending_players INTEGER,
  players_without_cpf INTEGER,
  global_players_count INTEGER,
  global_documents_count INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    (SELECT COUNT(*)::INTEGER FROM players) as total_players,
    (SELECT COUNT(*)::INTEGER FROM players WHERE global_player_id IS NOT NULL) as migrated_players,
    (SELECT COUNT(*)::INTEGER FROM players WHERE global_player_id IS NULL AND cpf_number IS NOT NULL AND cpf_number != '') as pending_players,
    (SELECT COUNT(*)::INTEGER FROM players WHERE cpf_number IS NULL OR cpf_number = '') as players_without_cpf,
    (SELECT COUNT(*)::INTEGER FROM global_players) as global_players_count,
    (SELECT COUNT(*)::INTEGER FROM global_player_documents) as global_documents_count;
END;
$$ LANGUAGE plpgsql;

-- 4. Função para limpar dados de teste (usar com cuidado!)
CREATE OR REPLACE FUNCTION cleanup_transfer_system()
RETURNS VOID AS $$
BEGIN
  -- Remover referências nas tabelas dependentes
  UPDATE players SET global_player_id = NULL, is_transfer = FALSE, transfer_id = NULL;
  
  -- Limpar tabelas do sistema de transferência
  DELETE FROM player_transfers;
  DELETE FROM global_player_documents;
  DELETE FROM global_players;
  
  -- Resetar sequências
  ALTER SEQUENCE player_transfers_id_seq RESTART WITH 1;
  ALTER SEQUENCE global_player_documents_id_seq RESTART WITH 1;
END;
$$ LANGUAGE plpgsql;

-- 5. Função para gerar relatório de duplicatas de CPF
CREATE OR REPLACE FUNCTION find_duplicate_cpf_players()
RETURNS TABLE (
  cpf_number TEXT,
  player_count INTEGER,
  player_names TEXT[],
  club_names TEXT[]
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.cpf_number,
    COUNT(*)::INTEGER as player_count,
    ARRAY_AGG(p.name) as player_names,
    ARRAY_AGG(ci.name) as club_names
  FROM players p
  JOIN club_info ci ON ci.id = p.club_id
  WHERE p.cpf_number IS NOT NULL 
    AND p.cpf_number != ''
  GROUP BY p.cpf_number
  HAVING COUNT(*) > 1
  ORDER BY COUNT(*) DESC;
END;
$$ LANGUAGE plpgsql;

-- 6. Função para resolver duplicatas de CPF
CREATE OR REPLACE FUNCTION resolve_duplicate_cpf(p_cpf TEXT, p_keep_player_id UUID)
RETURNS TABLE (
  action TEXT,
  player_id UUID,
  player_name TEXT,
  club_name TEXT
) AS $$
DECLARE
  duplicate_record RECORD;
  v_global_player_id UUID;
BEGIN
  -- Verificar se o jogador a manter existe
  IF NOT EXISTS (SELECT 1 FROM players WHERE id = p_keep_player_id AND cpf_number = p_cpf) THEN
    RAISE EXCEPTION 'Jogador especificado não encontrado com este CPF';
  END IF;
  
  -- Migrar o jogador principal para o sistema global
  SELECT migrate_player_to_global(p_keep_player_id) INTO v_global_player_id;
  
  RETURN QUERY SELECT 
    'KEPT'::TEXT,
    p_keep_player_id,
    p.name,
    ci.name
  FROM players p
  JOIN club_info ci ON ci.id = p.club_id
  WHERE p.id = p_keep_player_id;
  
  -- Processar duplicatas
  FOR duplicate_record IN 
    SELECT p.id, p.name, ci.name as club_name
    FROM players p
    JOIN club_info ci ON ci.id = p.club_id
    WHERE p.cpf_number = p_cpf 
      AND p.id != p_keep_player_id
  LOOP
    -- Marcar como inativo e limpar CPF
    UPDATE players 
    SET status = 'inativo', 
        cpf_number = NULL,
        observation = COALESCE(observation || ' | ', '') || 'CPF removido por duplicata em ' || NOW()::DATE
    WHERE id = duplicate_record.id;
    
    RETURN QUERY SELECT 
      'DEACTIVATED'::TEXT,
      duplicate_record.id,
      duplicate_record.name,
      duplicate_record.club_name;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Comentários para documentação
COMMENT ON FUNCTION migrate_club_players_to_global(INTEGER) IS 'Migra todos os jogadores de um clube para o sistema global';
COMMENT ON FUNCTION migrate_all_players_to_global() IS 'Migra todos os jogadores do sistema para o sistema global';
COMMENT ON FUNCTION check_migration_status() IS 'Verifica o status da migração dos jogadores';
COMMENT ON FUNCTION cleanup_transfer_system() IS 'Limpa todos os dados do sistema de transferência (usar com cuidado!)';
COMMENT ON FUNCTION find_duplicate_cpf_players() IS 'Encontra jogadores com CPF duplicado';
COMMENT ON FUNCTION resolve_duplicate_cpf(TEXT, UUID) IS 'Resolve duplicatas de CPF mantendo um jogador e desativando os outros';

-- Exemplo de uso:
-- SELECT * FROM check_migration_status();
-- SELECT * FROM find_duplicate_cpf_players();
-- SELECT * FROM migrate_all_players_to_global();
-- SELECT * FROM migrate_club_players_to_global(1);