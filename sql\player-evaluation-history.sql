-- Create table to store historical player evaluations
CREATE TABLE IF NOT EXISTS player_evaluation_history (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id) ON DELETE CASCADE,
  player_id UUID REFERENCES players(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  status TEXT,
  is_locked BOOLEAN,
  signature_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE,
  created_by UUID REFERENCES auth.users(id),
  updated_by UUID REFERENCES auth.users(id),
  last_viewed_at TIMESTAMP WITH TIME ZONE,
  approved_by_manager <PERSON>UID REFERENCES auth.users(id),
  approved_by_president <PERSON><PERSON>D REFERENCES auth.users(id),
  manager_approved_at TIMESTAMP WITH TIME ZONE,
  president_approved_at TIMESTAMP WITH TIME ZONE,
  manager_notes TEXT,
  president_notes TEXT,
  requires_manager_approval BOOLEAN,
  requires_president_approval BOOLEAN,
  archived_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_player_evaluation_history_player ON player_evaluation_history(player_id);
CREATE INDEX IF NOT EXISTS idx_player_evaluation_history_club ON player_evaluation_history(club_id);

-- Enable RLS and add policies
ALTER TABLE player_evaluation_history ENABLE ROW LEVEL SECURITY;

CREATE POLICY player_evaluation_history_select_policy ON player_evaluation_history
  FOR SELECT
  USING (club_id = get_current_club_id());

CREATE POLICY player_evaluation_history_insert_policy ON player_evaluation_history
  FOR INSERT
  WITH CHECK (club_id = get_current_club_id());

CREATE POLICY player_evaluation_history_update_policy ON player_evaluation_history
  FOR UPDATE
  USING (club_id = get_current_club_id());

CREATE POLICY player_evaluation_history_delete_policy ON player_evaluation_history
  FOR DELETE
  USING (club_id = get_current_club_id());

COMMENT ON TABLE player_evaluation_history IS 'Stores historical player evaluations with RLS policies based on club';