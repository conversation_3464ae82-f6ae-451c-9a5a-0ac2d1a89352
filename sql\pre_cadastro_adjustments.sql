-- Ajustes para o sistema de pré-cadastro
-- Este arquivo contém possíveis ajustes no banco de dados para suportar as novas funcionalidades

-- 1. Tornar o campo CPF obrigatório na tabela player_evaluation_invitations (se necessário)
-- ALTER TABLE player_evaluation_invitations 
-- ALTER COLUMN cpf SET NOT NULL;

-- 2. <PERSON><PERSON>onar índice para busca por CPF (melhoria de performance)
-- CREATE INDEX IF NOT EXISTS idx_player_evaluation_invitations_cpf 
-- ON player_evaluation_invitations(cpf);

-- 3. Adicionar índice para busca de jogadores por CPF (melhoria de performance)
-- CREATE INDEX IF NOT EXISTS idx_players_cpf_number 
-- ON players(cpf_number);

-- 4. Função para buscar jogador por CPF globalmente (bypass RLS) - Versão Alternativa
CREATE OR REPLACE FUNCTION search_player_by_cpf_global(p_cpf TEXT)
RETURNS TABLE(
  id UUID,
  club_id INTEGER,
  name TEXT,
  email TEXT,
  cpf_number TEXT,
  birthdate DATE,
  position TEXT,
  height INTEGER,
  weight DECIMAL,
  nationality TEXT,
  rg_number TEXT,
  phone TEXT,
  address TEXT,
  city TEXT,
  state TEXT,
  zip_code TEXT,
  father_name TEXT,
  mother_name TEXT,
  referred_by TEXT
) 
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.id,
    p.club_id,
    p.name,
    p.email,
    p.cpf_number,
    p.birthdate,
    p.position,
    p.height,
    p.weight,
    p.nationality,
    p.rg_number,
    p.phone,
    p.address,
    p.city,
    p.state,
    p.zip_code,
    p.father_name,
    p.mother_name,
    p.referred_by
  FROM players p
  WHERE p.cpf_number = p_cpf
  LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- 5. Função principal usada pelo sistema de transferências (compatível)
CREATE OR REPLACE FUNCTION search_player_by_cpf(p_cpf TEXT)
RETURNS TABLE(
  found BOOLEAN,
  global_player_id UUID,
  name TEXT,
  birthdate DATE,
  birthplace TEXT,
  nationality TEXT,
  rg_number TEXT,
  father_name TEXT,
  mother_name TEXT,
  phone TEXT,
  email TEXT,
  height INTEGER,
  weight DECIMAL,
  last_club_id INTEGER,
  last_club_name TEXT,
  current_status TEXT,
  is_active_elsewhere BOOLEAN,
  active_club_name TEXT,
  documents_count INTEGER
)
SECURITY DEFINER
AS $$
DECLARE
  player_record RECORD;
  doc_count INTEGER;
  club_name TEXT;
BEGIN
  -- Buscar jogador por CPF
  SELECT * INTO player_record
  FROM players p
  WHERE p.cpf_number = p_cpf
  ORDER BY p.created_at DESC
  LIMIT 1;
  
  IF player_record IS NULL THEN
    -- Jogador não encontrado
    RETURN QUERY SELECT 
      FALSE, -- found
      NULL::UUID, -- global_player_id
      NULL::TEXT, -- name
      NULL::DATE, -- birthdate
      NULL::TEXT, -- birthplace
      NULL::TEXT, -- nationality
      NULL::TEXT, -- rg_number
      NULL::TEXT, -- father_name
      NULL::TEXT, -- mother_name
      NULL::TEXT, -- phone
      NULL::TEXT, -- email
      NULL::INTEGER, -- height
      NULL::DECIMAL, -- weight
      NULL::INTEGER, -- last_club_id
      NULL::TEXT, -- last_club_name
      NULL::TEXT, -- current_status
      FALSE, -- is_active_elsewhere
      NULL::TEXT, -- active_club_name
      0; -- documents_count
  ELSE
    -- Contar documentos do jogador
    SELECT COUNT(*) INTO doc_count
    FROM player_documents pd
    WHERE pd.player_id = player_record.id;
    
    -- Buscar nome do clube
    SELECT ci.name INTO club_name
    FROM club_info ci
    WHERE ci.id = player_record.club_id;
    
    -- Retornar dados do jogador encontrado
    RETURN QUERY SELECT 
      TRUE, -- found
      player_record.id, -- global_player_id
      player_record.name,
      player_record.birthdate,
      player_record.address, -- usando address como birthplace
      player_record.nationality,
      player_record.rg_number,
      player_record.father_name,
      player_record.mother_name,
      player_record.phone,
      player_record.email,
      player_record.height,
      player_record.weight,
      player_record.club_id, -- last_club_id
      club_name, -- last_club_name
      player_record.status, -- current_status
      (player_record.status = 'disponivel'), -- is_active_elsewhere
      club_name, -- active_club_name
      doc_count; -- documents_count
  END IF;
END;
$$ LANGUAGE plpgsql;

-- Observações:
-- - A maioria das funcionalidades implementadas não requer mudanças no banco
-- - Os ajustes são principalmente na lógica da aplicação
-- - Execute apenas os comandos necessários após testar em ambiente de desenvolvimento