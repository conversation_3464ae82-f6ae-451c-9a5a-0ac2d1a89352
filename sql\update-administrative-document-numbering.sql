-- Fix document numbering to be per club instead of global sequence

-- Remove existing trigger, function, and sequence
DROP TRIGGER IF EXISTS set_document_number ON administrative_documents;
DROP FUNCTION IF EXISTS generate_document_number();
DROP SEQUENCE IF EXISTS administrative_document_number_seq;

-- Recreate function to generate document numbers per club
CREATE OR REPLACE FUNCTION generate_document_number()
RETURNS TRIGGER AS $$
DECLARE
  next_number INTEGER;
BEGIN
  -- Lock table to ensure sequential numbers per club
  LOCK TABLE administrative_documents IN SHARE ROW EXCLUSIVE MODE;
  -- Get the next document number for the club
  SELECT COALESCE(MAX(document_number), 0) + 1 INTO next_number
  FROM administrative_documents
  WHERE club_id = NEW.club_id;
  NEW.document_number := next_number;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Recreate trigger to use the new function
CREATE TRIGGER set_document_number
BEFORE INSERT ON administrative_documents
FOR EACH ROW
EXECUTE FUNCTION generate_document_number();