-- Atualizar apenas as datas de nascimento

-- 1. Atualizar birthdates na global_players
UPDATE global_players gp
SET birthdate = p.birthdate::DATE
FROM (
  SELECT DISTINCT ON (cpf_number)
    cpf_number, birthdate
  FROM players 
  WHERE global_player_id IS NOT NULL
    AND cpf_number IS NOT NULL
    AND birthdate IS NOT NULL
    AND birthdate != ''
    AND birthdate ~ '^\d{4}-\d{2}-\d{2}$'  -- Formato YYYY-MM-DD
  ORDER BY cpf_number, id DESC
) p
WHERE gp.cpf_number = p.cpf_number;

-- 2. Verificar resultado
SELECT 'Datas atualizadas:' as info;
SELECT name, cpf_number, birthdate FROM global_players WHERE birthdate IS NOT NULL LIMIT 5;

-- 3. Testar busca com Rafael (deve ter data agora)
SELECT 'Rafael com data:' as info;
SELECT * FROM search_player_by_cpf('35000577809');

SELECT 'Datas atualizadas com sucesso!' as status;