-- Atualizar dados completos na tabela global_players

-- 1. Verificar dados atuais
SELECT 'Dados atuais na global_players (exemplo):' as info;
SELECT cpf_number, name, birthdate, height, weight, phone FROM global_players LIMIT 3;

-- 2. Atualizar global_players com dados completos dos players
UPDATE global_players gp
SET 
  birthplace = p.birthplace,
  nationality = COALESCE(p.nationality, 'Brasil'),
  rg_number = p.rg_number,
  father_name = p.father_name,
  mother_name = p.mother_name,
  phone = p.phone,
  email = p.email,
  height = p.height,
  weight = p.weight
FROM (
  SELECT DISTINCT ON (cpf_number)
    cpf_number, birthplace, nationality, rg_number,
    father_name, mother_name, phone, email, height, weight
  FROM players 
  WHERE global_player_id IS NOT NULL
    AND cpf_number IS NOT NULL
  ORDER BY cpf_number, id DESC  -- Pegar os dados mais recentes
) p
WHERE gp.cpf_number = p.cpf_number;

-- 3. Verificar dados após atualização
SELECT 'Dados após atualização (exemplo):' as info;
SELECT cpf_number, name, birthdate, height, weight, phone FROM global_players LIMIT 3;

-- 4. Testar busca por CPF do Rafael
SELECT 'Teste com Rafael Freitas:' as info;
SELECT * FROM search_player_by_cpf('35000577809');

-- 5. Verificar documentos do Rafael
SELECT 'Documentos do Rafael:' as info;
SELECT document_type, file_url, original_club_id 
FROM global_player_documents 
WHERE global_player_id = '0043e44f-cf38-4def-9116-67ba3f0c256a';

-- 6. Se não tem documentos, vamos migrar
INSERT INTO global_player_documents (global_player_id, document_type, file_url, original_club_id, uploaded_at)
SELECT 
  p.global_player_id,
  pd.document_type,
  pd.file_url,
  pd.club_id,
  pd.uploaded_at
FROM player_documents pd
JOIN players p ON p.id = pd.player_id
WHERE p.cpf_number = '35000577809'
  AND p.global_player_id IS NOT NULL
  AND NOT EXISTS (
    SELECT 1 FROM global_player_documents gpd
    WHERE gpd.global_player_id = p.global_player_id
      AND gpd.document_type = pd.document_type
      AND gpd.file_url = pd.file_url
  );

-- 7. Verificar documentos após migração
SELECT 'Documentos após migração:' as info;
SELECT COUNT(*) as total_docs FROM global_player_documents 
WHERE global_player_id = '0043e44f-cf38-4def-9116-67ba3f0c256a';

SELECT 'Atualização concluída!' as status;