-- Atualizar permissões para sistema de prevenção de lesões

-- 1. Atualizar permissões para presidente (adicionar todas as permissões de injury prevention)
UPDATE club_members 
SET permissions = COALESCE(permissions, '{}'::jsonb) || jsonb_build_object(
  'medical.injury_prevention.view', true,
  'medical.injury_prevention.create', true,
  'medical.injury_prevention.edit', true,
  'medical.injury_prevention.delete', true,
  'medical.injury_prevention.analytics', true,
  'medical.injury_prevention.wellness.view', true,
  'medical.injury_prevention.wellness.create', true,
  'medical.injury_prevention.wellness.edit', true,
  'medical.injury_prevention.workload.view', true,
  'medical.injury_prevention.workload.create', true,
  'medical.injury_prevention.workload.edit', true,
  'medical.injury_prevention.alerts.view', true,
  'medical.injury_prevention.alerts.acknowledge', true
)
WHERE role = 'president';

-- 2. Atualizar permissões para admin (mesma<PERSON> do presidente)
UPDATE club_members 
SET permissions = COALESCE(permissions, '{}'::jsonb) || jsonb_build_object(
  'medical.injury_prevention.view', true,
  'medical.injury_prevention.create', true,
  'medical.injury_prevention.edit', true,
  'medical.injury_prevention.delete', true,
  'medical.injury_prevention.analytics', true,
  'medical.injury_prevention.wellness.view', true,
  'medical.injury_prevention.wellness.create', true,
  'medical.injury_prevention.wellness.edit', true,
  'medical.injury_prevention.workload.view', true,
  'medical.injury_prevention.workload.create', true,
  'medical.injury_prevention.workload.edit', true,
  'medical.injury_prevention.alerts.view', true,
  'medical.injury_prevention.alerts.acknowledge', true
)
WHERE role = 'admin';

-- 3. Atualizar permissões para médicos (permissões completas)
UPDATE club_members 
SET permissions = COALESCE(permissions, '{}'::jsonb) || jsonb_build_object(
  'medical.injury_prevention.view', true,
  'medical.injury_prevention.create', true,
  'medical.injury_prevention.edit', true,
  'medical.injury_prevention.analytics', true,
  'medical.injury_prevention.wellness.view', true,
  'medical.injury_prevention.wellness.create', true,
  'medical.injury_prevention.wellness.edit', true,
  'medical.injury_prevention.workload.view', true,
  'medical.injury_prevention.workload.create', true,
  'medical.injury_prevention.workload.edit', true,
  'medical.injury_prevention.alerts.view', true,
  'medical.injury_prevention.alerts.acknowledge', true
)
WHERE role = 'medical';

-- 4. Atualizar permissões para treinadores (visualização e criação de dados)
UPDATE club_members 
SET permissions = COALESCE(permissions, '{}'::jsonb) || jsonb_build_object(
  'medical.injury_prevention.view', true,
  'medical.injury_prevention.wellness.view', true,
  'medical.injury_prevention.workload.view', true,
  'medical.injury_prevention.workload.create', true,
  'medical.injury_prevention.workload.edit', true,
  'medical.injury_prevention.alerts.view', true
)
WHERE role = 'coach';

-- 5. Atualizar permissões para jogadores (apenas wellness próprio)
UPDATE club_members 
SET permissions = COALESCE(permissions, '{}'::jsonb) || jsonb_build_object(
  'medical.injury_prevention.wellness.create', true,
  'medical.injury_prevention.wellness.view', true
)
WHERE role = 'player';

-- 6. Verificar se as atualizações foram aplicadas
SELECT 
  role,
  COUNT(*) as total_users,
  COUNT(CASE WHEN permissions ? 'medical.injury_prevention.view' THEN 1 END) as with_injury_prevention
FROM club_members 
GROUP BY role
ORDER BY role;