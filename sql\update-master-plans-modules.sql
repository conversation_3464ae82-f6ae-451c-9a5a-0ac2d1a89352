-- =====================================================
-- SCRIPT: Atualização dos Módulos nos Planos Master
-- DESCRIÇÃO: Adiciona todos os módulos do sistema
-- VERSÃO: 1.0
-- DATA: 2025-08-01
-- =====================================================

-- Atualizar plano Básico
UPDATE master_plans 
SET modules = '{
  "dashboard": true,
  "players": true,
  "trainings": true,
  "matches": true,
  "evaluations": false,
  "medical": false,
  "finances": false,
  "administrative": false,
  "inventory": false,
  "reports": false,
  "accommodations": false,
  "meals": false,
  "billing": false,
  "monthly_fees": false,
  "callups": false
}'::jsonb
WHERE name = 'Básico';

-- Atualizar plano Profissional
UPDATE master_plans 
SET modules = '{
  "dashboard": true,
  "players": true,
  "trainings": true,
  "matches": true,
  "evaluations": true,
  "medical": true,
  "finances": true,
  "administrative": true,
  "inventory": false,
  "reports": true,
  "accommodations": true,
  "meals": true,
  "billing": true,
  "monthly_fees": true,
  "callups": true
}'::jsonb
WHERE name = 'Profissional';

-- Atualizar plano Enterprise
UPDATE master_plans 
SET modules = '{
  "dashboard": true,
  "players": true,
  "trainings": true,
  "matches": true,
  "evaluations": true,
  "medical": true,
  "finances": true,
  "administrative": true,
  "inventory": true,
  "reports": true,
  "accommodations": true,
  "meals": true,
  "billing": true,
  "monthly_fees": true,
  "callups": true
}'::jsonb
WHERE name = 'Enterprise';

-- Verificar se as atualizações foram aplicadas
SELECT name, modules FROM master_plans ORDER BY id;

RAISE NOTICE 'Módulos dos planos atualizados com sucesso!';