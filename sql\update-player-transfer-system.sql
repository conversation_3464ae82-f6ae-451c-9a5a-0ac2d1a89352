-- Script de atualização do sistema de transferências
-- <PERSON><PERSON> script atualiza as funções existentes com as novas validações

-- 1. Remover função existente para poder recriar com nova assinatura
DROP FUNCTION IF EXISTS search_player_by_cpf(TEXT);

-- 2. Recriar função de busca com informações de status
CREATE OR REPLACE FUNCTION search_player_by_cpf(p_cpf TEXT)
RETURNS TABLE (
  found BOOLEAN,
  global_player_id UUID,
  name TEXT,
  birthdate DATE,
  birthplace TEXT,
  nationality TEXT,
  rg_number TEXT,
  father_name TEXT,
  mother_name TEXT,
  phone TEXT,
  email TEXT,
  height DECIMAL,
  weight DECIMAL,
  last_club_id INTEGER,
  last_club_name TEXT,
  current_status TEXT,
  is_active_elsewhere BOOLEAN,
  active_club_name TEXT,
  documents_count INTEGER
) AS $$
BEGIN
  -- Verificar se o jogador existe
  IF NOT EXISTS (SELECT 1 FROM global_players WHERE cpf_number = p_cpf) THEN
    RETURN QUERY SELECT FALSE::BOOLEAN, NULL::UUID, NULL::TEXT, NULL::DATE, NULL::TEXT, 
                        NULL::TEXT, NULL::TEXT, NULL::TEXT, NULL::TEXT, NULL::TEXT, 
                        NULL::TEXT, NULL::DECIMAL, NULL::DECIMAL, NULL::INTEGER, 
                        NULL::TEXT, NULL::TEXT, FALSE::BOOLEAN, NULL::TEXT, 0::INTEGER;
    RETURN;
  END IF;

  -- Retornar dados do jogador
  RETURN QUERY
  SELECT 
    TRUE::BOOLEAN as found,
    gp.id as global_player_id,
    gp.name,
    gp.birthdate,
    gp.birthplace,
    gp.nationality,
    gp.rg_number,
    gp.father_name,
    gp.mother_name,
    gp.phone,
    gp.email,
    gp.height,
    gp.weight,
    last_player.club_id as last_club_id,
    ci_last.name as last_club_name,
    active_player.status as current_status,
    CASE WHEN active_player.id IS NOT NULL THEN TRUE ELSE FALSE END as is_active_elsewhere,
    ci_active.name as active_club_name,
    COALESCE(doc_count.count, 0)::INTEGER as documents_count
  FROM global_players gp
  LEFT JOIN LATERAL (
    SELECT p.club_id, p.status
    FROM players p
    WHERE p.global_player_id = gp.id
    ORDER BY p.id DESC
    LIMIT 1
  ) last_player ON TRUE
  LEFT JOIN LATERAL (
    SELECT p.id, p.club_id, p.status
    FROM players p
    WHERE p.global_player_id = gp.id
      AND p.status IN ('ativo', 'disponivel', 'emprestado')
    LIMIT 1
  ) active_player ON TRUE
  LEFT JOIN club_info ci_last ON ci_last.id = last_player.club_id
  LEFT JOIN club_info ci_active ON ci_active.id = active_player.club_id
  LEFT JOIN (
    SELECT global_player_id, COUNT(*) as count
    FROM global_player_documents
    WHERE is_active = TRUE
    GROUP BY global_player_id
  ) doc_count ON doc_count.global_player_id = gp.id
  WHERE gp.cpf_number = p_cpf;
END;
$$ LANGUAGE plpgsql;

-- 3. Atualizar função de transferência com validação melhorada
CREATE OR REPLACE FUNCTION initiate_player_transfer(
  p_cpf TEXT,
  p_to_club_id INTEGER,
  p_player_data JSONB,
  p_requested_by UUID
)
RETURNS TABLE (
  success BOOLEAN,
  player_id UUID,
  transfer_id INTEGER,
  global_player_id UUID,
  message TEXT
) AS $$
DECLARE
  v_global_player_id UUID;
  v_player_id UUID;
  v_transfer_id INTEGER;
  v_active_player RECORD;
BEGIN
  -- Verificar se jogador global existe
  SELECT id INTO v_global_player_id 
  FROM global_players 
  WHERE cpf_number = p_cpf;
  
  -- Se não existe, criar jogador global
  IF v_global_player_id IS NULL THEN
    INSERT INTO global_players (
      cpf_number, name, birthdate, birthplace, nationality,
      rg_number, father_name, mother_name, phone, email, height, weight
    ) VALUES (
      p_cpf,
      (p_player_data->>'name')::TEXT,
      (p_player_data->>'birthdate')::DATE,
      (p_player_data->>'birthplace')::TEXT,
      COALESCE((p_player_data->>'nationality')::TEXT, 'Brasil'),
      (p_player_data->>'rg_number')::TEXT,
      (p_player_data->>'father_name')::TEXT,
      (p_player_data->>'mother_name')::TEXT,
      (p_player_data->>'phone')::TEXT,
      (p_player_data->>'email')::TEXT,
      (p_player_data->>'height')::DECIMAL,
      (p_player_data->>'weight')::DECIMAL
    ) RETURNING id INTO v_global_player_id;
  END IF;
  
  -- Verificar se jogador já está ativo em outro clube
  SELECT p.id, p.club_id, p.status, ci.name as club_name
  INTO v_active_player
  FROM players p
  JOIN club_info ci ON ci.id = p.club_id
  WHERE p.global_player_id = v_global_player_id 
    AND p.status IN ('ativo', 'disponivel', 'emprestado')
    AND p.club_id != p_to_club_id;
    
  IF v_active_player.id IS NOT NULL THEN
    RETURN QUERY SELECT FALSE, NULL::UUID, NULL::INTEGER, v_global_player_id,
                        ('Jogador já está ativo no clube: ' || v_active_player.club_name || '. O clube atual deve marcar o jogador como inativo antes da transferência.')::TEXT;
    RETURN;
  END IF;
  
  -- Criar registro de transferência
  INSERT INTO player_transfers (
    global_player_id, to_club_id, requested_by, status
  ) VALUES (
    v_global_player_id, p_to_club_id, p_requested_by, 'completed'
  ) RETURNING id INTO v_transfer_id;
  
  -- Criar jogador no novo clube
  INSERT INTO players (
    id, club_id, global_player_id, is_transfer, transfer_id,
    name, position, age, number, nationality, height, weight,
    birthdate, birthplace, status, entry_date, championship_registration,
    nickname, professional_status, rg_number, cpf_number,
    father_name, mother_name, referred_by, phone, address,
    zip_code, city, state, email, contract_end_date, observation
  ) VALUES (
    gen_random_uuid(),
    p_to_club_id,
    v_global_player_id,
    TRUE,
    v_transfer_id,
    (p_player_data->>'name')::TEXT,
    (p_player_data->>'position')::TEXT,
    (p_player_data->>'age')::INTEGER,
    (p_player_data->>'number')::INTEGER,
    COALESCE((p_player_data->>'nationality')::TEXT, 'Brasil'),
    (p_player_data->>'height')::DECIMAL,
    (p_player_data->>'weight')::DECIMAL,
    (p_player_data->>'birthdate')::DATE,
    (p_player_data->>'birthplace')::TEXT,
    COALESCE((p_player_data->>'status')::TEXT, 'ativo'),
    COALESCE((p_player_data->>'entry_date')::DATE, CURRENT_DATE),
    (p_player_data->>'championship_registration')::TEXT,
    (p_player_data->>'nickname')::TEXT,
    (p_player_data->>'professional_status')::TEXT,
    (p_player_data->>'rg_number')::TEXT,
    p_cpf,
    (p_player_data->>'father_name')::TEXT,
    (p_player_data->>'mother_name')::TEXT,
    (p_player_data->>'referred_by')::TEXT,
    (p_player_data->>'phone')::TEXT,
    (p_player_data->>'address')::TEXT,
    (p_player_data->>'zip_code')::TEXT,
    (p_player_data->>'city')::TEXT,
    (p_player_data->>'state')::TEXT,
    (p_player_data->>'email')::TEXT,
    (p_player_data->>'contract_end_date')::DATE,
    (p_player_data->>'observation')::TEXT
  ) RETURNING id INTO v_player_id;
  
  -- Atualizar data de conclusão da transferência
  UPDATE player_transfers 
  SET completed_at = NOW() 
  WHERE id = v_transfer_id;
  
  RETURN QUERY SELECT TRUE, v_player_id, v_transfer_id, v_global_player_id, 'Transferência realizada com sucesso'::TEXT;
END;
$$ LANGUAGE plpgsql;

-- 4. Comentários atualizados
COMMENT ON FUNCTION search_player_by_cpf(TEXT) IS 'Busca jogador por CPF no sistema global com informações de status atual';
COMMENT ON FUNCTION initiate_player_transfer(TEXT, INTEGER, JSONB, UUID) IS 'Inicia processo de transferência com validação de jogador ativo';

-- 5. Verificar se as funções foram atualizadas corretamente
SELECT 
  routine_name,
  routine_type,
  data_type
FROM information_schema.routines 
WHERE routine_schema = 'public' 
  AND routine_name IN ('search_player_by_cpf', 'initiate_player_transfer')
ORDER BY routine_name;