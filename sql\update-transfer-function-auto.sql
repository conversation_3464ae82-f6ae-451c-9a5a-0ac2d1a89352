-- Atualizar função para registrar automaticamente novos jogadores no sistema global

CREATE OR REPLACE FUNCTION initiate_player_transfer(
  p_cpf TEXT,
  p_to_club_id INTEGER,
  p_player_data JSONB,
  p_requested_by UUID
)
RETURNS TABLE (
  success BOOLEAN,
  player_id UUID,
  transfer_id INTEGER,
  global_player_id UUID,
  message TEXT
) AS $$
DECLARE
  v_global_player_id UUID;
  v_player_id UUID;
  v_transfer_id INTEGER;
  v_active_player RECORD;
BEGIN
  -- Verificar se jogador global existe
  SELECT id INTO v_global_player_id 
  FROM global_players 
  WHERE cpf_number = p_cpf;
  
  -- Se não existe, criar jogador global AUTOMATICAMENTE
  IF v_global_player_id IS NULL THEN
    INSERT INTO global_players (
      cpf_number, name, birthdate, birthplace, nationality,
      rg_number, father_name, mother_name, phone, email, height, weight
    ) VALUES (
      p_cpf,
      (p_player_data->>'name')::TEXT,
      CASE 
        WHEN (p_player_data->>'birthdate') IS NOT NULL AND (p_player_data->>'birthdate') != '' 
        THEN (p_player_data->>'birthdate')::DATE 
        ELSE NULL 
      END,
      (p_player_data->>'birthplace')::TEXT,
      COALESCE((p_player_data->>'nationality')::TEXT, 'Brasil'),
      (p_player_data->>'rg_number')::TEXT,
      (p_player_data->>'father_name')::TEXT,
      (p_player_data->>'mother_name')::TEXT,
      (p_player_data->>'phone')::TEXT,
      (p_player_data->>'email')::TEXT,
      (p_player_data->>'height')::INTEGER,
      (p_player_data->>'weight')::INTEGER
    ) RETURNING id INTO v_global_player_id;
  END IF;
  
  -- Verificar se jogador já está ativo em outro clube
  SELECT p.id, p.club_id, p.status, ci.name as club_name
  INTO v_active_player
  FROM players p
  JOIN club_info ci ON ci.id = p.club_id
  WHERE p.global_player_id = v_global_player_id 
    AND p.status IN ('ativo', 'disponivel', 'emprestado')
    AND p.club_id != p_to_club_id;
    
  IF v_active_player.id IS NOT NULL THEN
    RETURN QUERY SELECT FALSE, NULL::UUID, NULL::INTEGER, v_global_player_id,
                        ('Jogador já está ativo no clube: ' || v_active_player.club_name || '. O clube atual deve marcar o jogador como inativo antes da transferência.')::TEXT;
    RETURN;
  END IF;
  
  -- Criar registro de transferência
  INSERT INTO player_transfers (
    global_player_id, to_club_id, requested_by, status
  ) VALUES (
    v_global_player_id, p_to_club_id, p_requested_by, 'completed'
  ) RETURNING id INTO v_transfer_id;
  
  -- Criar jogador no novo clube
  INSERT INTO players (
    id, club_id, global_player_id, is_transfer, transfer_id,
    name, position, age, number, nationality, height, weight,
    birthdate, birthplace, status, entry_date, championship_registration,
    nickname, professional_status, rg_number, cpf_number,
    father_name, mother_name, referred_by, phone, address,
    zip_code, city, state, email, contract_end_date, observation
  ) VALUES (
    gen_random_uuid(),
    p_to_club_id,
    v_global_player_id,
    TRUE,
    v_transfer_id,
    (p_player_data->>'name')::TEXT,
    (p_player_data->>'position')::TEXT,
    (p_player_data->>'age')::INTEGER,
    (p_player_data->>'number')::INTEGER,
    COALESCE((p_player_data->>'nationality')::TEXT, 'Brasil'),
    (p_player_data->>'height')::INTEGER,
    (p_player_data->>'weight')::INTEGER,
    CASE 
      WHEN (p_player_data->>'birthdate') IS NOT NULL AND (p_player_data->>'birthdate') != '' 
      THEN (p_player_data->>'birthdate')::DATE 
      ELSE NULL 
    END,
    (p_player_data->>'birthplace')::TEXT,
    COALESCE((p_player_data->>'status')::TEXT, 'ativo'),
    COALESCE((p_player_data->>'entry_date')::DATE, CURRENT_DATE),
    (p_player_data->>'championship_registration')::TEXT,
    (p_player_data->>'nickname')::TEXT,
    (p_player_data->>'professional_status')::TEXT,
    (p_player_data->>'rg_number')::TEXT,
    p_cpf,
    (p_player_data->>'father_name')::TEXT,
    (p_player_data->>'mother_name')::TEXT,
    (p_player_data->>'referred_by')::TEXT,
    (p_player_data->>'phone')::TEXT,
    (p_player_data->>'address')::TEXT,
    (p_player_data->>'zip_code')::TEXT,
    (p_player_data->>'city')::TEXT,
    (p_player_data->>'state')::TEXT,
    (p_player_data->>'email')::TEXT,
    CASE 
      WHEN (p_player_data->>'contract_end_date') IS NOT NULL AND (p_player_data->>'contract_end_date') != '' 
      THEN (p_player_data->>'contract_end_date')::DATE 
      ELSE NULL 
    END,
    (p_player_data->>'observation')::TEXT
  ) RETURNING id INTO v_player_id;
  
  -- Atualizar data de conclusão da transferência
  UPDATE player_transfers 
  SET completed_at = NOW() 
  WHERE id = v_transfer_id;
  
  RETURN QUERY SELECT TRUE, v_player_id, v_transfer_id, v_global_player_id, 'Jogador cadastrado com sucesso no sistema global'::TEXT;
END;
$$ LANGUAGE plpgsql;

SELECT 'Função atualizada! Agora todos os novos jogadores são automaticamente registrados no sistema global.' as status;