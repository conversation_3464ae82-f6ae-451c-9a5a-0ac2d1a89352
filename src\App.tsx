import { useState, useEffect } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { HelmetProvider } from "react-helmet-async";
import { Layout } from "@/components/layout/Layout";
import Dashboard from "./pages/Dashboard";
import Elenco from "./pages/Elenco";
import Partidas from "./pages/Partidas";
import JogosPassados from "./pages/JogosPassados";
import NotFound from "./pages/NotFound";
import Treinamentos from "./pages/Treinamentos";
import Agenda from "./pages/Agenda";
import BaseJuvenil from "./pages/BaseJuvenil";
import EscalacaoJogadores from "./pages/EscalacaoJogadores";
import Mapeamento from "./pages/Mapeamento";
import Medico from "./pages/Medico";
import MedicosCadastro from "./pages/MedicosCadastro";
import InjuryPrevention from "./pages/InjuryPrevention";
import PlayerWellness from "./pages/PlayerWellness";
import Financeiro from "./pages/Financeiro";
import PerfilJogador from "./pages/PerfilJogador";
import PlayerStatisticsPage from "./pages/PlayerStatisticsPage";
import PlayerMedicalHistoryPage from "./pages/PlayerMedicalHistoryPage";
import ConfiguracoesClube from "@/pages/ConfiguracoesClube";
import Login from "@/pages/Login";
// import Register from "@/pages/Register";
import AcceptInvitation from "@/pages/AcceptInvitation";
import ResetPassword from "@/pages/ResetPassword";
import UpdatePassword from "@/pages/UpdatePassword";
import EstatisticasTemporada from "@/pages/EstatisticasTemporada";
import LandingPage from "@/pages/LandingPage";
import BlogHome from "@/pages/blog/BlogHome";
import BlogPost from "@/pages/blog/BlogPost";
import Recursos from "@/pages/Recursos";
import Help from "@/pages/Help";
import { GoogleAnalytics } from "@/components/analytics/GoogleAnalytics";
import Adversarios from "@/pages/Adversarios";
import Competicoes from "@/pages/Competicoes";
import Alojamentos from "@/pages/Alojamentos";
import Alimentacao from "@/pages/Alimentacao";
import AlimentacaoSessao from "@/pages/AlimentacaoSessao";
import Categorias from "@/pages/Categorias";
import Departamentos from "@/pages/Departamentos";
import Usuarios from "@/pages/Usuarios";
import PerfilUsuario from "@/pages/PerfilUsuario";
import MeuPerfilColaborador from "@/pages/MeuPerfilColaborador";
import Relatorios from "@/pages/Relatorios";
import AuditLogs from "@/pages/AuditLogs";
import Convocacao from "@/pages/Convocacao";
import ConvocacaoDetalhes from "@/pages/ConvocacaoDetalhes";
import Administrativo from "@/pages/Administrativo";
import Estoque from "@/pages/Estoque";
import SolicitarEstoque from "@/pages/SolicitarEstoque";
import EmailTest from "@/pages/EmailTest";
import { ChatTestPage } from "@/pages/ChatTestPage";
import Avaliacao from "./pages/Avaliacao";
import MonthlyFeesPage from "./pages/MonthlyFeesPage";
import PlayerMonthlyFeePaymentPage from "./pages/PlayerMonthlyFeePaymentPage";
import Cobrancas from "./pages/Cobrancas";
import EvaluationRegistration from "./pages/EvaluationRegistration";
import EvaluationSuccess from "./pages/EvaluationSuccess";
import PaymentReceipt from "./pages/PaymentReceipt";
import PaymentReceiptSuccess from "./pages/PaymentReceiptSuccess";
import FormTemplates from "./pages/FormTemplates";
import { PLAYER_PERMISSIONS, EVALUATION_PERMISSIONS, INVENTORY_PERMISSIONS, OPPONENT_PERMISSIONS, GAME_OPERATION_PERMISSIONS } from "@/constants/permissions";
import Operacao from "@/pages/Operacao";
import { ThemeProvider } from "@/context/ThemeContext";
import { ProtectedRoute } from "@/components/ProtectedRoute";
import { PermissionGuard } from "@/components/PermissionGuard";
import { MappingPermissionGuard } from "@/components/MappingPermissionGuard";
import { PlayerProfileGuard } from "@/components/PlayerProfileGuard";
import { PublicRoute } from "@/components/PublicRoute";
import { ClubSlugWrapper } from "@/components/ClubSlugWrapper";
import { UserProvider } from "@/context/UserContext";
import { checkAndUpdateLoanedPlayers } from "@/utils/loanManager";
// Importações Master
import { Navigate } from 'react-router-dom';
import { MasterLayout } from '@/components/master/MasterLayout';
import { MasterDashboard } from '@/pages/master/Dashboard';
import { MasterLogin } from '@/pages/master/Login';
import { MasterClubs } from '@/pages/master/Clubs';
import { Plans } from '@/pages/master/Plans';
import { Billing } from '@/pages/master/Billing';
import { Analytics } from '@/pages/master/Analytics';
import { MasterAuthProvider } from '@/hooks/useMasterAuth';
import { MasterProfile } from '@/pages/master/Profile';
import { MasterSettings } from '@/pages/master/Settings';
import { Reports } from '@/pages/master/Reports';
import { Users } from '@/pages/master/Users';
import { Audit } from '@/pages/master/Audit';
import { Support } from '@/pages/master/Support';
import RedirectToClub from '@/pages/RedirectToClub';
import { ClubPreloader } from '@/components/ClubPreloader';
import { UnauthorizedAccess } from '@/components/UnauthorizedAccess';
import useClubFavicon from '@/hooks/useClubFavicon';

import "@/styles/theme.css";
import "@/styles/player-card.css";
import "@/styles/responsive.css";
import "@/styles/blog.css";

const queryClient = new QueryClient();

function getInitialClubId() {
  const clubId = localStorage.getItem("clubId");
  return clubId ? Number(clubId) : undefined;
}

function ClubProtectedRoutes() {
  const [clubId, setClubId] = useState<number | undefined>(getInitialClubId());

  useEffect(() => {
    function handleStorage() {
      setClubId(getInitialClubId());
    }
    window.addEventListener("storage", handleStorage);
    return () => window.removeEventListener("storage", handleStorage);
  }, []);

  // Verificar jogadores emprestados cujo empréstimo terminou
  useEffect(() => {
    if (clubId) {
      // Verificar uma vez ao carregar a aplicação
      checkAndUpdateLoanedPlayers(clubId).then(count => {
        if (count > 0) {
          console.log(`${count} jogador(es) retornaram automaticamente de empréstimo.`);
        }
      });

      // Configurar verificação periódica (a cada 12 horas) apenas para jogadores emprestados
      const interval = setInterval(() => {
        // Verificar jogadores emprestados
        checkAndUpdateLoanedPlayers(clubId).then(count => {
          if (count > 0) {
            console.log(`${count} jogador(es) retornaram automaticamente de empréstimo.`);
          }
        });
      }, 12 * 60 * 60 * 1000); // 12 horas em milissegundos

      return () => clearInterval(interval);
    }
  }, [clubId]);

  return (
    <ClubSlugWrapper>
      <Routes>
        <Route element={<Layout />}>
          {/* Rotas acessíveis a todos os usuários autenticados */}
          <Route path="/dashboard" element={<ProtectedRoute><Dashboard /></ProtectedRoute>} />
          <Route path="/perfil" element={
            <ProtectedRoute>
              <PermissionGuard permission="users.view">
                <PerfilUsuario />
              </PermissionGuard>
            </ProtectedRoute>
          } />
          <Route path="/meu-perfil" element={
            <ProtectedRoute>
              <PermissionGuard permission="collaborators.view_own">
                <MeuPerfilColaborador />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          {/* Rotas que exigem permissões específicas */}
          <Route path="/elenco" element={
            <ProtectedRoute>
              <PermissionGuard permission="players.view">
                <Elenco />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/partidas" element={
            <ProtectedRoute>
              <PermissionGuard permission="matches.view">
                <Partidas />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/jogos-passados" element={
            <ProtectedRoute>
              <PermissionGuard permission="matches.view">
                <JogosPassados />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/adversarios" element={
            <ProtectedRoute>
              <PermissionGuard permission={OPPONENT_PERMISSIONS.VIEW}>
                <Adversarios />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/competicoes" element={
            <ProtectedRoute>
              <PermissionGuard permission="competitions.view">
                <Competicoes />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/alojamentos" element={
            <ProtectedRoute>
              <PermissionGuard permission="accommodations.view">
                <Alojamentos />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/alimentacao" element={
            <ProtectedRoute>
              <PermissionGuard permission="accommodations.view">
                <Alimentacao />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/alimentacao/sessao/:sessionId" element={
            <ProtectedRoute>
              <PermissionGuard permission="accommodations.view">
                <AlimentacaoSessao />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/treinamentos" element={
            <ProtectedRoute>
              <PermissionGuard permission="trainings.view">
                <Treinamentos />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/agenda" element={
            <ProtectedRoute>
              <PermissionGuard permission="agenda.view">
                <Agenda />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/categorias" element={
            <ProtectedRoute>
              <PermissionGuard permission="categories.view">
                <Categorias />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/departamentos" element={
            <ProtectedRoute>
              <PermissionGuard permission="departments.view">
                <Departamentos />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/usuarios" element={
            <ProtectedRoute>
              <PermissionGuard permission="users.view">
                <Usuarios />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/escalacao" element={
            <ProtectedRoute>
              <PermissionGuard permission="matches.lineup">
                <EscalacaoJogadores />
              </PermissionGuard>
            </ProtectedRoute>
          } />
          <Route path="/mapeamento" element={
            <ProtectedRoute>
              <MappingPermissionGuard>
                <Mapeamento />
              </MappingPermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/medico" element={
            <ProtectedRoute>
              <PermissionGuard permission="medical.view">
                <Medico />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/medico/prevencao-lesoes" element={
            <ProtectedRoute>
              <PermissionGuard permission="medical.injury_prevention.view">
                <InjuryPrevention />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/wellness" element={
            <ProtectedRoute>
              <PlayerWellness />
            </ProtectedRoute>
          } />

          <Route path="/medicos/cadastro" element={
            <ProtectedRoute>
              <PermissionGuard permissions={["medical_professionals.create", "medical_professionals.edit_own"]}>
                <MedicosCadastro />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/medicos/editar/:id" element={
            <ProtectedRoute>
              <PermissionGuard permissions={["medical_professionals.edit", "medical_professionals.edit_own"]}>
                <MedicosCadastro />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/financeiro" element={
            <ProtectedRoute>
              <PermissionGuard permission="finances.view">
                <Financeiro />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/cobrancas" element={
            <ProtectedRoute>
              <PermissionGuard permission="finances.view">
                <Cobrancas />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/mensalidades" element={
            <ProtectedRoute>
              <PermissionGuard permission="finances.view">
                <MonthlyFeesPage />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/player/monthly-fees/:monthlyFeeId" element={
            <ProtectedRoute>
              <PlayerMonthlyFeePaymentPage />
            </ProtectedRoute>
          } />

          <Route path="/jogador/:id" element={
            <ProtectedRoute>
              <PlayerProfileGuard>
                <PerfilJogador />
              </PlayerProfileGuard>
            </ProtectedRoute>
          } />

          <Route path="/jogador/:id/estatisticas" element={
            <ProtectedRoute>
              <PlayerProfileGuard>
                <PlayerStatisticsPage />
              </PlayerProfileGuard>
            </ProtectedRoute>
          } />

          <Route path="/jogador/:id/historico-medico" element={
            <ProtectedRoute>
              <PlayerProfileGuard>
                <PlayerMedicalHistoryPage />
              </PlayerProfileGuard>
            </ProtectedRoute>
          } />

          <Route path="/estatisticas" element={
            <ProtectedRoute>
              <PermissionGuard permission="statistics.view">
                <EstatisticasTemporada />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/relatorios" element={
            <ProtectedRoute>
              <PermissionGuard permission="reports.view">
                <Relatorios />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/analytics" element={
            <ProtectedRoute>
              <PermissionGuard permission="analytics.view">
                <ComingSoon title="Analytics" />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/comunicacao" element={
            <ProtectedRoute>
              <PermissionGuard permission="communication.view">
                <ComingSoon title="Comunicação" />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/configuracoes" element={
            <ProtectedRoute>
              <PermissionGuard permission="settings.view">
                <ConfiguracoesClube />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/audit-logs" element={
            <ProtectedRoute>
              <PermissionGuard permission="audit_logs.view">
                <AuditLogs />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/administrativo" element={
            <ProtectedRoute>
              <PermissionGuard permission="administrative.view">
                <Administrativo />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/estoque" element={
            <ProtectedRoute>
              <PermissionGuard permission="inventory.view">
                <Estoque />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/solicitar-estoque" element={
            <ProtectedRoute>
              <PermissionGuard permission={INVENTORY_PERMISSIONS.REQUESTS.CREATE}>
                <SolicitarEstoque />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/test-email" element={
            <ProtectedRoute>
              <EmailTest />
            </ProtectedRoute>
          } />

          <Route path="/test-chat" element={
            <ProtectedRoute>
              <ChatTestPage />
            </ProtectedRoute>
          } />

          <Route path="/convocacao" element={
            <ProtectedRoute>
              <PermissionGuard permission="callups.view">
                <Convocacao />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/operacao" element={
            <ProtectedRoute>
              <PermissionGuard permission={GAME_OPERATION_PERMISSIONS.VIEW}>
                <Operacao />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/convocacao/:id" element={
            <ProtectedRoute>
              <PermissionGuard permission="callups.view">
                <ConvocacaoDetalhes />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/avaliacao" element={
            <ProtectedRoute>
              <PermissionGuard
                permissions={[
                  EVALUATION_PERMISSIONS.TABS.PLAYERS,
                  EVALUATION_PERMISSIONS.TABS.INVITATIONS,
                  EVALUATION_PERMISSIONS.TABS.NEW_INVITATION,
                  EVALUATION_PERMISSIONS.TABS.DASHBOARD,
                ]}
              >
                <Avaliacao />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/fichas" element={
            <ProtectedRoute>
              <PermissionGuard permission="form_templates.view">
                <FormTemplates />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/alimentacao" element={
            <ProtectedRoute>
              <PermissionGuard permission="meals.view">
                <Alimentacao />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="/alimentacao-sessao/:sessionId" element={
            <ProtectedRoute>
              <PermissionGuard permission="meals.view">
                <AlimentacaoSessao />
              </PermissionGuard>
            </ProtectedRoute>
          } />

          <Route path="*" element={<NotFound />} />
        </Route>
      </Routes>
    </ClubSlugWrapper>
  );
}

function App() {
  // Verificar e limpar dados de autenticação inválidos ao carregar a aplicação
  useEffect(() => {
    const token = localStorage.getItem("token");

    // Se não há token, mas há outros dados de autenticação, limpar tudo
    if (!token && (localStorage.getItem("userId") || localStorage.getItem("clubId"))) {
      localStorage.removeItem("userId");
      localStorage.removeItem("clubId");
      localStorage.removeItem("selectedCategoryId");
    }

    // Se há token, verificar se é válido
    if (token) {
      try {
        // Decodificar o token (formato: header.payload.signature)
        const payload = JSON.parse(atob(token.split('.')[1]));

        // Verificar se o token expirou
        const expirationTime = payload.exp * 1000; // Converter para milissegundos
        const currentTime = Date.now();

        if (currentTime > expirationTime) {
          // Token expirou, limpar localStorage
          localStorage.removeItem("token");
          localStorage.removeItem("userId");
          localStorage.removeItem("clubId");
          localStorage.removeItem("selectedCategoryId");
        }
      } catch (error) {
        // Token inválido, limpar localStorage
        localStorage.removeItem("token");
        localStorage.removeItem("userId");
        localStorage.removeItem("clubId");
        localStorage.removeItem("selectedCategoryId");
      }
    }
  }, []);

  useClubFavicon();

  return (
    <HelmetProvider>
      <UserProvider>
        <QueryClientProvider client={queryClient}>
          <ThemeProvider>
            <TooltipProvider>
              <Toaster />
              <Sonner />
              <BrowserRouter>
                <ClubPreloader />
                <GoogleAnalytics measurementId="G-C6XNFXQXCC" />
              <Routes>
                {/* Rotas públicas sem Layout/ClubProvider */}
                <Route
                  path="/"
                  element={<LandingPage />}
                />
                
                {/* Blog Routes */}
                <Route
                  path="/blog"
                  element={<BlogHome />}
                />
                <Route
                  path="/blog/:slug"
                  element={<BlogPost />}
                />
                
                {/* Resources */}
                <Route
                  path="/recursos"
                  element={<Recursos />}
                />

                {/* Help */}
                <Route
                  path="/ajuda"
                  element={<Help />}
                />
                <Route
                  path="/login"
                  element={
                    <PublicRoute>
                      <Login />
                    </PublicRoute>
                  }
                />
                {/*
                <Route
                  path="/register"
                  element={
                    <PublicRoute>
                      <Register />
                    </PublicRoute>
                  }
                /> */}
                <Route
                  path="/accept-invitation"
                  element={
                    <PublicRoute>
                      <AcceptInvitation />
                    </PublicRoute>
                  }
                />
                 <Route
                  path="/reset-password"
                  element={
                    <PublicRoute>
                      <ResetPassword />
                    </PublicRoute>
                  }
                />
                <Route path="/update-password" element={<UpdatePassword />} />
                <Route
                  path="/evaluation-registration"
                  element={
                    <PublicRoute>
                      <EvaluationRegistration />
                    </PublicRoute>
                  }
                />
                <Route
                  path="/evaluation-success"
                  element={
                    <PublicRoute>
                      <EvaluationSuccess />
                    </PublicRoute>
                  }
                />
                <Route
                  path="/payment-receipt"
                  element={
                    <PublicRoute>
                      <PaymentReceipt />
                    </PublicRoute>
                  }
                />
                <Route
                  path="/payment-receipt-success"
                  element={
                    <PublicRoute>
                      <PaymentReceiptSuccess />
                    </PublicRoute>
                  }
                />
                <Route
                  path="/email-test"
                  element={<EmailTest />}
                />
                
                {/* Rota Master Login - Fora do provider */}
                <Route path="/master/login" element={<MasterLogin />} />

                {/* Rotas Master Autenticadas - Dentro do provider */}
                <Route path="/master/*" element={
                  <MasterAuthProvider>
                    <MasterLayout>
                      <Routes>
                        <Route path="dashboard" element={<MasterDashboard />} />
                        <Route path="clubs" element={<MasterClubs />} />
                        <Route path="plans" element={<Plans />} />
                        <Route path="billing" element={<Billing />} />
                        <Route path="analytics" element={<Analytics />} />
                        <Route path="reports" element={<Reports />} />
                        <Route path="users" element={<Users />} />
                        <Route path="audit" element={<Audit />} />
                        <Route path="support" element={<Support />} />
                        <Route path="profile" element={<MasterProfile />} />
                        <Route path="settings" element={<MasterSettings />} />
                        <Route path="/" element={<Navigate to="/master/dashboard" replace />} />
                      </Routes>
                    </MasterLayout>
                  </MasterAuthProvider>
                } />
                
                {/* Rotas privadas multi-clube com slug */}
                <Route path="/:clubSlug/*" element={<ClubProtectedRoutes />} />
                
                {/* Rota de redirecionamento para usuários autenticados */}
                <Route path="/redirect-to-club" element={<RedirectToClub />} />
                
                {/* Rota de acesso não autorizado */}
                <Route path="/unauthorized" element={<UnauthorizedAccess />} />
                
                {/* Fallback para URLs antigas sem slug */}
                <Route path="/dashboard" element={<RedirectToClub />} />
                <Route path="/elenco" element={<RedirectToClub />} />
                <Route path="/partidas" element={<RedirectToClub />} />
                <Route path="/configuracoes" element={<RedirectToClub />} />
              </Routes>
              </BrowserRouter>
            </TooltipProvider>
          </ThemeProvider>
        </QueryClientProvider>
      </UserProvider>
    </HelmetProvider>
  );
}

function ComingSoon({ title }: { title: string }) {
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="bg-white rounded-lg shadow-xl p-8 max-w-md w-full text-center">
        <h2 className="text-2xl font-bold mb-4 text-team-blue">{title} em breve!</h2>
        <p className="mb-4">Estamos trabalhando para trazer essa funcionalidade para você.</p>
      </div>
    </div>
  );
}

export default App;