import { supabase } from "@/integrations/supabase/client";
import { v4 as uuidv4 } from "uuid";

/**
 * Faz upload de um arquivo de ficha para o Supabase Storage
 * @param clubId ID do clube
 * @param file Arquivo da ficha (PDF, Word, Excel, etc.)
 * @param templateName Nome do template para organização
 * @returns URL pública do arquivo
 */
export async function uploadFormTemplateFile(
  clubId: string | number,
  file: File,
  templateName: string
): Promise<string> {
  try {
    console.log("Uploading form template file:", file.name, file.type, file.size);

    // Limitar tamanho do arquivo (10MB para documentos)
    const MAX_SIZE = 10 * 1024 * 1024; // 10MB
    if (file.size > MAX_SIZE) {
      throw new Error("O arquivo deve ter no máximo 10MB");
    }

    // Validar tipos de arquivo aceitos
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/plain'
    ];

    const allowedExtensions = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.txt'];

    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();

    if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {
      throw new Error("Tipo de arquivo não suportado. Formatos aceitos: PDF, Word (.doc, .docx), Excel (.xls, .xlsx), TXT");
    }

    // Gerar nome único para o arquivo
    const fileExt = file.name.split('.').pop() || 'pdf';
    const sanitizedTemplateName = templateName.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase();
    const fileName = `${sanitizedTemplateName}-${uuidv4()}.${fileExt}`;
    const filePath = `form-templates/club-${clubId}/${fileName}`;

    // Fazer upload do arquivo
    const { error: uploadError } = await supabase.storage
      .from('playerdocuments') // Usar bucket existente de documentos de jogadores
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: true,
      });

    if (uploadError) {
      throw new Error(`Erro ao fazer upload da ficha: ${uploadError.message}`);
    }

    // Obter URL pública do arquivo
    const { data: urlData } = supabase.storage
      .from('playerdocuments')
      .getPublicUrl(filePath);

    console.log('Ficha enviada com sucesso:', urlData.publicUrl);
    return urlData.publicUrl;
  } catch (error) {
    console.error('Erro no upload da ficha:', error);
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido ao fazer upload da ficha';
    throw new Error(errorMessage);
  }
}

/**
 * Remove um arquivo de ficha do Supabase Storage
 * @param fileUrl URL do arquivo a ser removido
 * @returns boolean indicando sucesso
 */
export async function deleteFormTemplateFile(fileUrl: string): Promise<boolean> {
  try {
    // Extrair o caminho do arquivo da URL
    const url = new URL(fileUrl);
    const pathParts = url.pathname.split('/');
    const bucketIndex = pathParts.findIndex(part => part === 'playerdocuments');

    if (bucketIndex === -1) {
      throw new Error('URL de arquivo inválida');
    }

    const filePath = pathParts.slice(bucketIndex + 1).join('/');

    const { error } = await supabase.storage
      .from('playerdocuments')
      .remove([filePath]);

    if (error) {
      console.error('Erro ao remover arquivo:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Erro ao remover arquivo da ficha:', error);
    return false;
  }
}