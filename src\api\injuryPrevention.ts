import { supabase } from "@/integrations/supabase/client";
import { withPermission, withAuditLog } from "./middleware";

// Tipos para o sistema de prevenção de lesões
export interface WorkloadData {
  id?: number;
  club_id: number;
  player_id: string;
  date: string;
  training_duration?: number;
  training_intensity?: number;
  training_type?: string;
  distance_covered?: number;
  sprint_count?: number;
  max_speed?: number;
  heart_rate_avg?: number;
  heart_rate_max?: number;
  perceived_exertion?: number;
  sleep_quality?: number;
  stress_level?: number;
  muscle_soreness?: number;
  training_load?: number;
  acute_chronic_ratio?: number;
}

export interface WellnessData {
  id?: number;
  club_id: number;
  player_id: string;
  date: string;
  sleep_hours?: number;
  sleep_quality?: number;
  fatigue_level?: number;
  muscle_soreness?: number;
  stress_level?: number;
  mood?: number;
  weight?: number;
  resting_heart_rate?: number;
  notes?: string;
}

export interface InjuryHistory {
  id?: number;
  club_id: number;
  player_id: string;
  injury_date: string;
  injury_type: string;
  body_part: string;
  severity: string;
  mechanism?: string;
  days_out?: number;
  return_date?: string;
  occurred_during?: string;
  field_position?: string;
  weather_conditions?: string;
  previous_injury?: boolean;
  fatigue_level?: number;
  workload_week?: number;
}

export interface RiskFactors {
  id?: number;
  club_id: number;
  player_id: string;
  calculated_date: string;
  overall_risk_score: number;
  muscular_risk_score: number;
  joint_risk_score: number;
  overload_risk_score: number;
  acute_chronic_ratio: number;
  training_monotony: number;
  wellness_score: number;
  previous_injury_factor: number;
  risk_level: 'baixo' | 'moderado' | 'alto' | 'crítico';
  recommendations: any;
}

// Permissões
const INJURY_PREVENTION_PERMISSIONS = {
  VIEW: "medical.injury_prevention.view",
  CREATE: "medical.injury_prevention.create",
  EDIT: "medical.injury_prevention.edit",
  DELETE: "medical.injury_prevention.delete",
  VIEW_ANALYTICS: "medical.injury_prevention.analytics",
  WELLNESS: {
    VIEW: "medical.injury_prevention.wellness.view",
    CREATE: "medical.injury_prevention.wellness.create",
    EDIT: "medical.injury_prevention.wellness.edit"
  }
};

/**
 * Registra dados de carga de trabalho
 */
export async function createWorkloadData(
  clubId: number,
  userId: string,
  data: Omit<WorkloadData, 'id' | 'club_id'>
): Promise<WorkloadData> {
  return withPermission(
    clubId,
    userId,
    INJURY_PREVENTION_PERMISSIONS.CREATE,
    () => {
      return withAuditLog(
        clubId,
        userId,
        "injury_prevention.workload.create",
        { player_id: data.player_id, date: data.date },
        async () => {
          // Calcular training_load se não fornecido
          const training_load = data.training_load || 
            (data.training_intensity && data.training_duration ? 
              data.training_intensity * data.training_duration : null);

          const { data: result, error } = await supabase
            .from("player_workload_data")
            .insert({
              club_id: clubId,
              ...data,
              training_load
            })
            .select()
            .single();

          if (error) throw error;
          return result;
        }
      );
    }
  );
}

/**
 * Registra dados de wellness diário
 */
export async function createWellnessData(
  clubId: number,
  userId: string,
  data: Omit<WellnessData, 'id' | 'club_id'>
): Promise<WellnessData> {
  return withPermission(
    clubId,
    userId,
    INJURY_PREVENTION_PERMISSIONS.WELLNESS.CREATE,
    () => {
      return withAuditLog(
        clubId,
        userId,
        "injury_prevention.wellness.create",
        { player_id: data.player_id, date: data.date },
        async () => {
          const { data: result, error } = await supabase
            .from("player_wellness_data")
            .upsert({
              club_id: clubId,
              ...data
            })
            .select()
            .single();

          if (error) throw error;
          return result;
        }
      );
    }
  );
}

/**
 * Registra dados de wellness para o próprio jogador (sem verificação de permissão)
 */
export async function createPlayerOwnWellnessData(
  clubId: number,
  userId: string,
  playerId: string,
  data: Omit<WellnessData, 'id' | 'club_id' | 'player_id'>
): Promise<WellnessData> {
  // Verificar se o usuário é realmente o jogador
  const { data: playerCheck, error: playerError } = await supabase
    .from("players")
    .select("id, user_id")
    .eq("id", playerId)
    .eq("club_id", clubId)
    .eq("user_id", userId)
    .single();

  if (playerError || !playerCheck) {
    throw new Error("Você só pode registrar seus próprios dados de wellness");
  }

  return withAuditLog(
    clubId,
    userId,
    "injury_prevention.wellness.create_own",
    { player_id: playerId, date: data.date },
    async () => {
      const { data: result, error } = await supabase
        .from("player_wellness_data")
        .upsert({
          club_id: clubId,
          player_id: playerId,
          ...data
        })
        .select()
        .single();

      if (error) throw error;
      return result;
    }
  );
}

/**
 * Registra histórico de lesão
 */
export async function createInjuryHistory(
  clubId: number,
  userId: string,
  data: Omit<InjuryHistory, 'id' | 'club_id'>
): Promise<InjuryHistory> {
  return withPermission(
    clubId,
    userId,
    INJURY_PREVENTION_PERMISSIONS.CREATE,
    () => {
      return withAuditLog(
        clubId,
        userId,
        "injury_prevention.injury.create",
        { player_id: data.player_id, injury_type: data.injury_type },
        async () => {
          const { data: result, error } = await supabase
            .from("injury_history")
            .insert({
              club_id: clubId,
              ...data
            })
            .select()
            .single();

          if (error) throw error;
          return result;
        }
      );
    }
  );
}

/**
 * Calcula fatores de risco usando algoritmos de ML
 */
export async function calculateRiskFactors(
  clubId: number,
  playerId: string,
  date: string = new Date().toISOString().split('T')[0]
): Promise<RiskFactors> {
  // Buscar dados históricos do jogador
  const [workloadData, wellnessData, injuryData] = await Promise.all([
    getPlayerWorkloadHistory(clubId, playerId, 28), // últimos 28 dias
    getPlayerWellnessHistory(clubId, playerId, 7),   // últimos 7 dias
    getPlayerInjuryHistory(clubId, playerId)
  ]);

  // Algoritmos de cálculo de risco
  const riskFactors = await calculateMLRiskScores({
    workloadData,
    wellnessData,
    injuryData,
    playerId,
    date
  });

  // Primeiro, tentar deletar registro existente para evitar conflito
  await supabase
    .from("injury_risk_factors")
    .delete()
    .eq("club_id", clubId)
    .eq("player_id", playerId)
    .eq("calculated_date", date);

  // Inserir novo registro
  const { data: result, error } = await supabase
    .from("injury_risk_factors")
    .insert({
      club_id: clubId,
      player_id: playerId,
      calculated_date: date,
      ...riskFactors
    })
    .select()
    .single();

  if (error) throw error;
  return result;
}

/**
 * Algoritmos de Machine Learning para cálculo de risco
 */
async function calculateMLRiskScores(data: {
  workloadData: WorkloadData[];
  wellnessData: WellnessData[];
  injuryData: InjuryHistory[];
  playerId: string;
  date: string;
}): Promise<Omit<RiskFactors, 'id' | 'club_id' | 'player_id' | 'calculated_date'>> {
  const { workloadData, wellnessData, injuryData } = data;

  // 1. Calcular Acute:Chronic Workload Ratio (ACWR)
  const acuteLoad = calculateAcuteLoad(workloadData, 7);  // última semana
  const chronicLoad = calculateChronicLoad(workloadData, 28); // últimas 4 semanas
  const acute_chronic_ratio = chronicLoad > 0 ? acuteLoad / chronicLoad : 0;

  // 2. Calcular Training Monotony
  const training_monotony = calculateTrainingMonotony(workloadData);

  // 3. Calcular Wellness Score
  const wellness_score = calculateWellnessScore(wellnessData);

  // 4. Fator de lesão prévia
  const previous_injury_factor = calculatePreviousInjuryFactor(injuryData);

  // 5. Scores específicos de risco
  const muscular_risk_score = calculateMuscularRisk(acute_chronic_ratio, wellness_score, previous_injury_factor);
  const joint_risk_score = calculateJointRisk(workloadData, injuryData);
  const overload_risk_score = calculateOverloadRisk(acute_chronic_ratio, training_monotony);

  // 6. Score geral (média ponderada)
  const overall_risk_score = (
    muscular_risk_score * 0.4 +
    joint_risk_score * 0.3 +
    overload_risk_score * 0.3
  );

  // 7. Determinar nível de risco
  const risk_level = determineRiskLevel(overall_risk_score);

  // 8. Gerar recomendações
  const recommendations = generateRecommendations({
    overall_risk_score,
    acute_chronic_ratio,
    wellness_score,
    risk_level
  });

  return {
    overall_risk_score: Math.round(overall_risk_score * 100) / 100,
    muscular_risk_score: Math.round(muscular_risk_score * 100) / 100,
    joint_risk_score: Math.round(joint_risk_score * 100) / 100,
    overload_risk_score: Math.round(overload_risk_score * 100) / 100,
    acute_chronic_ratio: Math.round(acute_chronic_ratio * 100) / 100,
    training_monotony: Math.round(training_monotony * 100) / 100,
    wellness_score: Math.round(wellness_score * 100) / 100,
    previous_injury_factor: Math.round(previous_injury_factor * 100) / 100,
    risk_level,
    recommendations
  };
}

// Funções auxiliares para cálculos
function calculateAcuteLoad(workloadData: WorkloadData[], days: number): number {
  const recentData = workloadData.slice(-days);
  const totalLoad = recentData.reduce((sum, data) => sum + (data.training_load || 0), 0);
  return totalLoad / days;
}

function calculateChronicLoad(workloadData: WorkloadData[], days: number): number {
  const totalLoad = workloadData.reduce((sum, data) => sum + (data.training_load || 0), 0);
  return totalLoad / days;
}

function calculateTrainingMonotony(workloadData: WorkloadData[]): number {
  const loads = workloadData.map(d => d.training_load || 0);
  const mean = loads.reduce((a, b) => a + b, 0) / loads.length;
  const variance = loads.reduce((sum, load) => sum + Math.pow(load - mean, 2), 0) / loads.length;
  const stdDev = Math.sqrt(variance);
  return stdDev > 0 ? mean / stdDev : 0;
}

function calculateWellnessScore(wellnessData: WellnessData[]): number {
  if (wellnessData.length === 0) return 5; // neutro
  
  const latest = wellnessData[wellnessData.length - 1];
  const scores = [
    latest.sleep_quality || 5,
    10 - (latest.fatigue_level || 5), // inverter fadiga
    10 - (latest.muscle_soreness || 5), // inverter dor
    10 - (latest.stress_level || 5), // inverter stress
    latest.mood || 5
  ];
  
  return scores.reduce((a, b) => a + b, 0) / scores.length;
}

function calculatePreviousInjuryFactor(injuryData: InjuryHistory[]): number {
  const recentInjuries = injuryData.filter(injury => {
    const injuryDate = new Date(injury.injury_date);
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
    return injuryDate > sixMonthsAgo;
  });
  
  return Math.min(recentInjuries.length * 2, 10); // máximo 10
}

function calculateMuscularRisk(acwr: number, wellness: number, previousInjury: number): number {
  let risk = 0;
  
  // ACWR ideal é entre 0.8 e 1.3
  if (acwr > 1.5) risk += 30;
  else if (acwr > 1.3) risk += 15;
  else if (acwr < 0.5) risk += 20;
  
  // Wellness baixo aumenta risco
  if (wellness < 4) risk += 25;
  else if (wellness < 6) risk += 10;
  
  // Lesão prévia
  risk += previousInjury * 3;
  
  return Math.min(risk, 100);
}

function calculateJointRisk(workloadData: WorkloadData[], injuryData: InjuryHistory[]): number {
  // Baseado em sprints e lesões articulares prévias
  const recentSprints = workloadData.slice(-7).reduce((sum, data) => sum + (data.sprint_count || 0), 0);
  const jointInjuries = injuryData.filter(injury => 
    ['joelho', 'tornozelo', 'quadril'].includes(injury.body_part.toLowerCase())
  ).length;
  
  let risk = 0;
  if (recentSprints > 50) risk += 20;
  risk += jointInjuries * 15;
  
  return Math.min(risk, 100);
}

function calculateOverloadRisk(acwr: number, monotony: number): number {
  let risk = 0;
  
  if (acwr > 1.5) risk += 40;
  if (monotony < 2) risk += 30; // muito pouca variação
  if (monotony > 8) risk += 20; // muita variação
  
  return Math.min(risk, 100);
}

function determineRiskLevel(score: number): 'baixo' | 'moderado' | 'alto' | 'crítico' {
  if (score < 25) return 'baixo';
  if (score < 50) return 'moderado';
  if (score < 75) return 'alto';
  return 'crítico';
}

function generateRecommendations(data: {
  overall_risk_score: number;
  acute_chronic_ratio: number;
  wellness_score: number;
  risk_level: string;
}): any {
  const recommendations = [];
  
  if (data.acute_chronic_ratio > 1.3) {
    recommendations.push({
      type: 'workload',
      priority: 'high',
      message: 'Reduzir carga de treino em 20-30%',
      action: 'reduce_intensity'
    });
  }
  
  if (data.wellness_score < 5) {
    recommendations.push({
      type: 'recovery',
      priority: 'high',
      message: 'Focar em recuperação e descanso',
      action: 'increase_recovery'
    });
  }
  
  if (data.risk_level === 'crítico') {
    recommendations.push({
      type: 'medical',
      priority: 'critical',
      message: 'Avaliação médica urgente recomendada',
      action: 'medical_evaluation'
    });
  }
  
  return recommendations;
}

// Funções auxiliares para buscar dados
async function getPlayerWorkloadHistory(clubId: number, playerId: string, days: number): Promise<WorkloadData[]> {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  const { data, error } = await supabase
    .from("player_workload_data")
    .select("*")
    .eq("club_id", clubId)
    .eq("player_id", playerId)
    .gte("date", startDate.toISOString().split('T')[0])
    .order("date", { ascending: true });
  
  if (error) throw error;
  return data || [];
}

async function getPlayerWellnessHistory(clubId: number, playerId: string, days: number): Promise<WellnessData[]> {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  const { data, error } = await supabase
    .from("player_wellness_data")
    .select("*")
    .eq("club_id", clubId)
    .eq("player_id", playerId)
    .gte("date", startDate.toISOString().split('T')[0])
    .order("date", { ascending: true });
  
  if (error) throw error;
  return data || [];
}

async function getPlayerInjuryHistory(clubId: number, playerId: string): Promise<InjuryHistory[]> {
  const { data, error } = await supabase
    .from("injury_history")
    .select("*")
    .eq("club_id", clubId)
    .eq("player_id", playerId)
    .order("injury_date", { ascending: false });
  
  if (error) throw error;
  return data || [];
}

/**
 * Busca fatores de risco atuais de um jogador
 */
export async function getPlayerRiskFactors(
  clubId: number,
  userId: string,
  playerId: string
): Promise<RiskFactors | null> {
  return withPermission(
    clubId,
    userId,
    INJURY_PREVENTION_PERMISSIONS.VIEW,
    async () => {
      const today = new Date().toISOString().split('T')[0];
      
      const { data, error } = await supabase
        .from("injury_risk_factors")
        .select("*")
        .eq("club_id", clubId)
        .eq("player_id", playerId)
        .eq("calculated_date", today)
        .single();
      
      if (error && error.code !== 'PGRST116') throw error;
      return data;
    }
  );
}

/**
 * Busca jogadores com alto risco de lesão
 */
export async function getHighRiskPlayers(
  clubId: number,
  userId: string,
  riskThreshold: number = 50
): Promise<(RiskFactors & { player_name: string })[]> {
  return withPermission(
    clubId,
    userId,
    INJURY_PREVENTION_PERMISSIONS.VIEW_ANALYTICS,
    async () => {
      const today = new Date().toISOString().split('T')[0];
      
      const { data, error } = await supabase
        .from("injury_risk_factors")
        .select(`
          *,
          players!inner(name)
        `)
        .eq("club_id", clubId)
        .eq("calculated_date", today)
        .gte("overall_risk_score", riskThreshold)
        .order("overall_risk_score", { ascending: false });
      
      if (error) throw error;
      
      return (data || []).map(item => ({
        ...item,
        player_name: item.players.name
      }));
    }
  );
}

/**
 * Recalcula riscos para todos os jogadores do clube
 */
export async function recalculateAllRisks(
  clubId: number,
  userId: string
): Promise<{ success: number; errors: number; details: any[] }> {
  return withPermission(
    clubId,
    userId,
    INJURY_PREVENTION_PERMISSIONS.VIEW_ANALYTICS,
    async () => {
      // Buscar todos os jogadores ativos do clube
      const { data: players, error: playersError } = await supabase
        .from("players")
        .select("id, name")
        .eq("club_id", clubId)
        .eq("status", "active");

      if (playersError) throw playersError;

      const results = {
        success: 0,
        errors: 0,
        details: [] as any[]
      };

      // Processar jogadores em lotes para evitar sobrecarga
      const batchSize = 5;
      for (let i = 0; i < players.length; i += batchSize) {
        const batch = players.slice(i, i + batchSize);
        
        await Promise.allSettled(
          batch.map(async (player) => {
            try {
              await calculateRiskFactors(clubId, player.id);
              results.success++;
              results.details.push({
                player_id: player.id,
                player_name: player.name,
                status: 'success'
              });
            } catch (error) {
              results.errors++;
              results.details.push({
                player_id: player.id,
                player_name: player.name,
                status: 'error',
                error: error.message
              });
            }
          })
        );

        // Pequena pausa entre lotes
        if (i + batchSize < players.length) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      return results;
    }
  );
}

/**
 * Busca estatísticas do dashboard de prevenção
 */
export async function getInjuryPreventionStats(
  clubId: number,
  userId: string
): Promise<{
  totalPlayers: number;
  criticalRisk: number;
  highRisk: number;
  moderateRisk: number;
  lowRisk: number;
  riskDistribution: Array<{ risk_level: string; count: number }>;
}> {
  return withPermission(
    clubId,
    userId,
    INJURY_PREVENTION_PERMISSIONS.VIEW,
    async () => {
      const today = new Date().toISOString().split('T')[0];
      
      // Buscar todos os fatores de risco do dia
      const { data: riskData, error } = await supabase
        .from("injury_risk_factors")
        .select("risk_level")
        .eq("club_id", clubId)
        .eq("calculated_date", today);

      if (error) throw error;

      const stats = {
        totalPlayers: riskData?.length || 0,
        criticalRisk: 0,
        highRisk: 0,
        moderateRisk: 0,
        lowRisk: 0,
        riskDistribution: [] as Array<{ risk_level: string; count: number }>
      };

      // Contar por nível de risco
      const riskCounts = (riskData || []).reduce((acc, item) => {
        acc[item.risk_level] = (acc[item.risk_level] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      stats.criticalRisk = riskCounts['crítico'] || 0;
      stats.highRisk = riskCounts['alto'] || 0;
      stats.moderateRisk = riskCounts['moderado'] || 0;
      stats.lowRisk = riskCounts['baixo'] || 0;

      stats.riskDistribution = Object.entries(riskCounts).map(([risk_level, count]) => ({
        risk_level,
        count
      }));

      return stats;
    }
  );
}

export { INJURY_PREVENTION_PERMISSIONS };
