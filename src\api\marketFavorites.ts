import { supabase } from "@/integrations/supabase/client";
import type { MarketProfile, MarketProfileFilters } from "./marketProfiles";

// =====================================================
// TIPOS E INTERFACES
// =====================================================

export interface SavedSearch {
  id: string;
  user_id: string;
  search_name: string;
  search_criteria: MarketProfileFilters;
  created_at: string;
}

export interface Favorite {
  id: string;
  user_id: string;
  profile_id: string;
  created_at: string;
  profile?: MarketProfile;
}

export interface ProfileView {
  id: string;
  profile_id: string;
  viewer_user_id?: string;
  viewer_ip_address?: string;
  created_at: string;
}

// =====================================================
// FUNÇÕES DE AUTENTICAÇÃO
// =====================================================

async function requireAuth() {
  const { data: { session } } = await supabase.auth.getSession();
  if (!session) {
    throw new Error("Usuário não autenticado");
  }
  return session;
}

// =====================================================
// FUNÇÕES - FAVORITOS
// =====================================================

/**
 * Adicionar perfil aos favoritos
 */
export async function addToFavorites(profileId: string): Promise<Favorite> {
  const session = await requireAuth();
  
  try {
    // Verificar se já está nos favoritos
    const { data: existing } = await supabase
      .from("market_favorites")
      .select("id")
      .eq("user_id", session.user.id)
      .eq("profile_id", profileId)
      .single();

    if (existing) {
      throw new Error("Perfil já está nos favoritos");
    }

    const { data, error } = await supabase
      .from("market_favorites")
      .insert({
        user_id: session.user.id,
        profile_id: profileId
      })
      .select()
      .single();

    if (error) {
      console.error("Erro ao adicionar favorito:", error);
      throw new Error(`Erro ao adicionar favorito: ${error.message}`);
    }

    return data;
  } catch (error) {
    console.error("Erro ao adicionar aos favoritos:", error);
    throw error;
  }
}

/**
 * Remover perfil dos favoritos
 */
export async function removeFromFavorites(profileId: string): Promise<void> {
  const session = await requireAuth();
  
  try {
    const { error } = await supabase
      .from("market_favorites")
      .delete()
      .eq("user_id", session.user.id)
      .eq("profile_id", profileId);

    if (error) {
      console.error("Erro ao remover favorito:", error);
      throw new Error(`Erro ao remover favorito: ${error.message}`);
    }
  } catch (error) {
    console.error("Erro ao remover dos favoritos:", error);
    throw error;
  }
}

/**
 * Verificar se um perfil está nos favoritos
 */
export async function isFavorite(profileId: string): Promise<boolean> {
  const session = await requireAuth();
  
  try {
    const { data, error } = await supabase
      .from("market_favorites")
      .select("id")
      .eq("user_id", session.user.id)
      .eq("profile_id", profileId)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error("Erro ao verificar favorito:", error);
      return false;
    }

    return !!data;
  } catch (error) {
    console.error("Erro ao verificar favorito:", error);
    return false;
  }
}

/**
 * Obter lista de favoritos do usuário
 */
export async function getFavorites(): Promise<Favorite[]> {
  const session = await requireAuth();
  
  try {
    const { data, error } = await supabase
      .from("market_favorites")
      .select(`
        *,
        profile:market_profiles(*)
      `)
      .eq("user_id", session.user.id)
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Erro ao buscar favoritos:", error);
      throw new Error(`Erro ao buscar favoritos: ${error.message}`);
    }

    return data || [];
  } catch (error) {
    console.error("Erro ao obter favoritos:", error);
    throw error;
  }
}

// =====================================================
// FUNÇÕES - BUSCAS SALVAS
// =====================================================

/**
 * Salvar uma busca
 */
export async function saveSearch(searchName: string, searchCriteria: MarketProfileFilters): Promise<SavedSearch> {
  const session = await requireAuth();
  
  try {
    // Verificar se já existe uma busca com o mesmo nome
    const { data: existing } = await supabase
      .from("market_saved_searches")
      .select("id")
      .eq("user_id", session.user.id)
      .eq("search_name", searchName)
      .single();

    if (existing) {
      throw new Error("Já existe uma busca salva com este nome");
    }

    const { data, error } = await supabase
      .from("market_saved_searches")
      .insert({
        user_id: session.user.id,
        search_name: searchName,
        search_criteria: searchCriteria
      })
      .select()
      .single();

    if (error) {
      console.error("Erro ao salvar busca:", error);
      throw new Error(`Erro ao salvar busca: ${error.message}`);
    }

    return data;
  } catch (error) {
    console.error("Erro ao salvar busca:", error);
    throw error;
  }
}

/**
 * Obter buscas salvas do usuário
 */
export async function getSavedSearches(): Promise<SavedSearch[]> {
  const session = await requireAuth();
  
  try {
    const { data, error } = await supabase
      .from("market_saved_searches")
      .select("*")
      .eq("user_id", session.user.id)
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Erro ao buscar buscas salvas:", error);
      throw new Error(`Erro ao buscar buscas salvas: ${error.message}`);
    }

    return data || [];
  } catch (error) {
    console.error("Erro ao obter buscas salvas:", error);
    throw error;
  }
}

/**
 * Deletar busca salva
 */
export async function deleteSavedSearch(searchId: string): Promise<void> {
  const session = await requireAuth();
  
  try {
    const { error } = await supabase
      .from("market_saved_searches")
      .delete()
      .eq("id", searchId)
      .eq("user_id", session.user.id);

    if (error) {
      console.error("Erro ao deletar busca salva:", error);
      throw new Error(`Erro ao deletar busca salva: ${error.message}`);
    }
  } catch (error) {
    console.error("Erro ao deletar busca salva:", error);
    throw error;
  }
}

/**
 * Atualizar busca salva
 */
export async function updateSavedSearch(
  searchId: string, 
  searchName: string, 
  searchCriteria: MarketProfileFilters
): Promise<SavedSearch> {
  const session = await requireAuth();
  
  try {
    const { data, error } = await supabase
      .from("market_saved_searches")
      .update({
        search_name: searchName,
        search_criteria: searchCriteria
      })
      .eq("id", searchId)
      .eq("user_id", session.user.id)
      .select()
      .single();

    if (error) {
      console.error("Erro ao atualizar busca salva:", error);
      throw new Error(`Erro ao atualizar busca salva: ${error.message}`);
    }

    return data;
  } catch (error) {
    console.error("Erro ao atualizar busca salva:", error);
    throw error;
  }
}

// =====================================================
// FUNÇÕES - VISUALIZAÇÕES DE PERFIL
// =====================================================

/**
 * Registrar visualização de perfil
 */
export async function logProfileView(profileId: string): Promise<void> {
  try {
    const { data: { session } } = await supabase.auth.getSession();
    
    // Obter IP do usuário (se disponível)
    let ipAddress: string | undefined;
    try {
      const response = await fetch('https://api.ipify.org?format=json');
      const data = await response.json();
      ipAddress = data.ip;
    } catch (error) {
      console.warn("Não foi possível obter IP do usuário:", error);
    }

    const { error } = await supabase
      .from("market_profile_views")
      .insert({
        profile_id: profileId,
        viewer_user_id: session?.user.id || null,
        viewer_ip_address: ipAddress || null
      });

    if (error) {
      console.error("Erro ao registrar visualização:", error);
      // Não lançar erro para não interromper a visualização do perfil
    }
  } catch (error) {
    console.error("Erro ao registrar visualização de perfil:", error);
    // Não lançar erro para não interromper a visualização do perfil
  }
}

/**
 * Obter estatísticas de visualizações de um perfil
 */
export async function getProfileViewStats(profileId: string): Promise<{
  totalViews: number;
  uniqueViewers: number;
  viewsToday: number;
  viewsThisWeek: number;
  viewsThisMonth: number;
}> {
  const session = await requireAuth();
  
  try {
    // Verificar se o perfil pertence ao usuário
    const { data: profile } = await supabase
      .from("market_profiles")
      .select("created_by")
      .eq("id", profileId)
      .single();

    if (!profile || profile.created_by !== session.user.id) {
      throw new Error("Não autorizado a ver estatísticas deste perfil");
    }

    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

    // Total de visualizações
    const { count: totalViews } = await supabase
      .from("market_profile_views")
      .select("*", { count: 'exact', head: true })
      .eq("profile_id", profileId);

    // Visualizadores únicos
    const { data: uniqueViewersData } = await supabase
      .from("market_profile_views")
      .select("viewer_user_id, viewer_ip_address")
      .eq("profile_id", profileId);

    const uniqueViewers = new Set(
      uniqueViewersData?.map(v => v.viewer_user_id || v.viewer_ip_address).filter(Boolean)
    ).size;

    // Visualizações hoje
    const { count: viewsToday } = await supabase
      .from("market_profile_views")
      .select("*", { count: 'exact', head: true })
      .eq("profile_id", profileId)
      .gte("created_at", today.toISOString());

    // Visualizações esta semana
    const { count: viewsThisWeek } = await supabase
      .from("market_profile_views")
      .select("*", { count: 'exact', head: true })
      .eq("profile_id", profileId)
      .gte("created_at", weekAgo.toISOString());

    // Visualizações este mês
    const { count: viewsThisMonth } = await supabase
      .from("market_profile_views")
      .select("*", { count: 'exact', head: true })
      .eq("profile_id", profileId)
      .gte("created_at", monthAgo.toISOString());

    return {
      totalViews: totalViews || 0,
      uniqueViewers,
      viewsToday: viewsToday || 0,
      viewsThisWeek: viewsThisWeek || 0,
      viewsThisMonth: viewsThisMonth || 0
    };
  } catch (error) {
    console.error("Erro ao obter estatísticas de visualizações:", error);
    throw error;
  }
}
