import { supabase } from "@/integrations/supabase/client";
import { v4 as uuidv4 } from "uuid";

// =====================================================
// TIPOS E INTERFACES
// =====================================================

export interface MarketProfile {
  id: string;
  full_name: string;
  nickname?: string;
  email: string;
  phone?: string;
  birth_date: string;
  nationality: string;
  has_eu_passport: boolean;
  height?: number;
  weight?: number;
  profile_type: 'player' | 'technical_staff' | 'support_staff';
  position?: string;
  preferred_foot?: 'direito' | 'esquerdo' | 'ambos';
  role?: string;
  years_experience: number;
  last_club?: string;
  career_highlights?: string;
  speed_rating?: number;
  finishing_rating?: number;
  passing_rating?: number;
  defending_rating?: number;
  physical_rating?: number;
  market_value_estimate?: number;
  salary_expectation_min?: number;
  salary_expectation_max?: number;
  available_for_travel: boolean;
  available_for_relocation: boolean;
  languages_spoken?: string[];
  availability_status: 'available' | 'contracted' | 'on_loan' | 'retired' | 'injured';
  contract_end_date?: string;
  certifications?: string[];
  specializations?: string[];
  work_methodology?: string;
  profile_photo_url?: string;
  additional_photos?: string[];
  highlight_video_url?: string;
  full_game_video_url?: string;
  resume_document_url?: string;
  personal_description?: string;
  subscription_status: 'trial' | 'active' | 'expired' | 'suspended';
  subscription_start_date: string;
  subscription_end_date?: string;
  last_payment_date?: string;
  created_at: string;
  updated_at: string;
  created_by: string;
  is_active: boolean;
}

export interface MarketProfileFilters {
  profile_type?: 'player' | 'technical_staff' | 'support_staff';
  position?: string[];
  age_min?: number;
  age_max?: number;
  height_min?: number;
  height_max?: number;
  weight_min?: number;
  weight_max?: number;
  preferred_foot?: string;
  nationality?: string;
  availability_status?: string[];
  market_value_min?: number;
  market_value_max?: number;
  experience_min?: number;
  experience_max?: number;
  salary_min?: number;
  salary_max?: number;
  available_for_travel?: boolean;
  available_for_relocation?: boolean;
  has_eu_passport?: boolean;
  certifications?: string[];
  specializations?: string[];
  search_text?: string;
  limit?: number;
  offset?: number;
}

export interface CareerHistory {
  id: string;
  profile_id: string;
  club_name: string;
  position_role?: string;
  start_date?: string;
  end_date?: string;
  achievements?: string;
  created_at: string;
}

// =====================================================
// FUNÇÕES DE AUTENTICAÇÃO E VALIDAÇÃO
// =====================================================

async function requireAuth() {
  const { data: { session } } = await supabase.auth.getSession();
  if (!session) {
    throw new Error("Usuário não autenticado");
  }
  return session;
}

async function validateProfileOwnership(profileId: string, userId: string): Promise<boolean> {
  const { data, error } = await supabase
    .from("market_profiles")
    .select("created_by")
    .eq("id", profileId)
    .single();

  if (error || !data) {
    return false;
  }

  return data.created_by === userId;
}

// =====================================================
// FUNÇÕES PRINCIPAIS - PERFIS
// =====================================================

/**
 * Criar um novo perfil de mercado
 */
export async function createMarketProfile(profileData: Partial<MarketProfile>): Promise<MarketProfile> {
  const session = await requireAuth();
  
  try {
    // Verificar se o usuário já tem um perfil
    const { data: existingProfile } = await supabase
      .from("market_profiles")
      .select("id")
      .eq("created_by", session.user.id)
      .single();

    if (existingProfile) {
      throw new Error("Usuário já possui um perfil de mercado");
    }

    // Validar dados obrigatórios
    if (!profileData.full_name || !profileData.email || !profileData.birth_date || !profileData.profile_type) {
      throw new Error("Dados obrigatórios não fornecidos");
    }

    // Preparar dados para inserção
    const insertData = {
      ...profileData,
      created_by: session.user.id,
      subscription_status: 'trial', // Período de teste para 2024
      subscription_start_date: new Date().toISOString().split('T')[0],
      // Para 2025, definir data de expiração
      subscription_end_date: new Date('2025-01-01').toISOString().split('T')[0],
    };

    const { data, error } = await supabase
      .from("market_profiles")
      .insert(insertData)
      .select()
      .single();

    if (error) {
      console.error("Erro ao criar perfil:", error);
      throw new Error(`Erro ao criar perfil: ${error.message}`);
    }

    return data;
  } catch (error) {
    console.error("Erro ao criar perfil de mercado:", error);
    throw error;
  }
}

/**
 * Atualizar perfil de mercado
 */
export async function updateMarketProfile(profileId: string, updates: Partial<MarketProfile>): Promise<MarketProfile> {
  const session = await requireAuth();
  
  try {
    // Verificar propriedade do perfil
    const isOwner = await validateProfileOwnership(profileId, session.user.id);
    if (!isOwner) {
      throw new Error("Não autorizado a editar este perfil");
    }

    const { data, error } = await supabase
      .from("market_profiles")
      .update(updates)
      .eq("id", profileId)
      .select()
      .single();

    if (error) {
      console.error("Erro ao atualizar perfil:", error);
      throw new Error(`Erro ao atualizar perfil: ${error.message}`);
    }

    return data;
  } catch (error) {
    console.error("Erro ao atualizar perfil de mercado:", error);
    throw error;
  }
}

/**
 * Buscar perfis com filtros
 */
export async function searchMarketProfiles(filters: MarketProfileFilters = {}): Promise<{
  profiles: MarketProfile[];
  total: number;
}> {
  await requireAuth();
  
  try {
    let query = supabase
      .from("market_profiles")
      .select("*", { count: 'exact' })
      .eq("is_active", true)
      .in("subscription_status", ["trial", "active"]);

    // Aplicar filtros
    if (filters.profile_type) {
      query = query.eq("profile_type", filters.profile_type);
    }

    if (filters.position && filters.position.length > 0) {
      query = query.in("position", filters.position);
    }

    if (filters.age_min || filters.age_max) {
      const currentYear = new Date().getFullYear();
      if (filters.age_max) {
        const minBirthYear = currentYear - filters.age_max;
        query = query.gte("birth_date", `${minBirthYear}-01-01`);
      }
      if (filters.age_min) {
        const maxBirthYear = currentYear - filters.age_min;
        query = query.lte("birth_date", `${maxBirthYear}-12-31`);
      }
    }

    if (filters.height_min) {
      query = query.gte("height", filters.height_min);
    }

    if (filters.height_max) {
      query = query.lte("height", filters.height_max);
    }

    if (filters.preferred_foot) {
      query = query.eq("preferred_foot", filters.preferred_foot);
    }

    if (filters.nationality) {
      query = query.eq("nationality", filters.nationality);
    }

    if (filters.availability_status && filters.availability_status.length > 0) {
      query = query.in("availability_status", filters.availability_status);
    }

    if (filters.market_value_min) {
      query = query.gte("market_value_estimate", filters.market_value_min);
    }

    if (filters.market_value_max) {
      query = query.lte("market_value_estimate", filters.market_value_max);
    }

    if (filters.experience_min) {
      query = query.gte("years_experience", filters.experience_min);
    }

    if (filters.experience_max) {
      query = query.lte("years_experience", filters.experience_max);
    }

    if (filters.available_for_travel !== undefined) {
      query = query.eq("available_for_travel", filters.available_for_travel);
    }

    if (filters.available_for_relocation !== undefined) {
      query = query.eq("available_for_relocation", filters.available_for_relocation);
    }

    if (filters.has_eu_passport !== undefined) {
      query = query.eq("has_eu_passport", filters.has_eu_passport);
    }

    // Busca textual
    if (filters.search_text) {
      query = query.textSearch("search_vector", filters.search_text, {
        type: 'websearch',
        config: 'portuguese'
      });
    }

    // Paginação
    const limit = filters.limit || 20;
    const offset = filters.offset || 0;
    query = query.range(offset, offset + limit - 1);

    // Ordenação
    query = query.order("created_at", { ascending: false });

    const { data, error, count } = await query;

    if (error) {
      console.error("Erro ao buscar perfis:", error);
      throw new Error(`Erro ao buscar perfis: ${error.message}`);
    }

    return {
      profiles: data || [],
      total: count || 0
    };
  } catch (error) {
    console.error("Erro ao buscar perfis de mercado:", error);
    throw error;
  }
}

/**
 * Obter perfil por ID
 */
export async function getMarketProfileById(profileId: string): Promise<MarketProfile | null> {
  await requireAuth();
  
  try {
    const { data, error } = await supabase
      .from("market_profiles")
      .select("*")
      .eq("id", profileId)
      .eq("is_active", true)
      .in("subscription_status", ["trial", "active"])
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Perfil não encontrado
      }
      throw new Error(`Erro ao buscar perfil: ${error.message}`);
    }

    return data;
  } catch (error) {
    console.error("Erro ao buscar perfil por ID:", error);
    throw error;
  }
}

/**
 * Obter perfil do usuário atual
 */
export async function getMyMarketProfile(): Promise<MarketProfile | null> {
  const session = await requireAuth();

  try {
    const { data, error } = await supabase
      .from("market_profiles")
      .select("*")
      .eq("created_by", session.user.id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Usuário não tem perfil
      }
      throw new Error(`Erro ao buscar perfil: ${error.message}`);
    }

    return data;
  } catch (error) {
    console.error("Erro ao buscar meu perfil:", error);
    throw error;
  }
}

/**
 * Deletar perfil de mercado
 */
export async function deleteMarketProfile(profileId: string): Promise<void> {
  const session = await requireAuth();

  try {
    // Verificar propriedade do perfil
    const isOwner = await validateProfileOwnership(profileId, session.user.id);
    if (!isOwner) {
      throw new Error("Não autorizado a deletar este perfil");
    }

    const { error } = await supabase
      .from("market_profiles")
      .update({ is_active: false })
      .eq("id", profileId);

    if (error) {
      console.error("Erro ao deletar perfil:", error);
      throw new Error(`Erro ao deletar perfil: ${error.message}`);
    }
  } catch (error) {
    console.error("Erro ao deletar perfil de mercado:", error);
    throw error;
  }
}

// =====================================================
// FUNÇÕES - HISTÓRICO PROFISSIONAL
// =====================================================

/**
 * Adicionar histórico profissional
 */
export async function addCareerHistory(profileId: string, historyData: Partial<CareerHistory>): Promise<CareerHistory> {
  const session = await requireAuth();

  try {
    // Verificar propriedade do perfil
    const isOwner = await validateProfileOwnership(profileId, session.user.id);
    if (!isOwner) {
      throw new Error("Não autorizado a editar este perfil");
    }

    const { data, error } = await supabase
      .from("market_career_history")
      .insert({
        ...historyData,
        profile_id: profileId
      })
      .select()
      .single();

    if (error) {
      console.error("Erro ao adicionar histórico:", error);
      throw new Error(`Erro ao adicionar histórico: ${error.message}`);
    }

    return data;
  } catch (error) {
    console.error("Erro ao adicionar histórico profissional:", error);
    throw error;
  }
}

/**
 * Obter histórico profissional de um perfil
 */
export async function getCareerHistory(profileId: string): Promise<CareerHistory[]> {
  await requireAuth();

  try {
    const { data, error } = await supabase
      .from("market_career_history")
      .select("*")
      .eq("profile_id", profileId)
      .order("start_date", { ascending: false });

    if (error) {
      console.error("Erro ao buscar histórico:", error);
      throw new Error(`Erro ao buscar histórico: ${error.message}`);
    }

    return data || [];
  } catch (error) {
    console.error("Erro ao buscar histórico profissional:", error);
    throw error;
  }
}

/**
 * Atualizar histórico profissional
 */
export async function updateCareerHistory(historyId: string, updates: Partial<CareerHistory>): Promise<CareerHistory> {
  const session = await requireAuth();

  try {
    // Verificar se o histórico pertence ao usuário
    const { data: history } = await supabase
      .from("market_career_history")
      .select("profile_id")
      .eq("id", historyId)
      .single();

    if (!history) {
      throw new Error("Histórico não encontrado");
    }

    const isOwner = await validateProfileOwnership(history.profile_id, session.user.id);
    if (!isOwner) {
      throw new Error("Não autorizado a editar este histórico");
    }

    const { data, error } = await supabase
      .from("market_career_history")
      .update(updates)
      .eq("id", historyId)
      .select()
      .single();

    if (error) {
      console.error("Erro ao atualizar histórico:", error);
      throw new Error(`Erro ao atualizar histórico: ${error.message}`);
    }

    return data;
  } catch (error) {
    console.error("Erro ao atualizar histórico profissional:", error);
    throw error;
  }
}

/**
 * Deletar histórico profissional
 */
export async function deleteCareerHistory(historyId: string): Promise<void> {
  const session = await requireAuth();

  try {
    // Verificar se o histórico pertence ao usuário
    const { data: history } = await supabase
      .from("market_career_history")
      .select("profile_id")
      .eq("id", historyId)
      .single();

    if (!history) {
      throw new Error("Histórico não encontrado");
    }

    const isOwner = await validateProfileOwnership(history.profile_id, session.user.id);
    if (!isOwner) {
      throw new Error("Não autorizado a deletar este histórico");
    }

    const { error } = await supabase
      .from("market_career_history")
      .delete()
      .eq("id", historyId);

    if (error) {
      console.error("Erro ao deletar histórico:", error);
      throw new Error(`Erro ao deletar histórico: ${error.message}`);
    }
  } catch (error) {
    console.error("Erro ao deletar histórico profissional:", error);
    throw error;
  }
}
