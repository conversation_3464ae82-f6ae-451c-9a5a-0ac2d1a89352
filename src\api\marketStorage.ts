import { supabase } from "@/integrations/supabase/client";
import { v4 as uuidv4 } from "uuid";

// =====================================================
// TIPOS E INTERFACES
// =====================================================

export interface UploadResult {
  url: string;
  path: string;
  fileName: string;
}

export type FileType = 'profile-photo' | 'additional-photo' | 'highlight-video' | 'full-game-video' | 'resume' | 'certificate' | 'gallery-photo';

// =====================================================
// CONFIGURAÇÕES
// =====================================================

const BUCKET_NAME = 'market-profiles';
const MAX_FILE_SIZES = {
  'profile-photo': 5 * 1024 * 1024, // 5MB
  'additional-photo': 5 * 1024 * 1024, // 5MB
  'gallery-photo': 5 * 1024 * 1024, // 5MB
  'highlight-video': 50 * 1024 * 1024, // 50MB
  'full-game-video': 100 * 1024 * 1024, // 100MB
  'resume': 10 * 1024 * 1024, // 10MB
  'certificate': 10 * 1024 * 1024, // 10MB
};

const ALLOWED_MIME_TYPES = {
  'profile-photo': ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
  'additional-photo': ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
  'gallery-photo': ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
  'highlight-video': ['video/mp4', 'video/avi', 'video/mov', 'video/wmv'],
  'full-game-video': ['video/mp4', 'video/avi', 'video/mov', 'video/wmv'],
  'resume': ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
  'certificate': ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'],
};

// =====================================================
// FUNÇÕES AUXILIARES
// =====================================================

async function requireAuth() {
  const { data: { session } } = await supabase.auth.getSession();
  if (!session) {
    throw new Error("Usuário não autenticado");
  }
  return session;
}

function validateFile(file: File, fileType: FileType): void {
  // Verificar tamanho
  const maxSize = MAX_FILE_SIZES[fileType];
  if (file.size > maxSize) {
    throw new Error(`Arquivo muito grande. Tamanho máximo: ${(maxSize / 1024 / 1024).toFixed(1)}MB`);
  }

  // Verificar tipo MIME
  const allowedTypes = ALLOWED_MIME_TYPES[fileType];
  if (!allowedTypes.includes(file.type)) {
    throw new Error(`Tipo de arquivo não permitido. Tipos aceitos: ${allowedTypes.join(', ')}`);
  }
}

function generateFilePath(userId: string, fileType: FileType, originalFileName: string): string {
  const fileExt = originalFileName.split('.').pop()?.toLowerCase() || '';
  const timestamp = Date.now();
  const uniqueId = uuidv4().substring(0, 8);

  switch (fileType) {
    case 'profile-photo':
      return `${userId}/profile-photos/main-photo-${timestamp}.${fileExt}`;
    case 'additional-photo':
      return `${userId}/profile-photos/additional-${timestamp}-${uniqueId}.${fileExt}`;
    case 'gallery-photo':
      return `${userId}/gallery/photo-${timestamp}-${uniqueId}.${fileExt}`;
    case 'highlight-video':
      return `${userId}/videos/highlights-${timestamp}.${fileExt}`;
    case 'full-game-video':
      return `${userId}/videos/full-game-${timestamp}.${fileExt}`;
    case 'resume':
      return `${userId}/documents/resume-${timestamp}.${fileExt}`;
    case 'certificate':
      return `${userId}/documents/certificate-${timestamp}-${uniqueId}.${fileExt}`;
    default:
      throw new Error(`Tipo de arquivo não reconhecido: ${fileType}`);
  }
}

// =====================================================
// FUNÇÕES PRINCIPAIS
// =====================================================

/**
 * Upload de arquivo para o bucket de mercado
 */
export async function uploadMarketFile(
  file: File,
  fileType: FileType,
  onProgress?: (progress: number) => void
): Promise<UploadResult> {
  const session = await requireAuth();
  
  try {
    console.log(`[DEBUG uploadMarketFile] Iniciando upload: ${file.name}, tipo: ${fileType}`);
    
    // Validar arquivo
    validateFile(file, fileType);
    
    // Gerar caminho do arquivo
    const filePath = generateFilePath(session.user.id, fileType, file.name);
    console.log(`[DEBUG uploadMarketFile] Caminho gerado: ${filePath}`);
    
    // Fazer upload
    const { data, error } = await supabase.storage
      .from(BUCKET_NAME)
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: true,
        contentType: file.type,
      });

    if (error) {
      console.error(`[DEBUG uploadMarketFile] Erro no upload:`, error);
      throw new Error(`Erro ao fazer upload: ${error.message}`);
    }

    // Obter URL pública
    const { data: urlData } = supabase.storage
      .from(BUCKET_NAME)
      .getPublicUrl(filePath);

    const result: UploadResult = {
      url: urlData.publicUrl,
      path: filePath,
      fileName: file.name
    };

    console.log(`[DEBUG uploadMarketFile] Upload concluído:`, result);
    return result;
    
  } catch (error) {
    console.error("Erro no upload de arquivo:", error);
    throw error;
  }
}

/**
 * Upload de múltiplas fotos para galeria
 */
export async function uploadMultiplePhotos(
  files: File[],
  onProgress?: (fileIndex: number, progress: number) => void
): Promise<UploadResult[]> {
  const session = await requireAuth();
  
  try {
    const results: UploadResult[] = [];
    
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      
      if (onProgress) {
        onProgress(i, 0);
      }
      
      const result = await uploadMarketFile(file, 'gallery-photo');
      results.push(result);
      
      if (onProgress) {
        onProgress(i, 100);
      }
    }
    
    return results;
  } catch (error) {
    console.error("Erro no upload de múltiplas fotos:", error);
    throw error;
  }
}

/**
 * Deletar arquivo do storage
 */
export async function deleteMarketFile(filePath: string): Promise<void> {
  const session = await requireAuth();
  
  try {
    // Verificar se o arquivo pertence ao usuário
    if (!filePath.startsWith(session.user.id)) {
      throw new Error("Não autorizado a deletar este arquivo");
    }
    
    const { error } = await supabase.storage
      .from(BUCKET_NAME)
      .remove([filePath]);

    if (error) {
      console.error("Erro ao deletar arquivo:", error);
      throw new Error(`Erro ao deletar arquivo: ${error.message}`);
    }
    
    console.log(`[DEBUG deleteMarketFile] Arquivo deletado: ${filePath}`);
  } catch (error) {
    console.error("Erro ao deletar arquivo:", error);
    throw error;
  }
}

/**
 * Listar arquivos do usuário
 */
export async function listUserFiles(folder?: string): Promise<any[]> {
  const session = await requireAuth();
  
  try {
    const prefix = folder ? `${session.user.id}/${folder}` : session.user.id;
    
    const { data, error } = await supabase.storage
      .from(BUCKET_NAME)
      .list(prefix, {
        limit: 100,
        sortBy: { column: 'created_at', order: 'desc' }
      });

    if (error) {
      console.error("Erro ao listar arquivos:", error);
      throw new Error(`Erro ao listar arquivos: ${error.message}`);
    }

    return data || [];
  } catch (error) {
    console.error("Erro ao listar arquivos do usuário:", error);
    throw error;
  }
}

/**
 * Obter URL pública de um arquivo
 */
export function getPublicUrl(filePath: string): string {
  const { data } = supabase.storage
    .from(BUCKET_NAME)
    .getPublicUrl(filePath);
    
  return data.publicUrl;
}

/**
 * Verificar se um arquivo existe
 */
export async function fileExists(filePath: string): Promise<boolean> {
  try {
    const { data, error } = await supabase.storage
      .from(BUCKET_NAME)
      .list(filePath.substring(0, filePath.lastIndexOf('/')), {
        search: filePath.substring(filePath.lastIndexOf('/') + 1)
      });

    return !error && data && data.length > 0;
  } catch (error) {
    console.error("Erro ao verificar existência do arquivo:", error);
    return false;
  }
}

/**
 * Obter informações de uso de storage do usuário
 */
export async function getUserStorageUsage(): Promise<{
  totalFiles: number;
  totalSize: number;
  filesByType: Record<string, number>;
}> {
  const session = await requireAuth();
  
  try {
    const folders = ['profile-photos', 'videos', 'documents', 'gallery'];
    let totalFiles = 0;
    let totalSize = 0;
    const filesByType: Record<string, number> = {};
    
    for (const folder of folders) {
      const files = await listUserFiles(folder);
      const folderFileCount = files.length;
      const folderSize = files.reduce((sum, file) => sum + (file.metadata?.size || 0), 0);
      
      totalFiles += folderFileCount;
      totalSize += folderSize;
      filesByType[folder] = folderFileCount;
    }
    
    return {
      totalFiles,
      totalSize,
      filesByType
    };
  } catch (error) {
    console.error("Erro ao obter uso de storage:", error);
    throw error;
  }
}
