import { supabase } from "@/integrations/supabase/client";

// =====================================================
// TIPOS E INTERFACES
// =====================================================

export interface SubscriptionPayment {
  id: string;
  profile_id: string;
  amount: number;
  payment_method?: string;
  payment_status: 'pending' | 'completed' | 'failed' | 'refunded';
  payment_date?: string;
  reference_code?: string;
  created_at: string;
}

export interface SubscriptionInfo {
  profile_id: string;
  subscription_status: 'trial' | 'active' | 'expired' | 'suspended';
  subscription_start_date: string;
  subscription_end_date?: string;
  last_payment_date?: string;
  next_payment_due?: string;
  is_trial_period: boolean;
  days_until_expiry?: number;
}

// =====================================================
// FUNÇÕES DE AUTENTICAÇÃO
// =====================================================

async function requireAuth() {
  const { data: { session } } = await supabase.auth.getSession();
  if (!session) {
    throw new Error("Usuário não autenticado");
  }
  return session;
}

// =====================================================
// FUNÇÕES PRINCIPAIS
// =====================================================

/**
 * Verificar status da assinatura de um perfil
 */
export async function getSubscriptionInfo(profileId: string): Promise<SubscriptionInfo | null> {
  await requireAuth();
  
  try {
    const { data: profile, error } = await supabase
      .from("market_profiles")
      .select(`
        id,
        subscription_status,
        subscription_start_date,
        subscription_end_date,
        last_payment_date
      `)
      .eq("id", profileId)
      .single();

    if (error || !profile) {
      return null;
    }

    const startDate = new Date(profile.subscription_start_date);
    const endDate = profile.subscription_end_date ? new Date(profile.subscription_end_date) : null;
    const now = new Date();
    
    // Verificar se está no período de teste (2024)
    const isTrialPeriod = profile.subscription_status === 'trial' || now.getFullYear() === 2024;
    
    // Calcular dias até expiração
    let daysUntilExpiry: number | undefined;
    if (endDate) {
      const diffTime = endDate.getTime() - now.getTime();
      daysUntilExpiry = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }

    // Calcular próxima data de pagamento
    let nextPaymentDue: string | undefined;
    if (profile.subscription_status === 'active' && endDate) {
      // Próximo pagamento é 30 dias antes do vencimento
      const paymentDate = new Date(endDate);
      paymentDate.setDate(paymentDate.getDate() - 30);
      nextPaymentDue = paymentDate.toISOString().split('T')[0];
    }

    return {
      profile_id: profileId,
      subscription_status: profile.subscription_status,
      subscription_start_date: profile.subscription_start_date,
      subscription_end_date: profile.subscription_end_date,
      last_payment_date: profile.last_payment_date,
      next_payment_due: nextPaymentDue,
      is_trial_period: isTrialPeriod,
      days_until_expiry: daysUntilExpiry
    };
  } catch (error) {
    console.error("Erro ao obter informações da assinatura:", error);
    throw error;
  }
}

/**
 * Processar pagamento de assinatura
 */
export async function processSubscriptionPayment(
  profileId: string,
  paymentData: {
    amount: number;
    payment_method: string;
    reference_code?: string;
  }
): Promise<SubscriptionPayment> {
  const session = await requireAuth();
  
  try {
    // Verificar se o perfil pertence ao usuário
    const { data: profile } = await supabase
      .from("market_profiles")
      .select("created_by")
      .eq("id", profileId)
      .single();

    if (!profile || profile.created_by !== session.user.id) {
      throw new Error("Não autorizado a processar pagamento para este perfil");
    }

    // Registrar pagamento
    const { data: payment, error: paymentError } = await supabase
      .from("market_subscription_payments")
      .insert({
        profile_id: profileId,
        amount: paymentData.amount,
        payment_method: paymentData.payment_method,
        payment_status: 'pending',
        reference_code: paymentData.reference_code,
        payment_date: new Date().toISOString().split('T')[0]
      })
      .select()
      .single();

    if (paymentError) {
      throw new Error(`Erro ao registrar pagamento: ${paymentError.message}`);
    }

    // Simular processamento do pagamento
    // Em um sistema real, aqui seria feita a integração com gateway de pagamento
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Atualizar status do pagamento para concluído
    const { error: updatePaymentError } = await supabase
      .from("market_subscription_payments")
      .update({ payment_status: 'completed' })
      .eq("id", payment.id);

    if (updatePaymentError) {
      throw new Error(`Erro ao atualizar status do pagamento: ${updatePaymentError.message}`);
    }

    // Atualizar assinatura do perfil
    const newEndDate = new Date();
    newEndDate.setFullYear(newEndDate.getFullYear() + 1); // Adicionar 1 ano

    const { error: updateProfileError } = await supabase
      .from("market_profiles")
      .update({
        subscription_status: 'active',
        subscription_end_date: newEndDate.toISOString().split('T')[0],
        last_payment_date: new Date().toISOString().split('T')[0]
      })
      .eq("id", profileId);

    if (updateProfileError) {
      throw new Error(`Erro ao atualizar assinatura: ${updateProfileError.message}`);
    }

    return {
      ...payment,
      payment_status: 'completed'
    };
  } catch (error) {
    console.error("Erro ao processar pagamento:", error);
    throw error;
  }
}

/**
 * Obter histórico de pagamentos de um perfil
 */
export async function getPaymentHistory(profileId: string): Promise<SubscriptionPayment[]> {
  const session = await requireAuth();
  
  try {
    // Verificar se o perfil pertence ao usuário
    const { data: profile } = await supabase
      .from("market_profiles")
      .select("created_by")
      .eq("id", profileId)
      .single();

    if (!profile || profile.created_by !== session.user.id) {
      throw new Error("Não autorizado a ver histórico deste perfil");
    }

    const { data, error } = await supabase
      .from("market_subscription_payments")
      .select("*")
      .eq("profile_id", profileId)
      .order("created_at", { ascending: false });

    if (error) {
      throw new Error(`Erro ao buscar histórico de pagamentos: ${error.message}`);
    }

    return data || [];
  } catch (error) {
    console.error("Erro ao obter histórico de pagamentos:", error);
    throw error;
  }
}

/**
 * Verificar se a assinatura está vencida
 */
export async function checkSubscriptionExpiry(profileId: string): Promise<{
  isExpired: boolean;
  daysUntilExpiry: number;
  shouldSuspend: boolean;
}> {
  try {
    const subscriptionInfo = await getSubscriptionInfo(profileId);
    
    if (!subscriptionInfo) {
      return { isExpired: true, daysUntilExpiry: 0, shouldSuspend: true };
    }

    // Durante 2024, todos os perfis estão em trial
    if (subscriptionInfo.is_trial_period) {
      const trialEndDate = new Date('2025-01-01');
      const now = new Date();
      const daysUntilExpiry = Math.ceil((trialEndDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      
      return {
        isExpired: false,
        daysUntilExpiry: Math.max(0, daysUntilExpiry),
        shouldSuspend: false
      };
    }

    // Para assinaturas pagas
    if (subscriptionInfo.subscription_end_date) {
      const endDate = new Date(subscriptionInfo.subscription_end_date);
      const now = new Date();
      const daysUntilExpiry = Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      
      const isExpired = daysUntilExpiry <= 0;
      const shouldSuspend = daysUntilExpiry <= -30; // Suspender após 30 dias de atraso
      
      return {
        isExpired,
        daysUntilExpiry: Math.max(0, daysUntilExpiry),
        shouldSuspend
      };
    }

    return { isExpired: false, daysUntilExpiry: 365, shouldSuspend: false };
  } catch (error) {
    console.error("Erro ao verificar expiração da assinatura:", error);
    return { isExpired: true, daysUntilExpiry: 0, shouldSuspend: true };
  }
}

/**
 * Suspender perfil por falta de pagamento
 */
export async function suspendProfile(profileId: string): Promise<void> {
  const session = await requireAuth();
  
  try {
    // Verificar se o perfil pertence ao usuário ou se é admin
    const { data: profile } = await supabase
      .from("market_profiles")
      .select("created_by")
      .eq("id", profileId)
      .single();

    if (!profile || profile.created_by !== session.user.id) {
      throw new Error("Não autorizado a suspender este perfil");
    }

    const { error } = await supabase
      .from("market_profiles")
      .update({ subscription_status: 'suspended' })
      .eq("id", profileId);

    if (error) {
      throw new Error(`Erro ao suspender perfil: ${error.message}`);
    }
  } catch (error) {
    console.error("Erro ao suspender perfil:", error);
    throw error;
  }
}

/**
 * Reativar perfil após pagamento
 */
export async function reactivateProfile(profileId: string): Promise<void> {
  const session = await requireAuth();
  
  try {
    // Verificar se o perfil pertence ao usuário
    const { data: profile } = await supabase
      .from("market_profiles")
      .select("created_by")
      .eq("id", profileId)
      .single();

    if (!profile || profile.created_by !== session.user.id) {
      throw new Error("Não autorizado a reativar este perfil");
    }

    const { error } = await supabase
      .from("market_profiles")
      .update({ subscription_status: 'active' })
      .eq("id", profileId);

    if (error) {
      throw new Error(`Erro ao reativar perfil: ${error.message}`);
    }
  } catch (error) {
    console.error("Erro ao reativar perfil:", error);
    throw error;
  }
}

/**
 * Gerar link de pagamento (simulado)
 */
export async function generatePaymentLink(
  profileId: string,
  amount: number = 120.00
): Promise<{
  paymentUrl: string;
  referenceCode: string;
  expiresAt: string;
}> {
  await requireAuth();
  
  try {
    // Em um sistema real, aqui seria feita a integração com gateway de pagamento
    // Por enquanto, vamos simular
    const referenceCode = `PAY_${Date.now()}_${Math.random().toString(36).substring(7)}`;
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + 24); // Expira em 24 horas
    
    // URL simulada - em produção seria do gateway de pagamento
    const paymentUrl = `/pagamento?ref=${referenceCode}&amount=${amount}&profile=${profileId}`;
    
    return {
      paymentUrl,
      referenceCode,
      expiresAt: expiresAt.toISOString()
    };
  } catch (error) {
    console.error("Erro ao gerar link de pagamento:", error);
    throw error;
  }
}
