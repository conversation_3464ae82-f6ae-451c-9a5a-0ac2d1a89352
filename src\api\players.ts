// Funções relacionadas a Players serão migradas para cá a partir do api.ts

import { supabase } from "@/integrations/supabase/client";
import { getSupabaseClientWithClubId } from "@/integrations/supabase/clientWithClubId";
import { v4 as uuidv4 } from "uuid";
import { uploadProfileImage } from "./storage";
import { withPermission, withAuditLog } from "./middleware";
import { PLAYER_PERMISSIONS } from "@/constants/permissions";
import type { Player } from "@/api/api";

async function ensureAuth() {
  const {
    data: { session }
  } = await supabase.auth.getSession();
  if (!session) {
    throw new Error("Sessão não autenticada");
  }
}


/**
 * Verifica se um jogador pertence ao usuário atual
 * @param clubId ID do clube
 * @param playerId ID do jogador
 * @param userId ID do usuário
 * @returns true se o jogador pertence ao usuário
 */
export async function isPlayerOwnedByUser(clubId: number, playerId: string, userId: string): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from("players")
      .select("id")
      .eq("club_id", clubId as any)
      .eq("id", playerId as any)
      .eq("user_id", userId as any)
      .single();

    if (error) {
      return false;
    }

    return !!data;
  } catch (error) {
    return false;
  }
}

/**
 * Obtém o jogador associado a um usuário
 * @param clubId ID do clube
 * @param userId ID do usuário
 * @returns Dados do jogador ou null se não encontrado
 */
export async function getPlayerByUserId(clubId: number, userId: string): Promise<Player | null> {
  try {
    const { data, error } = await supabase
      .from("players")
      .select("*")
      .eq("club_id", clubId as any)
      .eq("user_id", userId as any)
      .single();

    if (error) {
      console.log(`Jogador não encontrado para o usuário ${userId}:`, error.message);
      return null;
    }

    return data as unknown as Player;
  } catch (error) {
    console.error("Erro ao buscar jogador por user_id:", error);
    return null;
  }
}

/**
 * Busca um jogador por CPF (busca global em todos os clubes)
 * @param clubId ID do clube (usado apenas para logs, não filtra a busca)
 * @param cpf CPF do jogador
 * @returns Dados do jogador ou null se não encontrado
 */
export async function getPlayerByCpf(clubId: number, cpf: string): Promise<Player | null> {
  try {
    console.log(`Buscando jogador por CPF: ${cpf} (busca global, não restrita ao clube)`);

    // Limpar CPF (remover formatação)
    const cleanCPF = cpf.replace(/\D/g, '');
    // Usar função RPC existente que busca jogador globalmente
    const { data, error } = await supabase.rpc('search_player_by_cpf', {
      p_cpf: cleanCPF
    });

    console.log("Resultado da busca:", { data, error });

    if (error) {
      console.log(`Erro ao buscar jogador por CPF ${cleanCPF}:`, error.message);
      return null;
    }

    const result = data && data.length > 0 ? data[0] : null;

    if (!result || !result.found) {
      console.log(`Nenhum jogador encontrado para o CPF ${cleanCPF}`);
      return null;
    }

    const player = {
      id: result.global_player_id_result || result.global_player_id,
      club_id: result.last_club_id,
      name: result.name,
      email: result.email,
      birthdate: result.birthdate,
      nationality: result.nationality,
      rg_number: result.rg_number,
      phone: result.phone,
      height: result.height,
      weight: result.weight,
      father_name: result.father_name,
      mother_name: result.mother_name
    };

    console.log(`Jogador encontrado: ${player.name} (Global ID: ${player.id}) último clube: ${player.club_id}`);
    return player as unknown as Player;
  } catch (error) {
    console.error("Erro ao buscar jogador por CPF:", error);
    return null;
  }
}

/**
 * Busca um jogador por CPF apenas no clube específico
 * @param clubId ID do clube
 * @param cpf CPF do jogador
 * @returns Dados do jogador ou null se não encontrado
 */
export async function getPlayerByCpfInClub(clubId: number, cpf: string): Promise<Player | null> {
  try {
    console.log(`Buscando jogador por CPF: ${cpf} no clube: ${clubId}`);

    // Usar cliente Supabase com club_id nos headers para garantir RLS
    const supabaseWithClubId = getSupabaseClientWithClubId(clubId);

    const { data, error } = await supabaseWithClubId
      .from("players")
      .select("*")
      .eq("cpf_number", cpf)
      .limit(1);

    if (error) {
      console.log(`Erro ao buscar jogador por CPF ${cpf}:`, error.message);
      return null;
    }

    if (!data || data.length === 0) {
      console.log(`Nenhum jogador encontrado para o CPF ${cpf} no clube ${clubId}`);
      return null;
    }

    console.log(`Jogador encontrado: ${data[0].name} (ID: ${data[0].id})`);
    return data[0] as unknown as Player;
  } catch (error) {
    console.error("Erro ao buscar jogador por CPF:", error);
    return null;
  }
}

/**
 * Verifica o status de um jogador baseado no user_id
 * @param clubId ID do clube
 * @param userId ID do usuário
 * @returns Objeto com informações do status do jogador ou null se não for jogador
 */
export async function checkPlayerStatus(clubId: number, userId: string): Promise<{ isPlayer: boolean; status?: string; playerId?: string } | null> {
  try {
    console.log("checkPlayerStatus - Verificando:", { clubId, userId });

    const { data, error } = await supabase
      .from("players")
      .select("id, status, name")
      .eq("club_id", clubId as any)
      .eq("user_id", userId as any)
      .single();

    if (error) {
      console.log("checkPlayerStatus - Usuário não é jogador:", error.message);
      // Se não encontrou o jogador, significa que o usuário não é um jogador
      return { isPlayer: false };
    }

    console.log("checkPlayerStatus - Jogador encontrado:", {
      playerId: data.id,
      playerName: data.name,
      status: data.status
    });

    return {
      isPlayer: true,
      status: data.status,
      playerId: data.id
    };
  } catch (error) {
    console.error("Erro ao verificar status do jogador:", error);
    return { isPlayer: false };
  }
}

/**
 * Obtém a categoria de um jogador baseado no user_id
 * @param clubId ID do clube
 * @param userId ID do usuário
 * @returns ID da categoria ou null se não encontrado
 */
export async function getPlayerCategoryByUserId(clubId: number, userId: string): Promise<number | null> {
  try {
    // Primeiro, obter o jogador pelo user_id
    const player = await getPlayerByUserId(clubId, userId);
    if (!player) {
      return null;
    }

    // Depois, obter a categoria do jogador
    const { data, error } = await supabase
      .from("player_categories")
      .select("category_id")
      .eq("club_id", clubId as any)
      .eq("player_id", player.id as any)
      .single();

    if (error) {
      console.log(`Categoria não encontrada para o jogador ${player.id}:`, error.message);
      return null;
    }

    return data.category_id;
  } catch (error) {
    console.error("Erro ao buscar categoria do jogador por user_id:", error);
    return null;
  }
}

// Tipos
export type Player = {
  id: string;
  club_id: number;
  name: string;
  position: string;
  age: number;
  number: number;
  registration_number?: string; // Número de cadastro único
  nationality?: string;
  height?: number;
  weight?: number;
  birthdate?: string;
  birthplace?: string;
  status: string;
  image?: string;
  entry_date?: string;
  exit_date?: string;
  championship_registration?: string;
  nickname?: string;
  professional_status?: string;
  rg_number?: string;
  cpf_number?: string;
  father_name?: string;
  mother_name?: string;
  referred_by?: string;
  phone?: string;
  address?: string;
  zip_code?: string;
  city?: string;
  state?: string;
  email?: string;
  contract_end_date?: string;
  loan_end_date?: string;
  loan_club_name?: string;
  observation?: string;
  // Campos do sistema de transferências
  global_player_id?: string;
  is_transfer?: boolean;
  transfer_id?: number;
  stats?: {
    games: number;
    goals: number;
    assists: number;
    yellowCards: number;
    redCards: number;
    minutes: number;
  }
};

// Funções para Players
export async function getPlayers(
  clubId: number,
  userId?: string,
  options?: { includeInactive?: boolean; includeLoaned?: boolean; includeScheduled?: boolean }
): Promise<Player[]> {
  // Opções padrão
  const includeInactive = options?.includeInactive ?? true;
  const includeLoaned = options?.includeLoaned ?? true;
  const includeScheduled = options?.includeScheduled ?? false;

  // Função para filtrar jogadores com base nas opções
  const filterPlayers = (players: Player[]): Player[] => {
    return players.filter(player => {
      // Filtrar jogadores inativos se necessário
      if (!includeInactive && player.status === "inativo") {
        return false;
      }

      // Filtrar jogadores emprestados se necessário
      if (!includeLoaned && player.status === "emprestado") {
        return false;
      }

      // Excluir jogadores em pré cadastro agendado
      if (!includeScheduled && player.status === "jogador agendado") {
        return false;
      }

      return true;
    });
  };

  // Se userId não for fornecido, não verificamos permissões (para compatibilidade)
  if (!userId) {
    const { data, error } = await supabase
      .from("players")
      .select("*")
      .eq("club_id", clubId as any);

    if (error) {
      throw new Error(`Erro ao buscar jogadores: ${error.message}`);
    }

    return filterPlayers(data as unknown as Player[]);
  }

  // Com userId, verificamos permissões
  return withPermission(
    clubId,
    userId,
    PLAYER_PERMISSIONS.VIEW,
    async () => {
      const { data, error } = await supabase
        .from("players")
        .select("*")
        .eq("club_id", clubId as any);

      if (error) {
        throw new Error(`Erro ao buscar jogadores: ${error.message}`);
      }

      return filterPlayers(data as unknown as Player[]);
    }
  );
}

export async function getPlayerById(clubId: number, id: string, userId?: string): Promise<Player> {
  // Se userId não for fornecido, não verificamos permissões (para compatibilidade)
  if (!userId) {
    const { data, error } = await supabase
      .from("players")
      .select("*")
      .eq("club_id", clubId as any)
      .eq("id", id as any)
      .single();

    if (error) {
      throw new Error(`Erro ao buscar jogador: ${error.message}`);
    }

    return data as unknown as Player;
  }

  // Com userId, verificamos permissões
  return withPermission(
    clubId,
    userId,
    PLAYER_PERMISSIONS.VIEW,
    async () => {
      const { data, error } = await supabase
        .from("players")
        .select("*")
        .eq("club_id", clubId as any)
        .eq("id", id as any)
        .single();

      if (error) {
        throw new Error(`Erro ao buscar jogador: ${error.message}`);
      }

      return data as unknown as Player;
    }
  );
}

export async function getPlayersByIds(clubId: number, ids: Array<string | number>): Promise<Player[]> {
  if (ids.length === 0) return [];
  try {
    const { data, error } = await supabase
      .from('players')
      .select('*')
      .eq('club_id', clubId as any)
      .in('id', ids);

    if (error) {
      throw new Error(`Erro ao buscar jogadores: ${error.message}`);
    }

    return (data as unknown as Player[]) || [];
  } catch (error: any) {
    console.error('Erro ao buscar jogadores por IDs:', error);
    throw new Error(error.message || 'Erro ao buscar jogadores');
  }
}

export async function createPlayer(clubId: number, player: Omit<Player, "id">, userId?: string): Promise<Player> {
  console.log("Iniciando criação de jogador:", { clubId, playerName: player.name, userId });

  // Função auxiliar para gerar número de cadastro único
  async function generateUniqueRegistrationNumber(): Promise<string> {
    // Chamar a função SQL para gerar um número de cadastro
    const { data: genData, error: genError } = await supabase
      .rpc('generate_registration_number');

    if (genError) {
      console.error("Erro ao gerar número de cadastro:", genError);
      throw new Error(`Erro ao gerar número de cadastro: ${genError.message}`);
    }

    let registrationNumber = genData;

    // Verificar se o número já existe para este clube
    let isUnique = false;
    let attempts = 0;

    while (!isUnique && attempts < 5) {
      const { data: checkData, error: checkError } = await supabase
        .from("players")
        .select("id")
        .eq("club_id", clubId)
        .eq("registration_number", registrationNumber)
        .limit(1);

      if (checkError) {
        console.error("Erro ao verificar número de cadastro:", checkError);
        break;
      }

      if (!checkData || checkData.length === 0) {
        isUnique = true;
      } else {
        // Gerar novo número
        const { data: newGenData } = await supabase.rpc('generate_registration_number');
        registrationNumber = newGenData;
        attempts++;
      }
    }

    return registrationNumber;
  }

  // Função auxiliar para inserir jogador no banco de dados
  async function insertPlayer(playerData: any): Promise<Player> {
    console.log("Inserindo jogador no banco de dados:", {
      playerName: playerData.name,
      playerId: playerData.id,
      registrationNumber: playerData.registration_number,
      clubId: playerData.club_id
    });

    try {
      // Garantir que o clubId seja um número válido
      if (!clubId || isNaN(Number(clubId)) || Number(clubId) <= 0) {
        console.error(`[ERROR] club_id inválido: ${clubId}`);
        throw new Error(`Invalid club_id: ${clubId}`);
      }

      // Criar cliente Supabase com club_id nos headers
      const supabaseWithClubId = getSupabaseClientWithClubId(clubId);
      console.log("[DEBUG] Cliente Supabase criado com club_id:", clubId);

      // Verificar se o header está sendo configurado corretamente
      console.log("[DEBUG] Headers configurados:", {
        'x-club-id': clubId.toString()
      });

      // Inserir o jogador no banco de dados
      const { data, error } = await supabaseWithClubId
        .from("players")
        .insert(playerData as any)
        .select()
        .single();

      if (error) {
        console.error("[ERROR] Erro ao inserir jogador:", error);
        throw new Error(`Erro ao criar jogador: ${error.message}`);
      }

      console.log("[DEBUG] Jogador criado com sucesso:", {
        id: data.id,
        name: data.name,
        registrationNumber: data.registration_number
      });

      // NOVO: Registrar automaticamente no sistema global se tiver CPF
      if (data.cpf_number) {
        try {
          console.log("[DEBUG] Registrando jogador no sistema global...");

          // Verificar se já existe um jogador global com este CPF
          const { data: existingGlobal, error: checkError } = await supabase
            .from('global_players')
            .select('id')
            .eq('cpf_number', data.cpf_number)
            .single();

          let globalPlayerId: string;

          if (checkError && checkError.code === 'PGRST116') {
            // Não existe, criar novo jogador global
            const { data: newGlobal, error: createError } = await supabase
              .from('global_players')
              .insert({
                cpf_number: data.cpf_number,
                name: data.name,
                birthdate: data.birthdate ? new Date(data.birthdate) : null,
                birthplace: data.birthplace,
                nationality: data.nationality || 'Brasil',
                rg_number: data.rg_number,
                father_name: data.father_name,
                mother_name: data.mother_name,
                phone: data.phone,
                email: data.email,
                height: data.height,
                weight: data.weight
              })
              .select('id')
              .single();

            if (createError) {
              console.error("[ERROR] Erro ao criar jogador global:", createError);
            } else {
              globalPlayerId = newGlobal.id;
              console.log("[DEBUG] Jogador global criado:", globalPlayerId);
            }
          } else if (!checkError) {
            // Já existe, usar o existente
            globalPlayerId = existingGlobal.id;
            console.log("[DEBUG] Jogador global já existe:", globalPlayerId);
          }

          // Atualizar o jogador com o global_player_id
          if (globalPlayerId!) {
            await supabaseWithClubId
              .from('players')
              .update({ global_player_id: globalPlayerId })
              .eq('id', data.id);

            console.log("[DEBUG] Jogador vinculado ao sistema global");
          }

        } catch (globalError) {
          console.error("[ERROR] Erro ao registrar no sistema global:", globalError);
          // Não falhar a criação do jogador por causa disso
        }
      }

      return data as unknown as Player;
    } catch (err: any) {
      console.error("[ERROR] Exceção ao inserir jogador:", err);
      throw err;
    }
  }

  // Preparar dados do jogador
  let registrationNumber = player.registration_number;
  if (!registrationNumber) {
    registrationNumber = await generateUniqueRegistrationNumber();
  }

  const playerId = uuidv4();
  const newPlayer = {
    ...player,
    id: playerId,
    club_id: clubId,
    registration_number: registrationNumber
  };

  // Se userId não for fornecido, não verificamos permissões (para compatibilidade)
  if (!userId) {
    return insertPlayer(newPlayer);
  }

  // Com userId, verificamos permissões e registramos no log de auditoria
  return withPermission(
    clubId,
    userId,
    PLAYER_PERMISSIONS.CREATE,
    async () => {
      return withAuditLog(
        clubId,
        userId,
        "player.create",
        { player_name: player.name },
        async () => {
          return insertPlayer(newPlayer);
        }
      );
    }
  );
}

export async function updatePlayer(clubId: number, id: string, player: Partial<Player>, userId?: string): Promise<Player> {
  // VALIDAÇÃO: Impedir mudança de status de jogadores transferidos
  if (player.status && player.status !== 'inativo') {
    try {
      const { data: validation, error: validationError } = await supabase.rpc('validate_player_status_change', {
        p_player_id: id,
        p_club_id: clubId,
        p_new_status: player.status
      });

      if (validationError) {
        console.error('Erro na validação de status:', validationError);
      } else if (validation && validation.length > 0) {
        const result = validation[0];
        if (!result.can_change) {
          throw new Error(result.error_message);
        }
      }
    } catch (error: any) {
      if (error.message.includes('Não é possível ativar')) {
        throw error;
      }
      console.warn('Erro ao verificar status de transferência:', error);
    }
  }

  // Se userId não for fornecido, não verificamos permissões (para compatibilidade)
  if (!userId) {
    console.log(`Atualizando jogador ${id} do clube ${clubId} sem verificação de permissões`);
    console.log(`Dados a serem atualizados:`, player);

    // Verificar se estamos atualizando a data de fim de contrato
    const contractEndDateChanged = player.contract_end_date !== undefined;

    // Verificar se o status está sendo alterado para 'inativo'
    const statusChangedToInactive = player.status === 'inativo';

    // Primeiro, verificar se o jogador existe usando o cliente com club_id
    const supabaseWithClubId = getSupabaseClientWithClubId(clubId);
    const { data: existingPlayer, error: checkError } = await supabaseWithClubId
      .from("players")
      .select("id, club_id, user_id, status")
      .eq("club_id", clubId)
      .eq("id", id)
      .single();

    const statusRestored = existingPlayer && existingPlayer.status === 'inativo' && player.status && player.status !== 'inativo';

    if (checkError) {
      console.error(`Erro ao verificar existência do jogador ${id}:`, checkError);
      throw new Error(`Jogador não encontrado ou sem acesso: ${checkError.message}`);
    }

    console.log(`Jogador encontrado:`, existingPlayer);

    // Atualizar o jogador
    const { data, error } = await supabaseWithClubId
      .from("players")
      .update({ ...player, club_id: clubId } as any)
      .eq("club_id", clubId)
      .eq("id", id)
      .select()
      .single();

    if (error) {
      console.error(`Erro detalhado ao atualizar jogador ${id}:`, {
        error,
        clubId,
        playerId: id,
        updateData: player
      });
      throw new Error(`Erro ao atualizar jogador: ${error.message}`);
    }

    // Se o status foi alterado para 'inativo', executar limpeza de vinculações
    if (statusChangedToInactive) {
      try {
        console.log(`Jogador ${id} alterado para status 'inativo'. Executando limpeza de vinculações...`);

        // Chamar a função SQL para remover vinculações
        const { error: cleanupError } = await supabase
          .rpc('remove_player_associations', {
            p_club_id: clubId,
            p_player_id: id
          });

        if (cleanupError) {
          console.error("Erro ao executar limpeza de vinculações:", cleanupError);
          // Não lançamos erro aqui para não interromper o fluxo principal
        } else {
          console.log(`Vinculações removidas com sucesso para o jogador ${id}`);
        }
      } catch (cleanupError) {
        console.error("Erro ao executar limpeza de vinculações:", cleanupError);
      }
    }

    // Se o jogador estava inativo e voltou a ficar ativo, restaurar permissões
    if (statusRestored) {
      try {
        const { error: restoreError } = await supabase
          .rpc('restore_player_permissions', {
            p_club_id: clubId,
            p_player_id: id,
          });

        if (restoreError) {
          console.error('Erro ao restaurar permissões:', restoreError);
        } else {
          console.log(`Permissões restauradas para o jogador ${id}`);
        }
      } catch (restoreError) {
        console.error('Erro ao restaurar permissões:', restoreError);
      }
    }

    // Se a data de fim de contrato foi alterada e o jogador tem um usuário associado
    if (contractEndDateChanged && data.user_id) {
      try {
        // Atualizar a data de expiração na tabela player_accounts
        const { error: accountError } = await supabase
          .from("player_accounts")
          .upsert({
            club_id: clubId,
            player_id: id,
            user_id: data.user_id,
            expires_at: player.contract_end_date
          }, {
            onConflict: 'player_id, club_id'
          });

        if (accountError) {
          console.error("Erro ao atualizar expiração da conta do jogador:", accountError);
          // Não lançamos erro aqui para não interromper o fluxo principal
        }
      } catch (accountUpdateError) {
        console.error("Erro ao atualizar expiração da conta do jogador:", accountUpdateError);
      }
    }

    // NOVO: Sincronizar dados globais se o jogador tiver global_player_id
    if (data.global_player_id) {
      try {
        console.log(`Sincronizando dados globais para jogador ${id}...`);

        const globalUpdateData: any = {};

        // Campos que devem ser sincronizados na tabela global
        if (player.name) globalUpdateData.name = player.name;
        if (player.birthdate) globalUpdateData.birthdate = player.birthdate;
        if (player.birthplace) globalUpdateData.birthplace = player.birthplace;
        if (player.nationality) globalUpdateData.nationality = player.nationality;
        if (player.rg_number) globalUpdateData.rg_number = player.rg_number;
        if (player.father_name) globalUpdateData.father_name = player.father_name;
        if (player.mother_name) globalUpdateData.mother_name = player.mother_name;
        if (player.phone) globalUpdateData.phone = player.phone;
        if (player.email) globalUpdateData.email = player.email;
        if (player.height) globalUpdateData.height = player.height;
        if (player.weight) globalUpdateData.weight = player.weight;

        // Atualizar apenas se há dados para sincronizar
        if (Object.keys(globalUpdateData).length > 0) {
          const { error: globalError } = await supabase
            .from('global_players')
            .update(globalUpdateData)
            .eq('id', data.global_player_id);

          if (globalError) {
            console.error('Erro ao sincronizar dados globais:', globalError);
          } else {
            console.log('Dados globais sincronizados com sucesso');
          }
        }
      } catch (globalError) {
        console.error('Erro na sincronização global:', globalError);
        // Não falhar a atualização por causa disso
      }
    }

    return data as unknown as Player;
  }

  try {
    // Verificar se o jogador pertence ao usuário (para jogadores editando seu próprio perfil)
    const isOwnProfile = await isPlayerOwnedByUser(clubId, id, userId);

    let permissionRequired = isOwnProfile ? PLAYER_PERMISSIONS.EDIT_OWN : PLAYER_PERMISSIONS.EDIT;
    if (player.status !== undefined) {
      permissionRequired = PLAYER_PERMISSIONS.STATUS.UPDATE;
    }

    // Com userId, verificamos permissões e registramos no log de auditoria
    return withPermission(
      clubId,
      userId,
      permissionRequired,
      async () => {
        return withAuditLog(
          clubId,
          userId,
          "player.update",
          { player_id: id, updated_fields: Object.keys(player), is_own_profile: isOwnProfile },
          async () => {
            // Verificar se estamos atualizando a data de fim de contrato
            const contractEndDateChanged = player.contract_end_date !== undefined;

            // Verificar se o status está sendo alterado para 'inativo'
            const statusChangedToInactive = player.status === 'inativo';

            // Usar cliente Supabase com club_id nos headers para contornar problemas de RLS
            const supabaseWithClubId = getSupabaseClientWithClubId(clubId);

            const { data: currentPlayer } = await supabaseWithClubId
              .from('players')
              .select('status')
              .eq('club_id', clubId)
              .eq('id', id)
              .single();
            const statusRestored = currentPlayer && currentPlayer.status === 'inativo' && player.status && player.status !== 'inativo';

            // Atualizar o jogador
            const { data, error } = await supabaseWithClubId
              .from("players")
              .update({ ...player, club_id: clubId } as any)
              .eq("club_id", clubId)
              .eq("id", id)
              .select()
              .single();

            if (error) {
              throw new Error(`Erro ao atualizar jogador: ${error.message}`);
            }

            // Se o status foi alterado para 'inativo', executar limpeza de vinculações
            if (statusChangedToInactive) {
              try {
                console.log(`Jogador ${id} alterado para status 'inativo'. Executando limpeza de vinculações...`);

                // Chamar a função SQL para remover vinculações
                const { error: cleanupError } = await supabase
                  .rpc('remove_player_associations', {
                    p_club_id: clubId,
                    p_player_id: id
                  });

                if (cleanupError) {
                  console.error("Erro ao executar limpeza de vinculações:", cleanupError);
                  // Não lançamos erro aqui para não interromper o fluxo principal
                } else {
                  console.log(`Vinculações removidas com sucesso para o jogador ${id}`);
                }
              } catch (cleanupError) {
                console.error("Erro ao executar limpeza de vinculações:", cleanupError);
              }
            }

            // Se o jogador estava inativo e volta a ficar ativo, restaurar permissões
            if (statusRestored) {
              try {
                const { error: restoreError } = await supabase.rpc('restore_player_permissions', {
                  p_club_id: clubId,
                  p_player_id: id,
                });

                if (restoreError) {
                  console.error('Erro ao restaurar permissões:', restoreError);
                } else {
                  console.log(`Permissões restauradas para o jogador ${id}`);
                }
              } catch (restoreError) {
                console.error('Erro ao restaurar permissões:', restoreError);
              }
            }

            // Se a data de fim de contrato foi alterada e o jogador tem um usuário associado
            if (contractEndDateChanged && data.user_id) {
              try {
                // Atualizar a data de expiração na tabela player_accounts
                const { error: accountError } = await supabase
                  .from("player_accounts")
                  .upsert({
                    club_id: clubId,
                    player_id: id,
                    user_id: data.user_id,
                    expires_at: player.contract_end_date
                  }, {
                    onConflict: 'player_id, club_id'
                  });

                if (accountError) {
                  console.error("Erro ao atualizar expiração da conta do jogador:", accountError);
                  // Não lançamos erro aqui para não interromper o fluxo principal
                }
              } catch (accountUpdateError) {
                console.error("Erro ao atualizar expiração da conta do jogador:", accountUpdateError);
              }
            }

            return data as unknown as Player;
          }
        );
      }
    );
  } catch (error: any) {
    console.error("Erro ao verificar permissões:", error);
    throw new Error(`Erro ao atualizar jogador: ${error.message}`);
  }
}

/**
 * Remove todas as dependências de um jogador antes da exclusão
 * @param clubId ID do clube
 * @param playerId ID do jogador
 */
async function removePlayerDependencies(clubId: number, playerId: string): Promise<void> {
  console.log(`Removendo dependências do jogador ${playerId} do clube ${clubId}`);
  await ensureAuth();

  // Tabelas que têm club_id e player_id
  const tablesWithClubId = [
    'medical_records',
    'gols',
    'rehab_sessions',
    'player_salaries',
    'match_events',
    'match_squad',
    'player_accommodations',
    'player_categories',
    'player_documents',
    'player_accounts',
    'callup_players',
    'player_evaluations',
    'training_players',
    'player_match_statistics',
    'match_player_minutes',
    'medical_appointments',
    'medical_exams',
    'medical_prescriptions',
    'player_evaluation_invitations'
  ];

  // Remove dependências de todas as tabelas relacionadas
  for (const table of tablesWithClubId) {
    try {
      console.log(`Limpando tabela ${table} para jogador ${playerId}`);
      const { error, count } = await supabase
        .from(table)
        .delete()
        .eq("club_id", clubId as any)
        .eq("player_id", playerId as any);

      if (error && !error.message.includes('does not exist') && !error.message.includes('column') && !error.message.includes('relation')) {
        console.warn(`Aviso ao limpar ${table}:`, error.message);
      } else {
        console.log(`Tabela ${table} limpa com sucesso`);
      }
    } catch (err) {
      console.warn(`Erro ao limpar dependências da tabela ${table}:`, err);
    }
  }

  // Tratar casos especiais onde o campo pode ter nome diferente
  try {
    // match_events pode ter player_in_id também
    const { error: matchEventsError } = await supabase
      .from("match_events")
      .delete()
      .eq("club_id", clubId as any)
      .eq("player_in_id", playerId as any);

    if (matchEventsError && !matchEventsError.message.includes('does not exist') && !matchEventsError.message.includes('column') && !matchEventsError.message.includes('relation')) {
      console.warn('Aviso ao limpar match_events (player_in_id):', matchEventsError.message);
    }
  } catch (err) {
    console.warn('Erro ao limpar match_events (player_in_id):', err);
  }

  // Tabelas que podem não ter club_id - remover apenas por player_id
  const tablesWithoutClubId = [];

  for (const table of tablesWithoutClubId) {
    try {
      const { error } = await supabase
        .from(table)
        .delete()
        .eq("player_id", playerId as any);

      if (error && !error.message.includes('does not exist') && !error.message.includes('column') && !error.message.includes('relation')) {
        console.warn(`Aviso ao limpar ${table} (sem club_id):`, error.message);
      }
    } catch (err) {
      console.warn(`Erro ao limpar dependências da tabela ${table} (sem club_id):`, err);
    }
  }
}

export async function deletePlayer(clubId: number, id: string, userId?: string): Promise<boolean> {
  // Se userId não for fornecido, não verificamos permissões (para compatibilidade)
  if (!userId) {
    try {
      console.log(`Iniciando remoção do jogador ${id} do clube ${clubId}`);

      // Primeiro, obter o user_id do jogador antes de excluí-lo
      const { data: playerData, error: playerError } = await supabase
        .from("players")
        .select("user_id")
        .eq("club_id", clubId as any)
        .eq("id", id as any)
        .single();

      if (playerError) {
        console.error(`Erro ao buscar dados do jogador ${id}:`, playerError);
        throw new Error(`Erro ao buscar dados do jogador: ${playerError.message}`);
      }

      const playerUserId = playerData?.user_id;

      // Remover todas as dependências
      await removePlayerDependencies(clubId, id);
      console.log(`Dependências removidas para o jogador ${id}`);

      // Excluir o jogador
      const { error } = await supabase
        .from("players")
        .delete()
        .eq("club_id", clubId as any)
        .eq("id", id as any);

      if (error) {
        console.error(`Erro ao deletar jogador ${id}:`, error);
        throw new Error(`Erro ao deletar jogador: ${error.message}`);
      }

      // Se o jogador tinha uma conta de usuário, excluí-la completamente
      if (playerUserId) {
        try {
          console.log(`Excluindo conta do usuário ${playerUserId} associada ao jogador ${id}`);

          // Importar a função de exclusão de usuário
          const { deleteUser } = await import('./userManagement');
          const result = await deleteUser(playerUserId);

          if (result.success) {
            console.log(`Conta do usuário ${playerUserId} excluída com sucesso`);
          } else {
            console.warn(`Falha ao excluir conta do usuário ${playerUserId}: ${result.message}`);
          }
        } catch (userDeleteError) {
          console.warn(`Erro ao excluir conta do usuário ${playerUserId}:`, userDeleteError);
          // Não falhar a operação principal se a exclusão do usuário falhar
        }
      }

      console.log(`Jogador ${id} removido com sucesso`);
      return true;
    } catch (error: any) {
      console.error("Erro ao deletar jogador:", error);
      throw new Error(`Erro ao deletar jogador: ${error.message}`);
    }
  }

  // Com userId, verificamos permissões e registramos no log de auditoria
  return withPermission(
    clubId,
    userId,
    PLAYER_PERMISSIONS.DELETE,
    async () => {
      return withAuditLog(
        clubId,
        userId,
        "player.delete",
        { player_id: id },
        async () => {
          try {
            console.log(`Iniciando remoção do jogador ${id} do clube ${clubId} (com permissões)`);

            // Primeiro, obter o user_id do jogador antes de excluí-lo
            const { data: playerData, error: playerError } = await supabase
              .from("players")
              .select("user_id")
              .eq("club_id", clubId as any)
              .eq("id", id as any)
              .single();

            if (playerError) {
              console.error(`Erro ao buscar dados do jogador ${id}:`, playerError);
              throw new Error(`Erro ao buscar dados do jogador: ${playerError.message}`);
            }

            const playerUserId = playerData?.user_id;

            // Remover todas as dependências
            await removePlayerDependencies(clubId, id);
            console.log(`Dependências removidas para o jogador ${id}`);

            // Excluir o jogador
            const { error } = await supabase
              .from("players")
              .delete()
              .eq("club_id", clubId as any)
              .eq("id", id as any);

            if (error) {
              throw new Error(`Erro ao deletar jogador: ${error.message}`);
            }

            // Se o jogador tinha uma conta de usuário, excluí-la completamente
            if (playerUserId) {
              try {
                console.log(`Excluindo conta do usuário ${playerUserId} associada ao jogador ${id}`);

                // Importar a função de exclusão de usuário
                const { deleteUser } = await import('./userManagement');
                const result = await deleteUser(playerUserId);

                if (result.success) {
                  console.log(`Conta do usuário ${playerUserId} excluída com sucesso`);
                } else {
                  console.warn(`Falha ao excluir conta do usuário ${playerUserId}: ${result.message}`);
                }
              } catch (userDeleteError) {
                console.warn(`Erro ao excluir conta do usuário ${playerUserId}:`, userDeleteError);
                // Não falhar a operação principal se a exclusão do usuário falhar
              }
            }

            console.log(`Jogador ${id} removido com sucesso (com permissões)`);
            return true;
          } catch (error: any) {
            console.error("Erro ao deletar jogador:", error);
            throw new Error(`Erro ao deletar jogador: ${error.message}`);
          }
        }
      );
    }
  );
}

/**
 * Faz upload da foto do jogador e atualiza o campo image na tabela players
 * @param clubId ID do clube
 * @param playerId ID do jogador
 * @param file Arquivo de imagem
 * @returns URL da imagem
 */
export async function uploadPlayerPhoto(clubId: number, playerId: string, file: File, userId?: string): Promise<string> {
  // Se userId não for fornecido, não verificamos permissões (para compatibilidade)
  if (!userId) {
    try {
      console.log("[DEBUG uploadPlayerPhoto sem userId] Iniciando upload para playerId:", playerId);

      // Fazer upload da imagem usando a função uploadProfileImage
      const imageUrl = await uploadProfileImage(playerId, file);
      console.log("[DEBUG uploadPlayerPhoto sem userId] Upload concluído. URL:", imageUrl);

      // Atualizar o campo image na tabela players
      const { error } = await supabase
        .from("players")
        .update({ image: imageUrl } as any)
        .eq("club_id", clubId as any)
        .eq("id", playerId as any);

      if (error) {
        console.error("[DEBUG uploadPlayerPhoto sem userId] Erro ao atualizar campo image:", error);
        throw new Error(`Erro ao atualizar foto do jogador: ${error.message}`);
      }

      console.log("[DEBUG uploadPlayerPhoto sem userId] Campo image atualizado com sucesso");
      return imageUrl;
    } catch (error: any) {
      console.error("Erro ao fazer upload da foto do jogador:", error);
      throw new Error(error.message || "Erro ao fazer upload da foto do jogador");
    }
  }

  try {
    // Verificar se o jogador pertence ao usuário (para jogadores editando seu próprio perfil)
    const isOwnProfile = await isPlayerOwnedByUser(clubId, playerId, userId);

    // Se for o próprio jogador, usar permissão EDIT_OWN, caso contrário, usar EDIT
    const permissionRequired = isOwnProfile ? PLAYER_PERMISSIONS.EDIT_OWN : PLAYER_PERMISSIONS.EDIT;

    // Com userId, verificamos permissões e registramos no log de auditoria
    return withPermission(
      clubId,
      userId,
      permissionRequired,
      async () => {
        return withAuditLog(
          clubId,
          userId,
          "player.upload_photo",
          { player_id: playerId, file_name: file.name, file_size: file.size, is_own_profile: isOwnProfile },
          async () => {
            try {
              console.log("[DEBUG uploadPlayerPhoto] Iniciando upload para playerId:", playerId);

              // Fazer upload da imagem usando a função uploadProfileImage
              const imageUrl = await uploadProfileImage(playerId, file);
              console.log("[DEBUG uploadPlayerPhoto] Upload concluído. URL:", imageUrl);

              // Atualizar o campo image na tabela players
              const { error } = await supabase
                .from("players")
                .update({ image: imageUrl } as any)
                .eq("club_id", clubId as any)
                .eq("id", playerId as any);

              if (error) {
                console.error("[DEBUG uploadPlayerPhoto] Erro ao atualizar campo image:", error);
                throw new Error(`Erro ao atualizar foto do jogador: ${error.message}`);
              }

              console.log("[DEBUG uploadPlayerPhoto] Campo image atualizado com sucesso");
              return imageUrl;
            } catch (error: any) {
              console.error("Erro ao fazer upload da foto do jogador:", error);
              throw new Error(error.message || "Erro ao fazer upload da foto do jogador");
            }
          }
        );
      }
    );
  } catch (error: any) {
    console.error("Erro ao verificar permissões para upload de foto:", error);
    throw new Error(`Erro ao fazer upload da foto: ${error.message}`);
  }
}