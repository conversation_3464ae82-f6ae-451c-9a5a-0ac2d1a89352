import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";
import { createPlayerEvaluationInvitation, sendEvaluationInvitationEmail } from "@/api/playerEvaluationInvitations";
import { validateCPF } from "@/api/external";
import { getPlayerByCpf } from "@/api/players";
import { supabase } from "@/integrations/supabase/client";

interface CreateEvaluationInvitationFormProps {
  onSuccess?: () => void;
}

export function CreateEvaluationInvitationForm({ onSuccess }: CreateEvaluationInvitationFormProps) {
  const clubId = useCurrentClubId();
  const { user } = useUser();
  const { toast } = useToast();
  
  const [cpf, setCpf] = useState("");
  const [email, setEmail] = useState("");
  const [expirationDays, setExpirationDays] = useState(5);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [existingPlayerData, setExistingPlayerData] = useState<any>(null);
  const [showEditOptions, setShowEditOptions] = useState(false);
  
  // Função para buscar jogador por CPF
  const handleCpfChange = async (cpfValue: string) => {
    const cleanCpf = cpfValue.replace(/\D/g, '');
    setCpf(cleanCpf);
    
    if (cleanCpf.length === 11 && validateCPF(cleanCpf)) {
      try {
        // Buscar jogador existente por CPF
        const existingPlayer = await getPlayerByCpf(clubId, cleanCpf);

        if (existingPlayer) {
          setExistingPlayerData(existingPlayer);
          setEmail(existingPlayer.email || "");
          setShowEditOptions(true);
          toast({
            title: "Jogador encontrado",
            description: `Dados de ${existingPlayer.name} foram carregados. Você pode alterar as informações se necessário.`,
          });
        } else {
          setExistingPlayerData(null);
          setShowEditOptions(false);
        }
      } catch (err) {
        console.error("Erro ao buscar jogador por CPF:", err);
        setExistingPlayerData(null);
        setShowEditOptions(false);
      }
    } else {
      setExistingPlayerData(null);
      setShowEditOptions(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate inputs - CPF é obrigatório agora
    if (!cpf) {
      setError("O CPF é obrigatório");
      return;
    }
    
    if (!validateCPF(cpf)) {
      setError("CPF inválido");
      return;
    }
    
    if (!email) {
      setError("O email é obrigatório");
      return;
    }
    
    if (!validateEmail(email)) {
      setError("Email inválido");
      return;
    }
    
    if (!user?.id) {
      setError("Usuário não autenticado");
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      
      // Create invitation - CPF agora é obrigatório
      const invitation = await createPlayerEvaluationInvitation(
        clubId,
        email,
        cpf,
        user.id,
        expirationDays
      );
      
      // Get club name
      const { data: clubData } = await supabase
        .from("club_info")
        .select("name")
        .eq("id", clubId)
        .single();
      
      const clubName = clubData?.name || "Game Day Nexus";
      
      // Send invitation email
      await sendEvaluationInvitationEmail(email, invitation.token, clubName);
      
      const successMessage = existingPlayerData 
        ? `Convite enviado com dados pré-preenchidos para ${existingPlayerData.name}`
        : "Convite para pré cadastro enviado com sucesso";
      
      toast({
        title: "Sucesso",
        description: successMessage,
      });
      
      // Reset form
      setCpf("");
      setEmail("");
      setExpirationDays(5);
      setExistingPlayerData(null);
      setShowEditOptions(false);
      
      // Call onSuccess callback
      if (onSuccess) {
        onSuccess();
      }
    } catch (err: any) {
      console.error("Erro ao criar convite para pré cadastro:", err);
      setError(err.message || "Erro ao criar convite para pré cadastro");
      toast({
        title: "Erro",
        description: err.message || "Erro ao criar convite para pré cadastro",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Criar Convite para pré cadastro</CardTitle>
        <CardDescription>
          Gere um link único para que um atleta ou empresário possa se cadastrar para pré cadastro.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="cpf">CPF*</Label>
            <Input
              id="cpf"
              value={cpf}
              onChange={(e) => handleCpfChange(e.target.value)}
              placeholder="CPF do atleta (obrigatório)"
              maxLength={11}
              required
            />
            <p className="text-xs text-muted-foreground">
              Digite o CPF do atleta. Se já estiver cadastrado, os dados serão carregados automaticamente.
            </p>
          </div>

          {existingPlayerData && (
            <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
              <p className="text-sm text-blue-800 font-medium">
                Atleta encontrado: {existingPlayerData.name}
              </p>
              <p className="text-xs text-blue-600">
                Os dados existentes foram carregados. Você pode alterar as informações abaixo se necessário.
              </p>
            </div>
          )}
          
          <div className="space-y-2">
            <Label htmlFor="email">Email*</Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Email do atleta ou empresário"
              required
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="expirationDays">Dias para expirar o link*</Label>
            <Input
              id="expirationDays"
              type="number"
              min={1}
              max={30}
              value={expirationDays}
              onChange={(e) => setExpirationDays(parseInt(e.target.value))}
              required
            />
          </div>
          
          {error && (
            <div className="text-sm font-medium text-destructive">{error}</div>
          )}
        </form>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={() => {
          setCpf("");
          setEmail("");
          setExpirationDays(5);
          setExistingPlayerData(null);
          setShowEditOptions(false);
          setError(null);
        }}>
          Limpar
        </Button>
        <Button onClick={handleSubmit} disabled={loading}>
          {loading ? "Enviando..." : "Enviar Convite"}
        </Button>
      </CardFooter>
    </Card>
  );
}

// Helper function to validate email
function validateEmail(email: string): boolean {
  const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return re.test(email);
}