import { useState, useEffect, useCallback } from "react";
import { Download, FileText, Loader2, Printer } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { getActiveFormTemplates, generateFormTemplatePDFHtml, ClubFormTemplate } from "@/api/clubFormTemplates";
import { getPublicClubInfo } from "@/api/api";
import { handleFileAction, shouldPrintFile } from "@/utils/printUtils";

interface FormTemplateDownloadLinksProps {
  clubId: number;
  formType: 'pre_registration' | 'housing' | 'liability_waiver' | 'liability_waiver_minor' | 'liability_waiver_adult';
  // Nota: isMinor não é mais usado para filtrar, mas mantido para compatibilidade com chamadas existentes
  label: string;
  isMinor?: boolean;
}

export function FormTemplateDownloadLinks({
  clubId,
  formType,
  label,
  isMinor = false
}: FormTemplateDownloadLinksProps) {
  const { toast } = useToast();
  const [templates, setTemplates] = useState<ClubFormTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [downloadingId, setDownloadingId] = useState<number | null>(null);

  const loadTemplates = useCallback(async () => {
    try {
      setLoading(true);
      
      // Se for um termo de isenção, buscar ambos os tipos (maior e menor de idade)
      const templateTypes = formType === 'liability_waiver' 
        ? ['liability_waiver_minor', 'liability_waiver_adult'] 
        : formType;
      
      // Buscar templates ativos
      const activeTemplates = await getActiveFormTemplates(clubId, templateTypes);
      
      // Se for um termo de isenção e não encontrou templates personalizados,
      // tenta carregar o template padrão
      if (formType === 'liability_waiver' && activeTemplates.length === 0) {
        try {
          const defaultTemplate = await getActiveFormTemplates(clubId, 'liability_waiver');
          setTemplates(defaultTemplate);
        } catch (e) {
          console.error("Error loading default liability waiver template:", e);
          setTemplates([]);
        }
      } else {
        setTemplates(activeTemplates);
      }
    } catch (err) {
      console.error("Error loading templates:", err);
      setTemplates([]);
    } finally {
      setLoading(false);
    }
  }, [clubId, formType]);

  useEffect(() => {
    loadTemplates();
  }, [loadTemplates]);

  const handleDownload = async (template: ClubFormTemplate) => {
    try {
      setDownloadingId(template.id);

      // Se o template tem um arquivo uploadado, usar função de impressão/download
      if (template.file_url) {
        const fileName = template.name.replace(/\s+/g, '_');
        handleFileAction(template.file_url, fileName);

        const isPrintable = shouldPrintFile(template.file_url);
        toast({
          title: isPrintable ? "Abrindo para impressão" : "Download iniciado",
          description: isPrintable 
            ? `O arquivo "${template.name}" foi aberto para impressão.`
            : `O arquivo "${template.name}" foi baixado com sucesso.`,
        });
        return;
      }

      // Caso contrário, gerar PDF do conteúdo HTML
      // Try to get club info for PDF generation
      let clubInfo = {};
      try {
        clubInfo = await getPublicClubInfo(clubId);
      } catch (clubError) {
        console.warn("Could not fetch club info, generating PDF without club header:", clubError);
        // Continue without club info - the PDF will be generated without the club header
      }

      const pdfBlob = await generateFormTemplatePDFHtml(template, clubInfo);

      // Create download link
      const url = URL.createObjectURL(pdfBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${template.name.replace(/\s+/g, '_')}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast({
        title: "Download iniciado",
        description: `O arquivo "${template.name}" foi baixado com sucesso.`,
      });
    } catch (err) {
      console.error("Error downloading template:", err);
      toast({
        title: "Erro no download",
        description: "Não foi possível baixar o arquivo. Tente novamente.",
        variant: "destructive",
      });
    } finally {
      setDownloadingId(null);
    }
  };

  // Show loading state
  if (loading) {
    return (
      <p className="text-xs text-muted-foreground flex items-center gap-1">
        <Loader2 className="h-3 w-3 animate-spin" />
        Carregando fichas disponíveis...
      </p>
    );
  }

  // If no templates found, show fallback message
  if (templates.length === 0) {
    return (
      <p className="text-xs text-muted-foreground">
        Nenhuma ficha de {label.toLowerCase()} disponível para download.
      </p>
    );
  }

  // Show single template as link (most common case)
  if (templates.length === 1) {
    const template = templates[0];
    const isDefaultTemplate = template.id < 0;

    return (
      <p className="text-xs text-muted-foreground">
        <button
          onClick={() => handleDownload(template)}
          className="text-blue-600 hover:underline inline-flex items-center gap-1"
          disabled={downloadingId === template.id}
        >
          {downloadingId === template.id ? (
            <>
              <Loader2 className="h-3 w-3 animate-spin" />
              Processando...
            </>
          ) : (
            <>
              {template.file_url && shouldPrintFile(template.file_url) ? (
                <Printer className="h-3 w-3" />
              ) : (
                <Download className="h-3 w-3" />
              )}
              Clique aqui para {template.file_url && shouldPrintFile(template.file_url) ? 'imprimir' : 'baixar'} {label.toLowerCase()}
              {isDefaultTemplate && " (ficha padrão)"}
            </>
          )}
        </button>
      </p>
    );
  }

  // Show multiple templates as buttons
  return (
    <div className="space-y-2">
      <p className="text-xs text-muted-foreground">
        Fichas de {label.toLowerCase()} disponíveis:
      </p>
      <div className="flex flex-wrap gap-2">
        {templates.map((template) => {
          const isDefaultTemplate = template.id < 0;
          return (
            <Button
              key={template.id}
              variant="outline"
              size="sm"
              onClick={() => handleDownload(template)}
              disabled={downloadingId === template.id}
              className="h-8 text-xs"
            >
              {downloadingId === template.id ? (
                <>
                  <Loader2 className="h-3 w-3 animate-spin mr-1" />
                  Baixando...
                </>
              ) : (
                <>
                  <FileText className="h-3 w-3 mr-1" />
                  {template.name}
                  {isDefaultTemplate && " (padrão)"}
                </>
              )}
            </Button>
          );
        })}
      </div>
    </div>
  );
}
