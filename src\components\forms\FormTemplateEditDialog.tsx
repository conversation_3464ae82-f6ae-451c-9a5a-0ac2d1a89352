import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { FileUploader } from "@/components/ui/file-uploader";
import { useToast } from "@/hooks/use-toast";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";
import { useFormTemplatesStore } from "@/store/useFormTemplatesStore";
import { uploadFormTemplateFile, deleteFormTemplateFile } from "@/api/formTemplateStorage";
import { ClubFormTemplate, FormType } from "@/api/clubFormTemplates";
import { Edit, FileText, Trash2, Upload, AlertTriangle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

const FORM_TYPE_OPTIONS = [
  { value: "pre_registration", label: "Ficha de Pré-cadastro" },
  { value: "housing", label: "Ficha de Moradia" },
  { value: "liability_waiver", label: "Termo de Responsabilidade" },
  { value: "liability_waiver_minor", label: "Termo de Responsabilidade - Menor de 18" },
  { value: "liability_waiver_adult", label: "Termo de Responsabilidade - Maior de 18" },
  { value: "custom", label: "Personalizado" },
];

interface FormTemplateEditDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  template: ClubFormTemplate | null;
  onSuccess?: () => void;
}

export function FormTemplateEditDialog({
  open,
  onOpenChange,
  template,
  onSuccess,
}: FormTemplateEditDialogProps) {
  const clubId = useCurrentClubId();
  const { user } = useUser();
  const { toast } = useToast();
  const { updateTemplate } = useFormTemplatesStore();

  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [formType, setFormType] = useState<FormType>("pre_registration");
  const [content, setContent] = useState("");
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [currentFileUrl, setCurrentFileUrl] = useState<string | null>(null);
  const [fileAction, setFileAction] = useState<'keep' | 'replace' | 'remove'>('keep');
  const [uploading, setUploading] = useState(false);

  // Reset form when template changes
  useEffect(() => {
    if (template) {
      setName(template.name);
      setDescription(template.description || "");
      setFormType(template.form_type);
      setContent(template.content);
      setCurrentFileUrl(template.file_url || null);
      setFileAction('keep');
      setSelectedFile(null);
    }
  }, [template]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!template || !name.trim()) {
      toast({
        title: "Erro",
        description: "O nome da ficha é obrigatório.",
        variant: "destructive",
      });
      return;
    }

    if (!user?.id) {
      toast({
        title: "Erro",
        description: "Usuário não autenticado.",
        variant: "destructive",
      });
      return;
    }

    setUploading(true);

    try {
      let newFileUrl = currentFileUrl;
      let newContent = content;

      // Handle file actions
      if (fileAction === 'remove' && currentFileUrl) {
        // Remove current file
        await deleteFormTemplateFile(currentFileUrl);
        newFileUrl = null;
        newContent = content; // Keep HTML content
      } else if (fileAction === 'replace' && selectedFile) {
        // Remove old file if exists
        if (currentFileUrl) {
          await deleteFormTemplateFile(currentFileUrl);
        }
        
        // Upload new file
        newFileUrl = await uploadFormTemplateFile(
          clubId,
          selectedFile,
          name
        );
        
        // Update content to reference new file
        newContent = `<p>Arquivo uploadado: ${selectedFile.name}</p><p>Para visualizar ou baixar, <a href="${newFileUrl}" target="_blank">clique aqui</a>.</p>`;
      }

      // Update template in database
      await updateTemplate(
        clubId,
        template.id,
        {
          name: name.trim(),
          description: description.trim() || undefined,
          content: newContent,
          form_type: formType,
          file_url: newFileUrl,
        },
        user.id
      );

      toast({
        title: "Ficha atualizada com sucesso",
        description: "As alterações foram salvas.",
      });

      onSuccess?.();
      onOpenChange(false);
    } catch (error) {
      console.error("Erro ao atualizar ficha:", error);
      toast({
        title: "Erro ao atualizar ficha",
        description: error instanceof Error ? error.message : "Ocorreu um erro inesperado.",
        variant: "destructive",
      });
    } finally {
      setUploading(false);
    }
  };

  const handleCancel = () => {
    if (!uploading) {
      onOpenChange(false);
    }
  };

  const isFileTemplate = !!currentFileUrl;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Edit className="h-5 w-5" />
            Editar Ficha
          </DialogTitle>
          <DialogDescription>
            {isFileTemplate 
              ? "Edite as informações da ficha e gerencie o arquivo anexado."
              : "Edite as informações da ficha HTML."
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Nome da Ficha *</Label>
            <Input
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="Ex: Ficha de Pré-cadastro Padrão do Clube"
              disabled={uploading}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Descrição</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Descreva brevemente o propósito desta ficha..."
              rows={3}
              disabled={uploading}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="formType">Tipo de Ficha *</Label>
            <Select
              value={formType}
              onValueChange={(value) => setFormType(value as FormType)}
              disabled={uploading}
            >
              <SelectTrigger>
                <SelectValue placeholder="Selecione o tipo de ficha" />
              </SelectTrigger>
              <SelectContent>
                {FORM_TYPE_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* File Management Section */}
          {isFileTemplate && (
            <div className="space-y-4 border rounded-lg p-4 bg-muted/50">
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                <Label className="text-sm font-medium">Gerenciar Arquivo</Label>
              </div>

              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="keep"
                    name="fileAction"
                    value="keep"
                    checked={fileAction === 'keep'}
                    onChange={(e) => setFileAction(e.target.value as any)}
                    disabled={uploading}
                  />
                  <Label htmlFor="keep" className="text-sm">
                    Manter arquivo atual
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="replace"
                    name="fileAction"
                    value="replace"
                    checked={fileAction === 'replace'}
                    onChange={(e) => setFileAction(e.target.value as any)}
                    disabled={uploading}
                  />
                  <Label htmlFor="replace" className="text-sm">
                    Substituir por novo arquivo
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="remove"
                    name="fileAction"
                    value="remove"
                    checked={fileAction === 'remove'}
                    onChange={(e) => setFileAction(e.target.value as any)}
                    disabled={uploading}
                  />
                  <Label htmlFor="remove" className="text-sm text-red-600">
                    Remover arquivo (converter para ficha HTML)
                  </Label>
                </div>
              </div>

              {fileAction === 'replace' && (
                <div className="space-y-2">
                  <Label>Novo Arquivo</Label>
                  <FileUploader
                    id="replacement-file"
                    accept=".pdf,.doc,.docx,.xls,.xlsx,.txt"
                    maxSize={10}
                    onFileSelect={setSelectedFile}
                    currentFile={selectedFile}
                    disabled={uploading}
                  />
                </div>
              )}

              {fileAction === 'remove' && (
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    Ao remover o arquivo, esta ficha se tornará uma ficha HTML editável.
                    O conteúdo HTML atual será mantido.
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}

          {/* Content editor for HTML templates or when removing file */}
          {(!isFileTemplate || fileAction === 'remove') && (
            <div className="space-y-2">
              <Label htmlFor="content">Conteúdo HTML</Label>
              <Textarea
                id="content"
                value={content}
                onChange={(e) => setContent(e.target.value)}
                placeholder="Digite o conteúdo HTML da ficha..."
                rows={8}
                disabled={uploading}
                className="font-mono text-sm"
              />
            </div>
          )}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={uploading}
            >
              Cancelar
            </Button>
            <Button 
              type="submit" 
              disabled={uploading || !name.trim() || (fileAction === 'replace' && !selectedFile)}
            >
              {uploading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Salvando...
                </>
              ) : (
                <>
                  <Edit className="h-4 w-4 mr-2" />
                  Salvar Alterações
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}