import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { FileUploader } from "@/components/ui/file-uploader";
import { useToast } from "@/hooks/use-toast";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";
import { useFormTemplatesStore } from "@/store/useFormTemplatesStore";
import { uploadFormTemplateFile } from "@/api/formTemplateStorage";
import { FormType } from "@/api/clubFormTemplates";
import { Upload, FileText } from "lucide-react";

const FORM_TYPE_OPTIONS = [
  { value: "pre_registration", label: "Ficha de Pré-cadastro" },
  { value: "housing", label: "Ficha de Moradia" },
  { value: "liability_waiver", label: "Termo de Responsabilidade" },
  { value: "liability_waiver_minor", label: "Termo de Responsabilidade - Menor de 18" },
  { value: "liability_waiver_adult", label: "Termo de Responsabilidade - Maior de 18" },
  { value: "custom", label: "Personalizado" },
];

interface FormTemplateUploadDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export function FormTemplateUploadDialog({
  open,
  onOpenChange,
  onSuccess,
}: FormTemplateUploadDialogProps) {
  const clubId = useCurrentClubId();
  const { user } = useUser();
  const { toast } = useToast();
  const { addTemplate } = useFormTemplatesStore();

  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [formType, setFormType] = useState<FormType>("pre_registration");
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!name.trim()) {
      toast({
        title: "Erro",
        description: "O nome da ficha é obrigatório.",
        variant: "destructive",
      });
      return;
    }

    if (!selectedFile) {
      toast({
        title: "Erro",
        description: "Selecione um arquivo para upload.",
        variant: "destructive",
      });
      return;
    }

    if (!user?.id) {
      toast({
        title: "Erro",
        description: "Usuário não autenticado.",
        variant: "destructive",
      });
      return;
    }

    setUploading(true);

    try {
      // 1. Fazer upload do arquivo
      const fileUrl = await uploadFormTemplateFile(
        clubId,
        selectedFile,
        name
      );

      // 2. Criar o template no banco de dados
      await addTemplate(
        clubId,
        {
          name: name.trim(),
          description: description.trim() || undefined,
          content: `<p>Arquivo uploadado: ${selectedFile.name}</p><p>Para visualizar ou baixar, <a href="${fileUrl}" target="_blank">clique aqui</a>.</p>`,
          form_type: formType,
          is_active: true,
          created_by: user.id,
          file_url: fileUrl,
        },
        user.id
      );

      toast({
        title: "Ficha criada com sucesso",
        description: "A ficha foi uploadada e está disponível para uso.",
      });

      // Reset form
      setName("");
      setDescription("");
      setFormType("pre_registration");
      setSelectedFile(null);
      
      onSuccess?.();
      onOpenChange(false);
    } catch (error) {
      console.error("Erro ao criar ficha:", error);
      toast({
        title: "Erro ao criar ficha",
        description: error instanceof Error ? error.message : "Ocorreu um erro inesperado.",
        variant: "destructive",
      });
    } finally {
      setUploading(false);
    }
  };

  const handleCancel = () => {
    if (!uploading) {
      setName("");
      setDescription("");
      setFormType("pre_registration");
      setSelectedFile(null);
      onOpenChange(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Upload de Ficha
          </DialogTitle>
          <DialogDescription>
            Faça upload de uma ficha já pronta (PDF, Word, Excel, etc.) que o clube já possua.
            Esta ficha ficará disponível para download pelos atletas durante o pré-cadastro.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Nome da Ficha *</Label>
            <Input
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="Ex: Ficha de Pré-cadastro Padrão do Clube"
              disabled={uploading}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Descrição</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Descreva brevemente o propósito desta ficha..."
              rows={3}
              disabled={uploading}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="formType">Tipo de Ficha *</Label>
            <Select
              value={formType}
              onValueChange={(value) => setFormType(value as FormType)}
              disabled={uploading}
            >
              <SelectTrigger>
                <SelectValue placeholder="Selecione o tipo de ficha" />
              </SelectTrigger>
              <SelectContent>
                {FORM_TYPE_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Arquivo da Ficha *</Label>
            <FileUploader
              id="template-file"
              accept=".pdf,.doc,.docx,.xls,.xlsx,.txt"
              maxSize={10} // 10MB
              onFileSelect={setSelectedFile}
              currentFile={selectedFile}
              disabled={uploading}
            />
            <p className="text-xs text-muted-foreground">
              Formatos aceitos: PDF, Word (.doc, .docx), Excel (.xls, .xlsx), TXT
              <br />
              Tamanho máximo: 10MB
            </p>
          </div>

          {selectedFile && (
            <div className="p-3 bg-muted rounded-md">
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4 text-muted-foreground" />
                <div className="text-sm">
                  <p className="font-medium">{selectedFile.name}</p>
                  <p className="text-muted-foreground">
                    {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={uploading}
            >
              Cancelar
            </Button>
            <Button type="submit" disabled={uploading || !selectedFile || !name.trim()}>
              {uploading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Enviando...
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4 mr-2" />
                  Criar Ficha
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}