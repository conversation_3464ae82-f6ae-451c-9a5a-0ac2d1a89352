import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { AlertTriangle, Calendar, MapPin, Activity, Clock } from 'lucide-react';
import { useUser } from '@/context/UserContext';
import { createInjuryHistory, InjuryHistory } from '@/api/injuryPrevention';
import { getPlayers } from '@/api/players';
import { toast } from 'sonner';

interface InjuryHistoryFormProps {
  clubId: number;
  onSuccess?: () => void;
}

export function InjuryHistoryForm({ clubId, onSuccess }: InjuryHistoryFormProps) {
  const { user } = useUser();
  const [players, setPlayers] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    player_id: '',
    injury_date: new Date().toISOString().split('T')[0],
    injury_type: '',
    body_part: '',
    severity: '',
    mechanism: '',
    days_out: '',
    return_date: '',
    occurred_during: '',
    field_position: '',
    weather_conditions: '',
    previous_injury: false,
    fatigue_level: '',
    workload_week: ''
  });

  React.useEffect(() => {
    loadPlayers();
  }, [clubId]);

  const loadPlayers = async () => {
    if (!user?.id) return;
    
    try {
        const playersData = await getPlayers(clubId, user.id, {
          includeInactive: false,
          includeLoaned: false,
          includeScheduled: false
        });
        // Exclude only inactive players and sort alphabetically, as done elsewhere
        setPlayers(
          playersData
            .filter(p => p.status !== 'inativo' && p.status !== 'inactive')
            .sort((a, b) => a.name.localeCompare(b.name))
        );
    } catch (error) {
      console.error('Erro ao carregar jogadores:', error);
      toast.error('Erro ao carregar lista de jogadores');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user?.id || !formData.player_id) return;

    try {
      setLoading(true);

      const injuryData: Omit<InjuryHistory, 'id' | 'club_id'> = {
        player_id: formData.player_id,
        injury_date: formData.injury_date,
        injury_type: formData.injury_type,
        body_part: formData.body_part,
        severity: formData.severity,
        mechanism: formData.mechanism || undefined,
        days_out: formData.days_out ? parseInt(formData.days_out) : undefined,
        return_date: formData.return_date || undefined,
        occurred_during: formData.occurred_during || undefined,
        field_position: formData.field_position || undefined,
        weather_conditions: formData.weather_conditions || undefined,
        previous_injury: formData.previous_injury,
        fatigue_level: formData.fatigue_level ? parseInt(formData.fatigue_level) : undefined,
        workload_week: formData.workload_week ? parseInt(formData.workload_week) : undefined
      };

      await createInjuryHistory(clubId, user.id, injuryData);
      
      toast.success('Histórico de lesão registrado com sucesso!');
      
      // Reset form
      setFormData({
        player_id: '',
        injury_date: new Date().toISOString().split('T')[0],
        injury_type: '',
        body_part: '',
        severity: '',
        mechanism: '',
        days_out: '',
        return_date: '',
        occurred_during: '',
        field_position: '',
        weather_conditions: '',
        previous_injury: false,
        fatigue_level: '',
        workload_week: ''
      });

      onSuccess?.();
    } catch (error) {
      console.error('Erro ao registrar lesão:', error);
      toast.error('Erro ao registrar histórico de lesão');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <AlertTriangle className="h-5 w-5 text-red-500" />
          Registro de Histórico de Lesões
        </CardTitle>
        <CardDescription>
          Registre lesões passadas e atuais para análise de risco
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="player">Jogador *</Label>
              <Select
                value={formData.player_id}
                onValueChange={(value) => setFormData(prev => ({ ...prev, player_id: value }))}
                required
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione um jogador" />
                </SelectTrigger>
                <SelectContent>
                  {players.map((player) => (
                    <SelectItem key={player.id} value={player.id}>
                      {player.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="injury_date" className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Data da Lesão *
              </Label>
              <Input
                id="injury_date"
                type="date"
                value={formData.injury_date}
                onChange={(e) => setFormData(prev => ({ ...prev, injury_date: e.target.value }))}
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Tipo de Lesão *</Label>
              <Select
                value={formData.injury_type}
                onValueChange={(value) => setFormData(prev => ({ ...prev, injury_type: value }))}
                required
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o tipo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="muscular">Muscular</SelectItem>
                  <SelectItem value="ligamentar">Ligamentar</SelectItem>
                  <SelectItem value="tendinosa">Tendinosa</SelectItem>
                  <SelectItem value="articular">Articular</SelectItem>
                  <SelectItem value="óssea">Óssea</SelectItem>
                  <SelectItem value="contusão">Contusão</SelectItem>
                  <SelectItem value="corte">Corte</SelectItem>
                  <SelectItem value="outras">Outras</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Parte do Corpo *</Label>
              <Select
                value={formData.body_part}
                onValueChange={(value) => setFormData(prev => ({ ...prev, body_part: value }))}
                required
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione a parte" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="cabeça">Cabeça</SelectItem>
                  <SelectItem value="pescoço">Pescoço</SelectItem>
                  <SelectItem value="ombro">Ombro</SelectItem>
                  <SelectItem value="braço">Braço</SelectItem>
                  <SelectItem value="cotovelo">Cotovelo</SelectItem>
                  <SelectItem value="punho">Punho</SelectItem>
                  <SelectItem value="mão">Mão</SelectItem>
                  <SelectItem value="tórax">Tórax</SelectItem>
                  <SelectItem value="abdomen">Abdomen</SelectItem>
                  <SelectItem value="costas">Costas</SelectItem>
                  <SelectItem value="quadril">Quadril</SelectItem>
                  <SelectItem value="coxa">Coxa</SelectItem>
                  <SelectItem value="joelho">Joelho</SelectItem>
                  <SelectItem value="panturrilha">Panturrilha</SelectItem>
                  <SelectItem value="tornozelo">Tornozelo</SelectItem>
                  <SelectItem value="pé">Pé</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Gravidade *</Label>
              <Select
                value={formData.severity}
                onValueChange={(value) => setFormData(prev => ({ ...prev, severity: value }))}
                required
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione a gravidade" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="leve">Leve (1-7 dias)</SelectItem>
                  <SelectItem value="moderada">Moderada (8-21 dias)</SelectItem>
                  <SelectItem value="grave">Grave (22-90 dias)</SelectItem>
                  <SelectItem value="muito_grave">Muito Grave (90+ dias)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Mecanismo da Lesão</Label>
              <Select
                value={formData.mechanism}
                onValueChange={(value) => setFormData(prev => ({ ...prev, mechanism: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Como ocorreu?" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="contato">Contato com adversário</SelectItem>
                  <SelectItem value="sem_contato">Sem contato</SelectItem>
                  <SelectItem value="sobrecarga">Sobrecarga</SelectItem>
                  <SelectItem value="fadiga">Fadiga</SelectItem>
                  <SelectItem value="acidental">Acidental</SelectItem>
                  <SelectItem value="recidiva">Recidiva</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Dias de Afastamento
              </Label>
              <Input
                type="number"
                min="0"
                max="365"
                value={formData.days_out}
                onChange={(e) => setFormData(prev => ({ ...prev, days_out: e.target.value }))}
                placeholder="Ex: 14"
              />
            </div>

            <div className="space-y-2">
              <Label>Data de Retorno</Label>
              <Input
                type="date"
                value={formData.return_date}
                onChange={(e) => setFormData(prev => ({ ...prev, return_date: e.target.value }))}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <Activity className="h-4 w-4" />
                Contexto da Lesão
              </Label>
              <Select
                value={formData.occurred_during}
                onValueChange={(value) => setFormData(prev => ({ ...prev, occurred_during: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Quando ocorreu?" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="treino">Treino</SelectItem>
                  <SelectItem value="jogo">Jogo</SelectItem>
                  <SelectItem value="aquecimento">Aquecimento</SelectItem>
                  <SelectItem value="fora_atividade">Fora da atividade</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                Posição em Campo
              </Label>
              <Select
                value={formData.field_position}
                onValueChange={(value) => setFormData(prev => ({ ...prev, field_position: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Onde estava?" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="area_penal">Área Penal</SelectItem>
                  <SelectItem value="meio_campo">Meio Campo</SelectItem>
                  <SelectItem value="lateral">Lateral</SelectItem>
                  <SelectItem value="fundo">Fundo</SelectItem>
                  <SelectItem value="centro">Centro</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label>Condições Climáticas</Label>
              <Select
                value={formData.weather_conditions}
                onValueChange={(value) => setFormData(prev => ({ ...prev, weather_conditions: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Clima" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="seco">Seco</SelectItem>
                  <SelectItem value="chuva">Chuva</SelectItem>
                  <SelectItem value="muito_quente">Muito Quente</SelectItem>
                  <SelectItem value="frio">Frio</SelectItem>
                  <SelectItem value="vento">Vento</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Nível de Fadiga (1-10)</Label>
              <Input
                type="number"
                min="1"
                max="10"
                value={formData.fatigue_level}
                onChange={(e) => setFormData(prev => ({ ...prev, fatigue_level: e.target.value }))}
                placeholder="Ex: 7"
              />
            </div>

            <div className="space-y-2">
              <Label>Carga da Semana (%)</Label>
              <Input
                type="number"
                min="0"
                max="200"
                value={formData.workload_week}
                onChange={(e) => setFormData(prev => ({ ...prev, workload_week: e.target.value }))}
                placeholder="Ex: 120"
              />
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="previous_injury"
              checked={formData.previous_injury}
              onCheckedChange={(checked) => 
                setFormData(prev => ({ ...prev, previous_injury: checked as boolean }))
              }
            />
            <Label htmlFor="previous_injury">
              Lesão prévia na mesma região
            </Label>
          </div>

          <Button type="submit" disabled={loading} className="w-full">
            {loading ? 'Registrando...' : 'Registrar Histórico de Lesão'}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}