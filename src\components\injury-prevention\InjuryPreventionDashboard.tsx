import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  AlertTriangle,
  Activity,
  Heart,
  TrendingUp,
  TrendingDown,
  Shield,
  Brain,
  Users,
  Calendar,
  Target,
  Zap,
  RefreshCw
} from 'lucide-react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import { useUser } from '@/context/UserContext';
import {
  getHighRiskPlayers,
  calculateRiskFactors,
  getPlayerRiskFactors,
  RiskFactors
} from '@/api/injuryPrevention';
import InjuryAlertService from '@/services/injuryAlertService';
import { getPlayers } from '@/api/players';
import { toast } from 'sonner';
import { WellnessForm } from './WellnessForm';
import { WorkloadForm } from './WorkloadForm';
import { InjuryHistoryForm } from './InjuryHistoryForm';

interface HighRiskPlayer extends RiskFactors {
  player_name: string;
}

interface InjuryPreventionDashboardProps {
  clubId: number;
}

export function InjuryPreventionDashboard({ clubId }: InjuryPreventionDashboardProps) {
  const { user } = useUser();
  const [highRiskPlayers, setHighRiskPlayers] = useState<HighRiskPlayer[]>([]);
  const [loading, setLoading] = useState(true);
  const [calculating, setCalculating] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<string>('');

  // Cores para os níveis de risco
  const riskColors = {
    baixo: '#22c55e',
    moderado: '#eab308', 
    alto: '#f97316',
    crítico: '#ef4444'
  };

  useEffect(() => {
    loadDashboardData();
  }, [clubId]);

  const loadDashboardData = async () => {
    if (!user?.id) return;
    
    try {
      setLoading(true);
      const players = await getHighRiskPlayers(clubId, user.id, 25); // threshold 25%
      setHighRiskPlayers(players);
      setLastUpdate(new Date().toLocaleString('pt-BR'));
    } catch (error) {
      console.error('Erro ao carregar dados:', error);
      toast.error('Erro ao carregar dados do dashboard');
    } finally {
      setLoading(false);
    }
  };

  const recalculateAllRisks = async () => {
    if (!user?.id) return;

    try {
      setCalculating(true);
      toast.info('Recalculando riscos para todos os jogadores...');

      // Usar a nova função da API que processa em lotes
      const { recalculateAllRisks } = await import('@/api/injuryPrevention');
      const results = await recalculateAllRisks(clubId, user.id);

      // Recarregar dados
      await loadDashboardData();

      toast.success(`Riscos recalculados! ${results.success} sucessos, ${results.errors} erros`);
    } catch (error) {
      console.error('Erro ao recalcular riscos:', error);
      toast.error('Erro ao recalcular riscos');
    } finally {
      setCalculating(false);
    }
  };

  const runFullAnalysis = async () => {
    if (!user?.id) return;

    try {
      setCalculating(true);
      toast.info('Executando análise completa com alertas automáticos...');

      // Executar análise completa com sistema de alertas
      const alerts = await InjuryAlertService.runAutomaticRiskAnalysis(clubId);

      // Recarregar dados
      await loadDashboardData();

      if (alerts.length > 0) {
        toast.success(`Análise concluída! ${alerts.length} alertas gerados.`);
      } else {
        toast.success('Análise concluída! Nenhum alerta crítico detectado.');
      }
    } catch (error) {
      console.error('Erro na análise completa:', error);
      toast.error('Erro na análise completa');
    } finally {
      setCalculating(false);
    }
  };

  // Estatísticas gerais
  const totalPlayers = highRiskPlayers.length;
  const criticalRisk = highRiskPlayers.filter(p => p.risk_level === 'crítico').length;
  const highRisk = highRiskPlayers.filter(p => p.risk_level === 'alto').length;
  const moderateRisk = highRiskPlayers.filter(p => p.risk_level === 'moderado').length;

  // Dados para gráficos
  const riskDistribution = [
    { name: 'Crítico', value: criticalRisk, color: riskColors.crítico },
    { name: 'Alto', value: highRisk, color: riskColors.alto },
    { name: 'Moderado', value: moderateRisk, color: riskColors.moderado }
  ];

  const riskTrend = highRiskPlayers.map(player => ({
    name: player.player_name.split(' ')[0],
    risco: player.overall_risk_score,
    muscular: player.muscular_risk_score,
    articular: player.joint_risk_score,
    sobrecarga: player.overload_risk_score
  }));

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center space-x-2">
          <RefreshCw className="h-4 w-4 animate-spin" />
          <span>Carregando dados de prevenção...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Brain className="h-6 w-6 text-blue-600" />
            IA - Prevenção de Lesões
          </h2>
          <p className="text-muted-foreground">
            Monitoramento inteligente de riscos • Última atualização: {lastUpdate}
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={recalculateAllRisks}
            disabled={calculating}
            variant="outline"
          >
            {calculating ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Calculando...
              </>
            ) : (
              <>
                <RefreshCw className="h-4 w-4 mr-2" />
                Recalcular Riscos
              </>
            )}
          </Button>

          <Button
            onClick={runFullAnalysis}
            disabled={calculating}
            className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
          >
            {calculating ? (
              <>
                <Zap className="h-4 w-4 mr-2 animate-pulse" />
                Analisando...
              </>
            ) : (
              <>
                <Zap className="h-4 w-4 mr-2" />
                Análise Completa
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Alertas Críticos */}
      {criticalRisk > 0 && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Atenção: Jogadores em Risco Crítico!</AlertTitle>
          <AlertDescription>
            {criticalRisk} jogador(es) apresentam risco crítico de lesão. 
            Avaliação médica urgente recomendada.
          </AlertDescription>
        </Alert>
      )}

      {/* Cards de Estatísticas */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Monitorados</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalPlayers}</div>
            <p className="text-xs text-muted-foreground">
              Jogadores com risco ≥ 25%
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Risco Crítico</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{criticalRisk}</div>
            <p className="text-xs text-muted-foreground">
              Requer atenção imediata
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Risco Alto</CardTitle>
            <TrendingUp className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{highRisk}</div>
            <p className="text-xs text-muted-foreground">
              Monitoramento intensivo
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Risco Moderado</CardTitle>
            <Activity className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{moderateRisk}</div>
            <p className="text-xs text-muted-foreground">
              Acompanhamento regular
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Visão Geral</TabsTrigger>
          <TabsTrigger value="players">Jogadores</TabsTrigger>
          <TabsTrigger value="analytics">Análises</TabsTrigger>
          <TabsTrigger value="wellness">Wellness</TabsTrigger>
          <TabsTrigger value="workload">Carga de Treino</TabsTrigger>
          <TabsTrigger value="injuries">Histórico de Lesões</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Distribuição de Riscos */}
            <Card>
              <CardHeader>
                <CardTitle>Distribuição de Riscos</CardTitle>
                <CardDescription>
                  Classificação dos jogadores por nível de risco
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={riskDistribution}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={100}
                      paddingAngle={5}
                      dataKey="value"
                    >
                      {riskDistribution.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Tendência de Riscos */}
            <Card>
              <CardHeader>
                <CardTitle>Scores de Risco por Jogador</CardTitle>
                <CardDescription>
                  Comparação dos diferentes tipos de risco
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={riskTrend}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="risco" fill="#8884d8" name="Risco Geral" />
                    <Bar dataKey="muscular" fill="#82ca9d" name="Muscular" />
                    <Bar dataKey="articular" fill="#ffc658" name="Articular" />
                    <Bar dataKey="sobrecarga" fill="#ff7300" name="Sobrecarga" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="players" className="space-y-4">
          <div className="grid gap-4">
            {highRiskPlayers.map((player) => (
              <Card key={player.id} className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div>
                      <h3 className="font-semibold">{player.player_name}</h3>
                      <div className="flex items-center space-x-2 mt-1">
                        <Badge 
                          variant={player.risk_level === 'crítico' ? 'destructive' : 'secondary'}
                          style={{ 
                            backgroundColor: riskColors[player.risk_level],
                            color: 'white'
                          }}
                        >
                          {player.risk_level.toUpperCase()}
                        </Badge>
                        <span className="text-sm text-muted-foreground">
                          Score: {player.overall_risk_score.toFixed(1)}%
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="text-sm text-muted-foreground mb-2">
                      Risco Geral
                    </div>
                    <Progress 
                      value={player.overall_risk_score} 
                      className="w-32"
                    />
                  </div>
                </div>

                {/* Recomendações */}
                {player.recommendations && player.recommendations.length > 0 && (
                  <div className="mt-4 p-3 bg-muted rounded-lg">
                    <h4 className="text-sm font-medium mb-2">Recomendações da IA:</h4>
                    <ul className="text-sm space-y-1">
                      {player.recommendations.map((rec: any, index: number) => (
                        <li key={index} className="flex items-center space-x-2">
                          <Target className="h-3 w-3" />
                          <span>{rec.message}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </Card>
            ))}

            {highRiskPlayers.length === 0 && (
              <Card className="p-8 text-center">
                <Shield className="h-12 w-12 text-green-500 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-green-700">
                  Excelente! Nenhum jogador em risco elevado
                </h3>
                <p className="text-muted-foreground">
                  Todos os jogadores estão com baixo risco de lesão
                </p>
              </Card>
            )}
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Métricas Avançadas */}
            <Card>
              <CardHeader>
                <CardTitle>Métricas de Performance</CardTitle>
                <CardDescription>
                  Indicadores chave de prevenção
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Taxa de Prevenção</span>
                  <span className="font-semibold text-green-600">
                    {((1 - (criticalRisk + highRisk) / Math.max(totalPlayers, 1)) * 100).toFixed(1)}%
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Jogadores Monitorados</span>
                  <span className="font-semibold">{totalPlayers}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Alertas Ativos</span>
                  <span className="font-semibold text-orange-600">
                    {criticalRisk + highRisk}
                  </span>
                </div>
              </CardContent>
            </Card>

            {/* Próximas Ações */}
            <Card>
              <CardHeader>
                <CardTitle>Próximas Ações Recomendadas</CardTitle>
                <CardDescription>
                  Baseado na análise da IA
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {criticalRisk > 0 && (
                    <div className="flex items-center space-x-2 text-red-600">
                      <AlertTriangle className="h-4 w-4" />
                      <span className="text-sm">
                        Agendar avaliação médica para {criticalRisk} jogador(es)
                      </span>
                    </div>
                  )}
                  {highRisk > 0 && (
                    <div className="flex items-center space-x-2 text-orange-600">
                      <Activity className="h-4 w-4" />
                      <span className="text-sm">
                        Ajustar carga de treino para {highRisk} jogador(es)
                      </span>
                    </div>
                  )}
                  <div className="flex items-center space-x-2 text-blue-600">
                    <Calendar className="h-4 w-4" />
                    <span className="text-sm">
                      Próxima análise automática em 24h
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="wellness" className="space-y-4">
          <WellnessForm clubId={clubId} onSuccess={loadDashboardData} />
        </TabsContent>

        <TabsContent value="workload" className="space-y-4">
          <WorkloadForm clubId={clubId} onSuccess={loadDashboardData} />
        </TabsContent>

        <TabsContent value="injuries" className="space-y-4">
          <InjuryHistoryForm clubId={clubId} onSuccess={loadDashboardData} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
