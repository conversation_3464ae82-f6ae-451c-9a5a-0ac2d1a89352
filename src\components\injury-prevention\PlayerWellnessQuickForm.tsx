import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Heart,
  Moon,
  Zap,
  Smile,
  CheckCircle,
  Save,
  Clock,
  User
} from 'lucide-react';
import { useUser } from '@/context/UserContext';
import { createPlayerOwnWellnessData, WellnessData } from '@/api/injuryPrevention';
import { checkPlayerStatus } from '@/api/players';
import { toast } from 'sonner';
import { format } from 'date-fns';

interface PlayerWellnessQuickFormProps {
  clubId: number;
  onSuccess?: () => void;
}

export function PlayerWellnessQuickForm({ clubId, onSuccess }: PlayerWellnessQuickFormProps) {
  const { user } = useUser();
  const [loading, setLoading] = useState(false);
  const [playerInfo, setPlayerInfo] = useState<{ isPlayer: boolean; playerId?: string } | null>(null);
  const [checkingPlayer, setCheckingPlayer] = useState(true);
  
  // Estados do formulário simplificado
  const [formData, setFormData] = useState({
    sleep_hours: 8,
    sleep_quality: 7,
    fatigue_level: 3,
    muscle_soreness: 2,
    stress_level: 3,
    mood: 7,
    notes: ''
  });

  useEffect(() => {
    checkIfUserIsPlayer();
  }, [clubId, user?.id]);

  const checkIfUserIsPlayer = async () => {
    if (!user?.id) return;
    
    try {
      setCheckingPlayer(true);
      const status = await checkPlayerStatus(clubId, user.id);
      setPlayerInfo(status);
    } catch (error) {
      console.error('Erro ao verificar status do jogador:', error);
      setPlayerInfo({ isPlayer: false });
    } finally {
      setCheckingPlayer(false);
    }
  };

  const handleSliderChange = (field: string, value: number[]) => {
    setFormData(prev => ({
      ...prev,
      [field]: value[0]
    }));
  };

  const handleInputChange = (field: string, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!playerInfo?.playerId || !user?.id) {
      toast.error('Erro de autenticação');
      return;
    }

    try {
      setLoading(true);

      const wellnessData: Omit<WellnessData, 'id' | 'club_id' | 'player_id'> = {
        date: format(new Date(), 'yyyy-MM-dd'),
        sleep_hours: formData.sleep_hours,
        sleep_quality: formData.sleep_quality,
        fatigue_level: formData.fatigue_level,
        muscle_soreness: formData.muscle_soreness,
        stress_level: formData.stress_level,
        mood: formData.mood,
        notes: formData.notes || undefined
      };

      await createPlayerOwnWellnessData(clubId, user.id, playerInfo.playerId, wellnessData);
      
      toast.success('Dados de wellness registrados com sucesso! 🎉');
      
      // Reset form
      setFormData({
        sleep_hours: 8,
        sleep_quality: 7,
        fatigue_level: 3,
        muscle_soreness: 2,
        stress_level: 3,
        mood: 7,
        notes: ''
      });

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Erro ao salvar dados:', error);
      toast.error('Erro ao salvar dados de wellness');
    } finally {
      setLoading(false);
    }
  };

  const getScoreColor = (value: number, reverse: boolean = false) => {
    if (reverse) {
      if (value <= 3) return 'text-green-600';
      if (value <= 6) return 'text-yellow-600';
      return 'text-red-600';
    } else {
      if (value >= 7) return 'text-green-600';
      if (value >= 4) return 'text-yellow-600';
      return 'text-red-600';
    }
  };

  const getScoreBadge = (value: number, reverse: boolean = false) => {
    const color = getScoreColor(value, reverse);
    let label = '';
    
    if (reverse) {
      if (value <= 3) label = 'Baixo';
      else if (value <= 6) label = 'Moderado';
      else label = 'Alto';
    } else {
      if (value >= 7) label = 'Bom';
      else if (value >= 4) label = 'Regular';
      else label = 'Ruim';
    }

    return (
      <Badge variant="outline" className={color}>
        {label}
      </Badge>
    );
  };

  if (checkingPlayer) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardContent className="p-8 text-center">
          <div className="animate-spin h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
          <p>Verificando permissões...</p>
        </CardContent>
      </Card>
    );
  }

  if (!playerInfo?.isPlayer) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardContent className="p-8 text-center">
          <User className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-700 mb-2">
            Acesso Restrito
          </h3>
          <p className="text-gray-600">
            Este formulário é exclusivo para jogadores registrados no sistema.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader className="text-center">
        <CardTitle className="flex items-center justify-center gap-2">
          <Heart className="h-5 w-5 text-red-500" />
          Como você está se sentindo hoje?
        </CardTitle>
        <CardDescription>
          Leva apenas 2 minutos • Ajuda a prevenir lesões • Dados confidenciais
        </CardDescription>
        <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
          <Clock className="h-4 w-4" />
          {format(new Date(), 'dd/MM/yyyy')}
        </div>
      </CardHeader>
      
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Sono */}
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Quantas horas você dormiu?</Label>
                <Input
                  type="number"
                  step="0.5"
                  min="0"
                  max="12"
                  value={formData.sleep_hours}
                  onChange={(e) => handleInputChange('sleep_hours', Number(e.target.value))}
                  className="text-center text-lg"
                />
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label>Como foi a qualidade do seu sono?</Label>
                  {getScoreBadge(formData.sleep_quality)}
                </div>
                <Slider
                  value={[formData.sleep_quality]}
                  onValueChange={(value) => handleSliderChange('sleep_quality', value)}
                  max={10}
                  min={1}
                  step={1}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Péssima</span>
                  <span className="font-medium">{formData.sleep_quality}</span>
                  <span>Excelente</span>
                </div>
              </div>
            </div>
          </div>

          {/* Estado Físico */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Zap className="h-4 w-4" />
              Como está seu corpo?
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label>Nível de cansaço</Label>
                  {getScoreBadge(formData.fatigue_level, true)}
                </div>
                <Slider
                  value={[formData.fatigue_level]}
                  onValueChange={(value) => handleSliderChange('fatigue_level', value)}
                  max={10}
                  min={1}
                  step={1}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Descansado</span>
                  <span className="font-medium">{formData.fatigue_level}</span>
                  <span>Exausto</span>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label>Dor muscular</Label>
                  {getScoreBadge(formData.muscle_soreness, true)}
                </div>
                <Slider
                  value={[formData.muscle_soreness]}
                  onValueChange={(value) => handleSliderChange('muscle_soreness', value)}
                  max={10}
                  min={1}
                  step={1}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Nenhuma</span>
                  <span className="font-medium">{formData.muscle_soreness}</span>
                  <span>Intensa</span>
                </div>
              </div>
            </div>
          </div>

          {/* Estado Mental */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Smile className="h-4 w-4" />
              Como está sua mente?
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label>Nível de stress</Label>
                  {getScoreBadge(formData.stress_level, true)}
                </div>
                <Slider
                  value={[formData.stress_level]}
                  onValueChange={(value) => handleSliderChange('stress_level', value)}
                  max={10}
                  min={1}
                  step={1}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Relaxado</span>
                  <span className="font-medium">{formData.stress_level}</span>
                  <span>Estressado</span>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label>Como está seu humor?</Label>
                  {getScoreBadge(formData.mood)}
                </div>
                <Slider
                  value={[formData.mood]}
                  onValueChange={(value) => handleSliderChange('mood', value)}
                  max={10}
                  min={1}
                  step={1}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Péssimo</span>
                  <span className="font-medium">{formData.mood}</span>
                  <span>Excelente</span>
                </div>
              </div>
            </div>
          </div>

          {/* Observações */}
          <div className="space-y-2">
            <Label>Alguma observação? (opcional)</Label>
            <Textarea
              placeholder="Ex: Senti uma leve dor no joelho ontem, dormi mal por causa do barulho..."
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              rows={3}
            />
          </div>

          {/* Botão de Envio */}
          <Button 
            type="submit" 
            className="w-full h-12 text-lg" 
            disabled={loading}
          >
            {loading ? (
              <>
                <Save className="h-5 w-5 mr-2 animate-spin" />
                Salvando...
              </>
            ) : (
              <>
                <CheckCircle className="h-5 w-5 mr-2" />
                Enviar Dados de Hoje
              </>
            )}
          </Button>

          <div className="text-center text-xs text-muted-foreground">
            🔒 Seus dados são confidenciais e usados apenas para prevenção de lesões
          </div>
        </form>
      </CardContent>
    </Card>
  );
}