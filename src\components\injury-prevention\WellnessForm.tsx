import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Heart, Moon, Zap, Frown, Smile, Meh } from 'lucide-react';
import { useUser } from '@/context/UserContext';
import { createWellnessData, WellnessData } from '@/api/injuryPrevention';
import { getPlayers } from '@/api/players';
import { toast } from 'sonner';

interface WellnessFormProps {
    clubId: number;
    onSuccess?: () => void;
}

export function WellnessForm({ clubId, onSuccess }: WellnessFormProps) {
    const { user } = useUser();
    const [players, setPlayers] = useState<any[]>([]);
    const [loading, setLoading] = useState(false);
    const [formData, setFormData] = useState({
        player_id: '',
        date: new Date().toISOString().split('T')[0],
        sleep_hours: 8,
        sleep_quality: [7],
        fatigue_level: [3],
        muscle_soreness: [2],
        stress_level: [3],
        mood: [7],
        weight: '',
        resting_heart_rate: '',
        notes: ''
    });

    React.useEffect(() => {
        loadPlayers();
    }, [clubId]);

    const loadPlayers = async () => {
        if (!user?.id) return;

        try {
            const playersData = await getPlayers(clubId, user.id, {
                includeInactive: false,
                includeLoaned: false,
                includeScheduled: false
            });
            // Exclude only inactive players and sort by name, similar to other modules
            setPlayers(
                playersData
                    .filter(p => p.status !== 'inativo' && p.status !== 'inactive')
                    .sort((a, b) => a.name.localeCompare(b.name))
            );
        } catch (error) {
            console.error('Erro ao carregar jogadores:', error);
            toast.error('Erro ao carregar lista de jogadores');
        }
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!user?.id || !formData.player_id) return;

        try {
            setLoading(true);

            const wellnessData: Omit<WellnessData, 'id' | 'club_id'> = {
                player_id: formData.player_id,
                date: formData.date,
                sleep_hours: formData.sleep_hours,
                sleep_quality: formData.sleep_quality[0],
                fatigue_level: formData.fatigue_level[0],
                muscle_soreness: formData.muscle_soreness[0],
                stress_level: formData.stress_level[0],
                mood: formData.mood[0],
                weight: formData.weight ? parseFloat(formData.weight) : undefined,
                resting_heart_rate: formData.resting_heart_rate ? parseInt(formData.resting_heart_rate) : undefined,
                notes: formData.notes || undefined
            };

            await createWellnessData(clubId, user.id, wellnessData);

            toast.success('Dados de wellness registrados com sucesso!');

            // Reset form
            setFormData({
                player_id: '',
                date: new Date().toISOString().split('T')[0],
                sleep_hours: 8,
                sleep_quality: [7],
                fatigue_level: [3],
                muscle_soreness: [2],
                stress_level: [3],
                mood: [7],
                weight: '',
                resting_heart_rate: '',
                notes: ''
            });

            onSuccess?.();
        } catch (error) {
            console.error('Erro ao registrar wellness:', error);
            toast.error('Erro ao registrar dados de wellness');
        } finally {
            setLoading(false);
        }
    };

    const getMoodIcon = (value: number) => {
        if (value <= 3) return <Frown className="h-4 w-4 text-red-500" />;
        if (value <= 6) return <Meh className="h-4 w-4 text-yellow-500" />;
        return <Smile className="h-4 w-4 text-green-500" />;
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    <Heart className="h-5 w-5 text-red-500" />
                    Registro de Wellness Diário
                </CardTitle>
                <CardDescription>
                    Registre dados de bem-estar e recuperação dos jogadores
                </CardDescription>
            </CardHeader>
            <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="player">Jogador *</Label>
                            <Select
                                value={formData.player_id}
                                onValueChange={(value) => setFormData(prev => ({ ...prev, player_id: value }))}
                                required
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Selecione um jogador" />
                                </SelectTrigger>
                                <SelectContent>
                                    {players.map((player) => (
                                        <SelectItem key={player.id} value={player.id}>
                                            {player.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="date">Data *</Label>
                            <Input
                                id="date"
                                type="date"
                                value={formData.date}
                                onChange={(e) => setFormData(prev => ({ ...prev, date: e.target.value }))}
                                required
                            />
                        </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-4">
                            <div className="space-y-2">
                                <Label>Horas de Sono</Label>
                                <Input
                                    type="number"
                                    min="0"
                                    max="12"
                                    step="0.5"
                                    value={formData.sleep_hours}
                                    onChange={(e) => setFormData(prev => ({ ...prev, sleep_hours: parseFloat(e.target.value) }))}
                                />
                            </div>

                            <div className="space-y-3">
                                <Label className="flex items-center gap-2">
                                    <Moon className="h-4 w-4" />
                                    Qualidade do Sono: {formData.sleep_quality[0]}/10
                                </Label>
                                <Slider
                                    value={formData.sleep_quality}
                                    onValueChange={(value) => setFormData(prev => ({ ...prev, sleep_quality: value }))}
                                    max={10}
                                    min={1}
                                    step={1}
                                    className="w-full"
                                />
                                <div className="flex justify-between text-xs text-muted-foreground">
                                    <span>Péssima</span>
                                    <span>Excelente</span>
                                </div>
                            </div>

                            <div className="space-y-3">
                                <Label className="flex items-center gap-2">
                                    <Zap className="h-4 w-4" />
                                    Nível de Fadiga: {formData.fatigue_level[0]}/10
                                </Label>
                                <Slider
                                    value={formData.fatigue_level}
                                    onValueChange={(value) => setFormData(prev => ({ ...prev, fatigue_level: value }))}
                                    max={10}
                                    min={1}
                                    step={1}
                                    className="w-full"
                                />
                                <div className="flex justify-between text-xs text-muted-foreground">
                                    <span>Sem fadiga</span>
                                    <span>Exausto</span>
                                </div>
                            </div>
                        </div>

                        <div className="space-y-4">
                            <div className="space-y-3">
                                <Label>Dor Muscular: {formData.muscle_soreness[0]}/10</Label>
                                <Slider
                                    value={formData.muscle_soreness}
                                    onValueChange={(value) => setFormData(prev => ({ ...prev, muscle_soreness: value }))}
                                    max={10}
                                    min={1}
                                    step={1}
                                    className="w-full"
                                />
                                <div className="flex justify-between text-xs text-muted-foreground">
                                    <span>Sem dor</span>
                                    <span>Dor intensa</span>
                                </div>
                            </div>

                            <div className="space-y-3">
                                <Label>Nível de Stress: {formData.stress_level[0]}/10</Label>
                                <Slider
                                    value={formData.stress_level}
                                    onValueChange={(value) => setFormData(prev => ({ ...prev, stress_level: value }))}
                                    max={10}
                                    min={1}
                                    step={1}
                                    className="w-full"
                                />
                                <div className="flex justify-between text-xs text-muted-foreground">
                                    <span>Relaxado</span>
                                    <span>Muito estressado</span>
                                </div>
                            </div>

                            <div className="space-y-3">
                                <Label className="flex items-center gap-2">
                                    {getMoodIcon(formData.mood[0])}
                                    Humor: {formData.mood[0]}/10
                                </Label>
                                <Slider
                                    value={formData.mood}
                                    onValueChange={(value) => setFormData(prev => ({ ...prev, mood: value }))}
                                    max={10}
                                    min={1}
                                    step={1}
                                    className="w-full"
                                />
                                <div className="flex justify-between text-xs text-muted-foreground">
                                    <span>Péssimo</span>
                                    <span>Excelente</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="weight">Peso (kg)</Label>
                            <Input
                                id="weight"
                                type="number"
                                min="40"
                                max="150"
                                step="0.1"
                                value={formData.weight}
                                onChange={(e) => setFormData(prev => ({ ...prev, weight: e.target.value }))}
                                placeholder="Ex: 75.5"
                            />
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="resting_heart_rate">FC Repouso (bpm)</Label>
                            <Input
                                id="resting_heart_rate"
                                type="number"
                                min="40"
                                max="120"
                                value={formData.resting_heart_rate}
                                onChange={(e) => setFormData(prev => ({ ...prev, resting_heart_rate: e.target.value }))}
                                placeholder="Ex: 65"
                            />
                        </div>
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="notes">Observações</Label>
                        <Textarea
                            id="notes"
                            value={formData.notes}
                            onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                            placeholder="Observações adicionais sobre o estado do jogador..."
                            rows={3}
                        />
                    </div>

                    <Button type="submit" disabled={loading} className="w-full">
                        {loading ? 'Registrando...' : 'Registrar Dados de Wellness'}
                    </Button>
                </form>
            </CardContent>
        </Card>
    );
}