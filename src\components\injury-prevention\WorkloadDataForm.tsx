import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Activity,
  Timer,
  Zap,
  MapPin,
  Heart,
  CheckCircle,
  Save,
  Calculator
} from 'lucide-react';
import { useUser } from '@/context/UserContext';
import { createWorkloadData, WorkloadData } from '@/api/injuryPrevention';
import { getPlayers } from '@/api/players';
import { toast } from 'sonner';
import { format } from 'date-fns';

interface Player {
  id: string;
  name: string;
}

interface WorkloadDataFormProps {
  clubId: number;
  playerId?: string;
  onSuccess?: () => void;
}

export function WorkloadDataForm({ clubId, playerId, onSuccess }: WorkloadDataFormProps) {
  const { user } = useUser();
  const [players, setPlayers] = useState<Player[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedPlayerId, setSelectedPlayerId] = useState<string | null>(playerId || null);
  
  // Estados do formulário
  const [formData, setFormData] = useState({
    date: format(new Date(), 'yyyy-MM-dd'),
    training_duration: 90, // minutos
    training_intensity: 7, // 1-10
    training_type: 'físico',
    distance_covered: 0, // km
    sprint_count: 0,
    max_speed: 0, // km/h
    heart_rate_avg: 0, // bpm
    heart_rate_max: 0, // bpm
    perceived_exertion: 6, // RPE 1-10
    sleep_quality: 7, // 1-10
    stress_level: 3, // 1-10
    muscle_soreness: 2 // 1-10
  });

  useEffect(() => {
    loadPlayers();
  }, [clubId]);

  const loadPlayers = async () => {
    try {
      const playersData = await getPlayers(clubId);
      setPlayers(playersData);
    } catch (error) {
      console.error('Erro ao carregar jogadores:', error);
      toast.error('Erro ao carregar lista de jogadores');
    }
  };

  const handleSliderChange = (field: string, value: number[]) => {
    setFormData(prev => ({
      ...prev,
      [field]: value[0]
    }));
  };

  const handleInputChange = (field: string, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const calculateTrainingLoad = () => {
    return formData.training_intensity * formData.training_duration;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedPlayerId) {
      toast.error('Selecione um jogador');
      return;
    }

    if (!user?.id) {
      toast.error('Usuário não autenticado');
      return;
    }

    try {
      setLoading(true);

      const workloadData: Omit<WorkloadData, 'id' | 'club_id'> = {
        player_id: selectedPlayerId,
        date: formData.date,
        training_duration: formData.training_duration,
        training_intensity: formData.training_intensity,
        training_type: formData.training_type,
        distance_covered: formData.distance_covered > 0 ? formData.distance_covered : undefined,
        sprint_count: formData.sprint_count > 0 ? formData.sprint_count : undefined,
        max_speed: formData.max_speed > 0 ? formData.max_speed : undefined,
        heart_rate_avg: formData.heart_rate_avg > 0 ? formData.heart_rate_avg : undefined,
        heart_rate_max: formData.heart_rate_max > 0 ? formData.heart_rate_max : undefined,
        perceived_exertion: formData.perceived_exertion,
        sleep_quality: formData.sleep_quality,
        stress_level: formData.stress_level,
        muscle_soreness: formData.muscle_soreness,
        training_load: calculateTrainingLoad()
      };

      await createWorkloadData(clubId, user.id, workloadData);
      
      toast.success('Dados de carga de trabalho registrados com sucesso!');
      
      // Reset form
      setFormData({
        date: format(new Date(), 'yyyy-MM-dd'),
        training_duration: 90,
        training_intensity: 7,
        training_type: 'físico',
        distance_covered: 0,
        sprint_count: 0,
        max_speed: 0,
        heart_rate_avg: 0,
        heart_rate_max: 0,
        perceived_exertion: 6,
        sleep_quality: 7,
        stress_level: 3,
        muscle_soreness: 2
      });

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Erro ao salvar dados:', error);
      toast.error('Erro ao salvar dados de carga de trabalho');
    } finally {
      setLoading(false);
    }
  };

  const getIntensityColor = (value: number) => {
    if (value <= 3) return 'text-green-600';
    if (value <= 6) return 'text-yellow-600';
    if (value <= 8) return 'text-orange-600';
    return 'text-red-600';
  };

  const getIntensityLabel = (value: number) => {
    if (value <= 3) return 'Leve';
    if (value <= 6) return 'Moderada';
    if (value <= 8) return 'Alta';
    return 'Máxima';
  };

  const getRPEColor = (value: number) => {
    if (value <= 4) return 'text-green-600';
    if (value <= 6) return 'text-yellow-600';
    if (value <= 8) return 'text-orange-600';
    return 'text-red-600';
  };

  const getRPELabel = (value: number) => {
    if (value <= 2) return 'Muito Fácil';
    if (value <= 4) return 'Fácil';
    if (value <= 6) return 'Moderado';
    if (value <= 8) return 'Difícil';
    return 'Muito Difícil';
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Activity className="h-5 w-5 text-blue-500" />
          Registro de Carga de Trabalho
        </CardTitle>
        <CardDescription>
          Registre os dados de treino para análise de risco de lesões
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Seleção de Jogador e Data */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="player">Jogador</Label>
              <Select 
                value={selectedPlayerId || ''} 
                onValueChange={(value) => setSelectedPlayerId(value)}
                disabled={!!playerId}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione um jogador" />
                </SelectTrigger>
                <SelectContent>
                  {players.map((player) => (
                    <SelectItem key={player.id} value={player.id}>
                      {player.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="date">Data do Treino</Label>
              <Input
                id="date"
                type="date"
                value={formData.date}
                onChange={(e) => handleInputChange('date', e.target.value)}
                required
              />
            </div>
          </div>

          {/* Dados Básicos do Treino */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Timer className="h-4 w-4" />
              Dados do Treino
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label>Duração (minutos)</Label>
                <Input
                  type="number"
                  min="1"
                  max="300"
                  value={formData.training_duration}
                  onChange={(e) => handleInputChange('training_duration', Number(e.target.value))}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label>Tipo de Treino</Label>
                <Select 
                  value={formData.training_type} 
                  onValueChange={(value) => handleInputChange('training_type', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="técnico">Técnico</SelectItem>
                    <SelectItem value="físico">Físico</SelectItem>
                    <SelectItem value="tático">Tático</SelectItem>
                    <SelectItem value="jogo">Jogo</SelectItem>
                    <SelectItem value="recuperação">Recuperação</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label className="flex items-center gap-2">
                  <Calculator className="h-4 w-4" />
                  Carga Calculada
                </Label>
                <div className="p-3 bg-muted rounded-md">
                  <div className="text-2xl font-bold text-blue-600">
                    {calculateTrainingLoad()}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Intensidade × Duração
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label>Intensidade do Treino</Label>
                <Badge variant="outline" className={getIntensityColor(formData.training_intensity)}>
                  {getIntensityLabel(formData.training_intensity)}
                </Badge>
              </div>
              <Slider
                value={[formData.training_intensity]}
                onValueChange={(value) => handleSliderChange('training_intensity', value)}
                max={10}
                min={1}
                step={1}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>Muito Leve (1)</span>
                <span className="font-medium">{formData.training_intensity}</span>
                <span>Máxima (10)</span>
              </div>
            </div>
          </div>

          {/* Dados Físicos (Opcional) */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <MapPin className="h-4 w-4" />
              Dados Físicos (Opcional)
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label>Distância Percorrida (km)</Label>
                <Input
                  type="number"
                  step="0.1"
                  min="0"
                  placeholder="Ex: 8.5"
                  value={formData.distance_covered || ''}
                  onChange={(e) => handleInputChange('distance_covered', Number(e.target.value))}
                />
              </div>

              <div className="space-y-2">
                <Label>Número de Sprints</Label>
                <Input
                  type="number"
                  min="0"
                  placeholder="Ex: 15"
                  value={formData.sprint_count || ''}
                  onChange={(e) => handleInputChange('sprint_count', Number(e.target.value))}
                />
              </div>

              <div className="space-y-2">
                <Label>Velocidade Máxima (km/h)</Label>
                <Input
                  type="number"
                  step="0.1"
                  min="0"
                  placeholder="Ex: 28.5"
                  value={formData.max_speed || ''}
                  onChange={(e) => handleInputChange('max_speed', Number(e.target.value))}
                />
              </div>
            </div>
          </div>

          {/* Dados de Frequência Cardíaca (Opcional) */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Heart className="h-4 w-4" />
              Frequência Cardíaca (Opcional)
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>FC Média (bpm)</Label>
                <Input
                  type="number"
                  min="60"
                  max="220"
                  placeholder="Ex: 150"
                  value={formData.heart_rate_avg || ''}
                  onChange={(e) => handleInputChange('heart_rate_avg', Number(e.target.value))}
                />
              </div>

              <div className="space-y-2">
                <Label>FC Máxima (bpm)</Label>
                <Input
                  type="number"
                  min="100"
                  max="220"
                  placeholder="Ex: 185"
                  value={formData.heart_rate_max || ''}
                  onChange={(e) => handleInputChange('heart_rate_max', Number(e.target.value))}
                />
              </div>
            </div>
          </div>

          {/* RPE e Dados Subjetivos */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Zap className="h-4 w-4" />
              Percepção Subjetiva
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label>RPE - Esforço Percebido</Label>
                  <Badge variant="outline" className={getRPEColor(formData.perceived_exertion)}>
                    {getRPELabel(formData.perceived_exertion)}
                  </Badge>
                </div>
                <Slider
                  value={[formData.perceived_exertion]}
                  onValueChange={(value) => handleSliderChange('perceived_exertion', value)}
                  max={10}
                  min={1}
                  step={1}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Muito Fácil (1)</span>
                  <span className="font-medium">{formData.perceived_exertion}</span>
                  <span>Máximo (10)</span>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Qualidade do Sono (noite anterior)</Label>
                <Slider
                  value={[formData.sleep_quality]}
                  onValueChange={(value) => handleSliderChange('sleep_quality', value)}
                  max={10}
                  min={1}
                  step={1}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Péssima (1)</span>
                  <span className="font-medium">{formData.sleep_quality}</span>
                  <span>Excelente (10)</span>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Nível de Stress</Label>
                <Slider
                  value={[formData.stress_level]}
                  onValueChange={(value) => handleSliderChange('stress_level', value)}
                  max={10}
                  min={1}
                  step={1}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Relaxado (1)</span>
                  <span className="font-medium">{formData.stress_level}</span>
                  <span>Muito Estressado (10)</span>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Dor Muscular</Label>
                <Slider
                  value={[formData.muscle_soreness]}
                  onValueChange={(value) => handleSliderChange('muscle_soreness', value)}
                  max={10}
                  min={1}
                  step={1}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Nenhuma (1)</span>
                  <span className="font-medium">{formData.muscle_soreness}</span>
                  <span>Intensa (10)</span>
                </div>
              </div>
            </div>
          </div>

          {/* Botão de Envio */}
          <Button 
            type="submit" 
            className="w-full" 
            disabled={loading || !selectedPlayerId}
          >
            {loading ? (
              <>
                <Save className="h-4 w-4 mr-2 animate-spin" />
                Salvando...
              </>
            ) : (
              <>
                <CheckCircle className="h-4 w-4 mr-2" />
                Registrar Carga de Trabalho
              </>
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}