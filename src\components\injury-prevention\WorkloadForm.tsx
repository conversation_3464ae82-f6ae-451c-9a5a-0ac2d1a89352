import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Activity, Clock, Zap, Target, Heart, TrendingUp } from 'lucide-react';
import { useUser } from '@/context/UserContext';
import { createWorkloadData, WorkloadData } from '@/api/injuryPrevention';
import { getPlayers } from '@/api/players';
import { toast } from 'sonner';

interface WorkloadFormProps {
  clubId: number;
  onSuccess?: () => void;
}

export function WorkloadForm({ clubId, onSuccess }: WorkloadFormProps) {
  const { user } = useUser();
  const [players, setPlayers] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    player_id: '',
    date: new Date().toISOString().split('T')[0],
    training_duration: '',
    training_intensity: [5],
    training_type: '',
    distance_covered: '',
    sprint_count: '',
    max_speed: '',
    heart_rate_avg: '',
    heart_rate_max: '',
    perceived_exertion: [5]
  });

  React.useEffect(() => {
    loadPlayers();
  }, [clubId]);

  const loadPlayers = async () => {
    if (!user?.id) return;
    
    try {
      const playersData = await getPlayers(clubId, user.id, {
        includeInactive: false,
        includeLoaned: false,
        includeScheduled: false
      });
      // Exclude only inactive players and sort by name to match other modules
      setPlayers(
        playersData
          .filter(p => p.status !== 'inativo' && p.status !== 'inactive')
          .sort((a, b) => a.name.localeCompare(b.name))
      );
    } catch (error) {
      console.error('Erro ao carregar jogadores:', error);
      toast.error('Erro ao carregar lista de jogadores');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user?.id || !formData.player_id) return;

    try {
      setLoading(true);

      const workloadData: Omit<WorkloadData, 'id' | 'club_id'> = {
        player_id: formData.player_id,
        date: formData.date,
        training_duration: formData.training_duration ? parseInt(formData.training_duration) : undefined,
        training_intensity: formData.training_intensity[0],
        training_type: formData.training_type || undefined,
        distance_covered: formData.distance_covered ? parseFloat(formData.distance_covered) : undefined,
        sprint_count: formData.sprint_count ? parseInt(formData.sprint_count) : undefined,
        max_speed: formData.max_speed ? parseFloat(formData.max_speed) : undefined,
        heart_rate_avg: formData.heart_rate_avg ? parseInt(formData.heart_rate_avg) : undefined,
        heart_rate_max: formData.heart_rate_max ? parseInt(formData.heart_rate_max) : undefined,
        perceived_exertion: formData.perceived_exertion[0]
      };

      await createWorkloadData(clubId, user.id, workloadData);
      
      toast.success('Dados de carga de trabalho registrados com sucesso!');
      
      // Reset form
      setFormData({
        player_id: '',
        date: new Date().toISOString().split('T')[0],
        training_duration: '',
        training_intensity: [5],
        training_type: '',
        distance_covered: '',
        sprint_count: '',
        max_speed: '',
        heart_rate_avg: '',
        heart_rate_max: '',
        perceived_exertion: [5]
      });

      onSuccess?.();
    } catch (error) {
      console.error('Erro ao registrar carga de trabalho:', error);
      toast.error('Erro ao registrar dados de carga de trabalho');
    } finally {
      setLoading(false);
    }
  };

  const calculateTrainingLoad = () => {
    const duration = parseInt(formData.training_duration) || 0;
    const intensity = formData.training_intensity[0] || 0;
    return duration * intensity;
  };

  const getIntensityLabel = (value: number) => {
    if (value <= 2) return 'Muito Leve';
    if (value <= 4) return 'Leve';
    if (value <= 6) return 'Moderada';
    if (value <= 8) return 'Intensa';
    return 'Muito Intensa';
  };

  const getRPELabel = (value: number) => {
    if (value <= 2) return 'Muito Fácil';
    if (value <= 4) return 'Fácil';
    if (value <= 6) return 'Moderado';
    if (value <= 8) return 'Difícil';
    return 'Muito Difícil';
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Activity className="h-5 w-5 text-blue-500" />
          Registro de Carga de Trabalho
        </CardTitle>
        <CardDescription>
          Registre dados de treino e carga física dos jogadores
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="player">Jogador *</Label>
              <Select
                value={formData.player_id}
                onValueChange={(value) => setFormData(prev => ({ ...prev, player_id: value }))}
                required
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione um jogador" />
                </SelectTrigger>
                <SelectContent>
                  {players.map((player) => (
                    <SelectItem key={player.id} value={player.id}>
                      {player.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="date">Data *</Label>
              <Input
                id="date"
                type="date"
                value={formData.date}
                onChange={(e) => setFormData(prev => ({ ...prev, date: e.target.value }))}
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label className="flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  Duração do Treino (minutos)
                </Label>
                <Input
                  type="number"
                  min="0"
                  max="300"
                  value={formData.training_duration}
                  onChange={(e) => setFormData(prev => ({ ...prev, training_duration: e.target.value }))}
                  placeholder="Ex: 90"
                />
              </div>

              <div className="space-y-3">
                <Label className="flex items-center gap-2">
                  <Zap className="h-4 w-4" />
                  Intensidade: {formData.training_intensity[0]}/10 ({getIntensityLabel(formData.training_intensity[0])})
                </Label>
                <Slider
                  value={formData.training_intensity}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, training_intensity: value }))}
                  max={10}
                  min={1}
                  step={1}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Muito Leve</span>
                  <span>Muito Intensa</span>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Tipo de Treino</Label>
                <Select
                  value={formData.training_type}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, training_type: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione o tipo" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="técnico">Técnico</SelectItem>
                    <SelectItem value="físico">Físico</SelectItem>
                    <SelectItem value="tático">Tático</SelectItem>
                    <SelectItem value="jogo">Jogo</SelectItem>
                    <SelectItem value="recuperação">Recuperação</SelectItem>
                    <SelectItem value="misto">Misto</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {formData.training_duration && formData.training_intensity[0] && (
                <div className="p-3 bg-blue-50 rounded-lg">
                  <div className="flex items-center gap-2 text-blue-700">
                    <Target className="h-4 w-4" />
                    <span className="font-medium">Carga de Treino Calculada</span>
                  </div>
                  <div className="text-2xl font-bold text-blue-800 mt-1">
                    {calculateTrainingLoad()} AU
                  </div>
                  <div className="text-xs text-blue-600">
                    Duração × Intensidade = Carga Total
                  </div>
                </div>
              )}
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label>Distância Percorrida (km)</Label>
                <Input
                  type="number"
                  min="0"
                  max="20"
                  step="0.1"
                  value={formData.distance_covered}
                  onChange={(e) => setFormData(prev => ({ ...prev, distance_covered: e.target.value }))}
                  placeholder="Ex: 8.5"
                />
              </div>

              <div className="space-y-2">
                <Label>Número de Sprints</Label>
                <Input
                  type="number"
                  min="0"
                  max="100"
                  value={formData.sprint_count}
                  onChange={(e) => setFormData(prev => ({ ...prev, sprint_count: e.target.value }))}
                  placeholder="Ex: 15"
                />
              </div>

              <div className="space-y-2">
                <Label>Velocidade Máxima (km/h)</Label>
                <Input
                  type="number"
                  min="0"
                  max="40"
                  step="0.1"
                  value={formData.max_speed}
                  onChange={(e) => setFormData(prev => ({ ...prev, max_speed: e.target.value }))}
                  placeholder="Ex: 28.5"
                />
              </div>

              <div className="grid grid-cols-2 gap-2">
                <div className="space-y-2">
                  <Label className="flex items-center gap-1">
                    <Heart className="h-3 w-3" />
                    FC Média
                  </Label>
                  <Input
                    type="number"
                    min="60"
                    max="220"
                    value={formData.heart_rate_avg}
                    onChange={(e) => setFormData(prev => ({ ...prev, heart_rate_avg: e.target.value }))}
                    placeholder="150"
                  />
                </div>

                <div className="space-y-2">
                  <Label className="flex items-center gap-1">
                    <TrendingUp className="h-3 w-3" />
                    FC Máx
                  </Label>
                  <Input
                    type="number"
                    min="100"
                    max="220"
                    value={formData.heart_rate_max}
                    onChange={(e) => setFormData(prev => ({ ...prev, heart_rate_max: e.target.value }))}
                    placeholder="185"
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <Label>
              RPE (Percepção de Esforço): {formData.perceived_exertion[0]}/10 ({getRPELabel(formData.perceived_exertion[0])})
            </Label>
            <Slider
              value={formData.perceived_exertion}
              onValueChange={(value) => setFormData(prev => ({ ...prev, perceived_exertion: value }))}
              max={10}
              min={1}
              step={1}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>Muito Fácil</span>
              <span>Muito Difícil</span>
            </div>
          </div>

          <Button type="submit" disabled={loading} className="w-full">
            {loading ? 'Registrando...' : 'Registrar Carga de Trabalho'}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}