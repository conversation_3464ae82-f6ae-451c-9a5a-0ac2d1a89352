import React, { useState } from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { toast } from '@/hooks/use-toast';
import { 
  Heart, 
  MapPin, 
  Calendar, 
  User, 
  Trophy, 
  Star,
  Eye,
  Phone,
  Mail,
  Globe,
  TrendingUp
} from 'lucide-react';
import { addToFavorites, removeFromFavorites, isFavorite, logProfileView } from '@/api/marketFavorites';
import type { MarketProfile } from '@/api/marketProfiles';

// =====================================================
// INTERFACE
// =====================================================

interface MarketProfileCardProps {
  profile: MarketProfile;
  onClick?: (profile: MarketProfile) => void;
  showFavoriteButton?: boolean;
  compact?: boolean;
}

// =====================================================
// COMPONENTE PRINCIPAL
// =====================================================

export function MarketProfileCard({ 
  profile, 
  onClick, 
  showFavoriteButton = true,
  compact = false 
}: MarketProfileCardProps) {
  const [isFav, setIsFav] = useState(false);
  const [favLoading, setFavLoading] = useState(false);

  // Verificar se é favorito ao montar o componente
  React.useEffect(() => {
    checkIfFavorite();
  }, [profile.id]);

  const checkIfFavorite = async () => {
    try {
      const favorite = await isFavorite(profile.id);
      setIsFav(favorite);
    } catch (error) {
      // Ignorar erro silenciosamente
    }
  };

  // =====================================================
  // FUNÇÕES AUXILIARES
  // =====================================================

  const calculateAge = (birthDate: string): number => {
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }
    
    return age;
  };

  const getProfileTypeLabel = (type: string): string => {
    switch (type) {
      case 'player':
        return 'Jogador';
      case 'technical_staff':
        return 'Comissão Técnica';
      case 'support_staff':
        return 'Staff de Apoio';
      default:
        return type;
    }
  };

  const getAvailabilityLabel = (status: string): string => {
    switch (status) {
      case 'available':
        return 'Disponível';
      case 'contracted':
        return 'Contratado';
      case 'on_loan':
        return 'Emprestado';
      case 'injured':
        return 'Lesionado';
      case 'retired':
        return 'Aposentado';
      default:
        return status;
    }
  };

  const getAvailabilityColor = (status: string): string => {
    switch (status) {
      case 'available':
        return 'bg-green-100 text-green-800';
      case 'contracted':
        return 'bg-blue-100 text-blue-800';
      case 'on_loan':
        return 'bg-yellow-100 text-yellow-800';
      case 'injured':
        return 'bg-red-100 text-red-800';
      case 'retired':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  // =====================================================
  // HANDLERS
  // =====================================================

  const handleCardClick = async () => {
    // Registrar visualização
    try {
      await logProfileView(profile.id);
    } catch (error) {
      // Ignorar erro silenciosamente
    }

    if (onClick) {
      onClick(profile);
    }
  };

  const handleFavoriteToggle = async (e: React.MouseEvent) => {
    e.stopPropagation(); // Evitar trigger do onClick do card
    
    try {
      setFavLoading(true);
      
      if (isFav) {
        await removeFromFavorites(profile.id);
        setIsFav(false);
        toast({
          title: "Removido dos favoritos",
          description: `${profile.full_name} foi removido dos seus favoritos`
        });
      } else {
        await addToFavorites(profile.id);
        setIsFav(true);
        toast({
          title: "Adicionado aos favoritos",
          description: `${profile.full_name} foi adicionado aos seus favoritos`
        });
      }
    } catch (error: any) {
      toast({
        title: "Erro",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setFavLoading(false);
    }
  };

  // =====================================================
  // RENDER
  // =====================================================

  const age = calculateAge(profile.birth_date);
  const profileTypeLabel = getProfileTypeLabel(profile.profile_type);
  const availabilityLabel = getAvailabilityLabel(profile.availability_status);
  const availabilityColor = getAvailabilityColor(profile.availability_status);

  return (
    <Card 
      className={`cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-[1.02] ${
        compact ? 'h-auto' : 'h-full'
      }`}
      onClick={handleCardClick}
    >
      <CardHeader className={`pb-3 ${compact ? 'p-4' : ''}`}>
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <Avatar className={compact ? "h-12 w-12" : "h-16 w-16"}>
              <AvatarImage 
                src={profile.profile_photo_url} 
                alt={profile.full_name}
              />
              <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white font-semibold">
                {profile.full_name.split(' ').map(n => n[0]).join('').substring(0, 2)}
              </AvatarFallback>
            </Avatar>
            
            <div className="flex-1 min-w-0">
              <h3 className={`font-semibold text-gray-900 truncate ${
                compact ? 'text-sm' : 'text-lg'
              }`}>
                {profile.full_name}
              </h3>
              {profile.nickname && (
                <p className={`text-gray-600 truncate ${
                  compact ? 'text-xs' : 'text-sm'
                }`}>
                  "{profile.nickname}"
                </p>
              )}
              <div className="flex items-center gap-2 mt-1">
                <Badge variant="outline" className={compact ? 'text-xs px-1 py-0' : ''}>
                  {profileTypeLabel}
                </Badge>
                <Badge className={`${availabilityColor} ${compact ? 'text-xs px-1 py-0' : ''}`}>
                  {availabilityLabel}
                </Badge>
              </div>
            </div>
          </div>

          {showFavoriteButton && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleFavoriteToggle}
              disabled={favLoading}
              className="p-1 h-auto"
            >
              <Heart 
                className={`w-5 h-5 ${
                  isFav ? 'fill-red-500 text-red-500' : 'text-gray-400'
                }`} 
              />
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent className={`space-y-3 ${compact ? 'p-4 pt-0' : 'pt-0'}`}>
        {/* Informações Básicas */}
        <div className="grid grid-cols-2 gap-2 text-sm">
          <div className="flex items-center text-gray-600">
            <Calendar className="w-4 h-4 mr-1" />
            {age} anos
          </div>
          <div className="flex items-center text-gray-600">
            <Globe className="w-4 h-4 mr-1" />
            {profile.nationality}
          </div>
          {profile.height && (
            <div className="flex items-center text-gray-600">
              <User className="w-4 h-4 mr-1" />
              {profile.height}cm
            </div>
          )}
          {profile.years_experience > 0 && (
            <div className="flex items-center text-gray-600">
              <Trophy className="w-4 h-4 mr-1" />
              {profile.years_experience} anos
            </div>
          )}
        </div>

        {/* Posição/Função */}
        <div>
          <p className="text-sm font-medium text-gray-900">
            {profile.position || profile.role}
          </p>
          {profile.last_club && (
            <p className="text-xs text-gray-600">
              Último clube: {profile.last_club}
            </p>
          )}
        </div>

        {/* Características específicas para jogadores */}
        {profile.profile_type === 'player' && !compact && (
          <>
            {/* Avaliações Técnicas */}
            {(profile.speed_rating || profile.finishing_rating || profile.passing_rating) && (
              <div className="space-y-1">
                <p className="text-xs font-medium text-gray-700">Avaliações:</p>
                <div className="flex gap-2 text-xs">
                  {profile.speed_rating && (
                    <span className="flex items-center">
                      <Star className="w-3 h-3 mr-1 text-yellow-500" />
                      Vel: {profile.speed_rating}
                    </span>
                  )}
                  {profile.finishing_rating && (
                    <span className="flex items-center">
                      <Star className="w-3 h-3 mr-1 text-yellow-500" />
                      Fin: {profile.finishing_rating}
                    </span>
                  )}
                  {profile.passing_rating && (
                    <span className="flex items-center">
                      <Star className="w-3 h-3 mr-1 text-yellow-500" />
                      Pas: {profile.passing_rating}
                    </span>
                  )}
                </div>
              </div>
            )}

            {/* Valor de Mercado */}
            {profile.market_value_estimate && (
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-600">Valor estimado:</span>
                <span className="text-sm font-semibold text-green-600">
                  {formatCurrency(profile.market_value_estimate)}
                </span>
              </div>
            )}

            {/* Pé Preferido */}
            {profile.preferred_foot && (
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-600">Pé preferido:</span>
                <span className="text-xs font-medium">
                  {profile.preferred_foot}
                </span>
              </div>
            )}
          </>
        )}

        {/* Disponibilidade para viagem/mudança */}
        {!compact && (
          <div className="flex gap-2">
            {profile.available_for_travel && (
              <Badge variant="outline" className="text-xs">
                Viaja
              </Badge>
            )}
            {profile.available_for_relocation && (
              <Badge variant="outline" className="text-xs">
                Muda
              </Badge>
            )}
            {profile.has_eu_passport && (
              <Badge variant="outline" className="text-xs">
                UE
              </Badge>
            )}
          </div>
        )}

        {/* Idiomas */}
        {profile.languages_spoken && profile.languages_spoken.length > 0 && !compact && (
          <div>
            <p className="text-xs text-gray-600 mb-1">Idiomas:</p>
            <div className="flex flex-wrap gap-1">
              {profile.languages_spoken.slice(0, 3).map((language) => (
                <Badge key={language} variant="secondary" className="text-xs">
                  {language}
                </Badge>
              ))}
              {profile.languages_spoken.length > 3 && (
                <Badge variant="secondary" className="text-xs">
                  +{profile.languages_spoken.length - 3}
                </Badge>
              )}
            </div>
          </div>
        )}

        {/* Descrição (truncada) */}
        {profile.personal_description && !compact && (
          <div>
            <p className="text-xs text-gray-600 line-clamp-2">
              {profile.personal_description}
            </p>
          </div>
        )}

        {/* Status da Assinatura */}
        <div className="flex items-center justify-between pt-2 border-t">
          <div className="flex items-center text-xs text-gray-500">
            <Eye className="w-3 h-3 mr-1" />
            Perfil ativo
          </div>
          <Badge 
            variant={profile.subscription_status === 'active' ? 'default' : 'secondary'}
            className="text-xs"
          >
            {profile.subscription_status === 'active' ? 'Premium' : 'Trial'}
          </Badge>
        </div>
      </CardContent>
    </Card>
  );
}
