import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { toast } from '@/hooks/use-toast';
import { 
  ArrowLeft, 
  Heart, 
  Share2, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar, 
  User, 
  Trophy, 
  Star,
  Globe,
  Download,
  Play,
  Eye,
  TrendingUp,
  Award,
  Languages,
  Briefcase
} from 'lucide-react';
import { addToFavorites, removeFromFavorites, isFavorite, getProfileViewStats } from '@/api/marketFavorites';
import { getCareerHistory, type CareerHistory } from '@/api/marketProfiles';
import type { MarketProfile } from '@/api/marketProfiles';

// =====================================================
// INTERFACE
// =====================================================

interface MarketProfileDetailsProps {
  profile: MarketProfile;
  onBack: () => void;
}

// =====================================================
// COMPONENTE PRINCIPAL
// =====================================================

export function MarketProfileDetails({ profile, onBack }: MarketProfileDetailsProps) {
  const [isFav, setIsFav] = useState(false);
  const [favLoading, setFavLoading] = useState(false);
  const [careerHistory, setCareerHistory] = useState<CareerHistory[]>([]);
  const [viewStats, setViewStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadProfileData();
  }, [profile.id]);

  const loadProfileData = async () => {
    try {
      setLoading(true);
      
      // Verificar se é favorito
      const favorite = await isFavorite(profile.id);
      setIsFav(favorite);
      
      // Carregar histórico profissional
      const history = await getCareerHistory(profile.id);
      setCareerHistory(history);
      
      // Carregar estatísticas de visualização (apenas se for o próprio perfil)
      // Por enquanto, vamos simular dados
      
    } catch (error: any) {
      console.error('Erro ao carregar dados do perfil:', error);
    } finally {
      setLoading(false);
    }
  };

  // =====================================================
  // FUNÇÕES AUXILIARES
  // =====================================================

  const calculateAge = (birthDate: string): number => {
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }
    
    return age;
  };

  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  const getProfileTypeLabel = (type: string): string => {
    switch (type) {
      case 'player':
        return 'Jogador';
      case 'technical_staff':
        return 'Comissão Técnica';
      case 'support_staff':
        return 'Staff de Apoio';
      default:
        return type;
    }
  };

  const getAvailabilityLabel = (status: string): string => {
    switch (status) {
      case 'available':
        return 'Disponível';
      case 'contracted':
        return 'Contratado';
      case 'on_loan':
        return 'Emprestado';
      case 'injured':
        return 'Lesionado';
      case 'retired':
        return 'Aposentado';
      default:
        return status;
    }
  };

  const getAvailabilityColor = (status: string): string => {
    switch (status) {
      case 'available':
        return 'bg-green-100 text-green-800';
      case 'contracted':
        return 'bg-blue-100 text-blue-800';
      case 'on_loan':
        return 'bg-yellow-100 text-yellow-800';
      case 'injured':
        return 'bg-red-100 text-red-800';
      case 'retired':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // =====================================================
  // HANDLERS
  // =====================================================

  const handleFavoriteToggle = async () => {
    try {
      setFavLoading(true);
      
      if (isFav) {
        await removeFromFavorites(profile.id);
        setIsFav(false);
        toast({
          title: "Removido dos favoritos",
          description: `${profile.full_name} foi removido dos seus favoritos`
        });
      } else {
        await addToFavorites(profile.id);
        setIsFav(true);
        toast({
          title: "Adicionado aos favoritos",
          description: `${profile.full_name} foi adicionado aos seus favoritos`
        });
      }
    } catch (error: any) {
      toast({
        title: "Erro",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setFavLoading(false);
    }
  };

  const handleShare = async () => {
    try {
      if (navigator.share) {
        await navigator.share({
          title: `Perfil de ${profile.full_name}`,
          text: `Confira o perfil de ${profile.full_name} - ${profile.position || profile.role}`,
          url: window.location.href
        });
      } else {
        // Fallback: copiar URL para clipboard
        await navigator.clipboard.writeText(window.location.href);
        toast({
          title: "Link copiado",
          description: "Link do perfil copiado para a área de transferência"
        });
      }
    } catch (error) {
      toast({
        title: "Erro",
        description: "Não foi possível compartilhar o perfil",
        variant: "destructive"
      });
    }
  };

  const handleContactClick = (type: 'email' | 'phone') => {
    if (type === 'email' && profile.email) {
      window.open(`mailto:${profile.email}`, '_blank');
    } else if (type === 'phone' && profile.phone) {
      window.open(`tel:${profile.phone}`, '_blank');
    }
  };

  // =====================================================
  // RENDER
  // =====================================================

  const age = calculateAge(profile.birth_date);
  const profileTypeLabel = getProfileTypeLabel(profile.profile_type);
  const availabilityLabel = getAvailabilityLabel(profile.availability_status);
  const availabilityColor = getAvailabilityColor(profile.availability_status);

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header com botão voltar */}
      <div className="flex items-center gap-4">
        <Button variant="outline" onClick={onBack} className="flex items-center gap-2">
          <ArrowLeft className="w-4 h-4" />
          Voltar
        </Button>
        <div className="flex-1" />
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleFavoriteToggle}
            disabled={favLoading}
            className="flex items-center gap-2"
          >
            <Heart className={`w-4 h-4 ${isFav ? 'fill-red-500 text-red-500' : ''}`} />
            {isFav ? 'Remover dos Favoritos' : 'Adicionar aos Favoritos'}
          </Button>
          <Button variant="outline" onClick={handleShare} className="flex items-center gap-2">
            <Share2 className="w-4 h-4" />
            Compartilhar
          </Button>
        </div>
      </div>

      {/* Cabeçalho do Perfil */}
      <Card>
        <CardHeader>
          <div className="flex flex-col md:flex-row gap-6">
            {/* Avatar e Info Básica */}
            <div className="flex items-start gap-4">
              <Avatar className="h-24 w-24">
                <AvatarImage src={profile.profile_photo_url} alt={profile.full_name} />
                <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white text-xl font-semibold">
                  {profile.full_name.split(' ').map(n => n[0]).join('').substring(0, 2)}
                </AvatarFallback>
              </Avatar>
              
              <div className="flex-1">
                <h1 className="text-3xl font-bold text-gray-900">{profile.full_name}</h1>
                {profile.nickname && (
                  <p className="text-xl text-gray-600 mb-2">"{profile.nickname}"</p>
                )}
                
                <div className="flex flex-wrap gap-2 mb-3">
                  <Badge variant="outline" className="text-sm">
                    {profileTypeLabel}
                  </Badge>
                  <Badge className={`${availabilityColor} text-sm`}>
                    {availabilityLabel}
                  </Badge>
                  {profile.subscription_status === 'active' && (
                    <Badge className="bg-gold text-white text-sm">
                      Premium
                    </Badge>
                  )}
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div className="flex items-center text-gray-600">
                    <Calendar className="w-4 h-4 mr-2" />
                    {age} anos
                  </div>
                  <div className="flex items-center text-gray-600">
                    <Globe className="w-4 h-4 mr-2" />
                    {profile.nationality}
                  </div>
                  {profile.height && (
                    <div className="flex items-center text-gray-600">
                      <User className="w-4 h-4 mr-2" />
                      {profile.height}cm
                    </div>
                  )}
                  {profile.years_experience > 0 && (
                    <div className="flex items-center text-gray-600">
                      <Trophy className="w-4 h-4 mr-2" />
                      {profile.years_experience} anos
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Ações de Contato */}
            <div className="flex flex-col gap-2 min-w-[200px]">
              {profile.email && (
                <Button 
                  variant="outline" 
                  onClick={() => handleContactClick('email')}
                  className="flex items-center gap-2 justify-start"
                >
                  <Mail className="w-4 h-4" />
                  {profile.email}
                </Button>
              )}
              {profile.phone && (
                <Button 
                  variant="outline" 
                  onClick={() => handleContactClick('phone')}
                  className="flex items-center gap-2 justify-start"
                >
                  <Phone className="w-4 h-4" />
                  {profile.phone}
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Conteúdo Principal em Tabs */}
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Visão Geral</TabsTrigger>
          <TabsTrigger value="professional">Profissional</TabsTrigger>
          <TabsTrigger value="media">Mídia</TabsTrigger>
          <TabsTrigger value="contact">Contato</TabsTrigger>
        </TabsList>

        {/* Aba: Visão Geral */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Informações Principais */}
            <div className="lg:col-span-2 space-y-6">
              {/* Posição/Função */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Briefcase className="w-5 h-5" />
                    {profile.profile_type === 'player' ? 'Posição' : 'Função'}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-xl font-semibold">{profile.position || profile.role}</p>
                  {profile.last_club && (
                    <p className="text-gray-600 mt-1">Último clube: {profile.last_club}</p>
                  )}
                </CardContent>
              </Card>

              {/* Descrição Pessoal */}
              {profile.personal_description && (
                <Card>
                  <CardHeader>
                    <CardTitle>Sobre</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">
                      {profile.personal_description}
                    </p>
                  </CardContent>
                </Card>
              )}

              {/* Destaques da Carreira */}
              {profile.career_highlights && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Award className="w-5 h-5" />
                      Destaques da Carreira
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">
                      {profile.career_highlights}
                    </p>
                  </CardContent>
                </Card>
              )}
            </div>

            {/* Sidebar com informações adicionais */}
            <div className="space-y-6">
              {/* Características Físicas */}
              <Card>
                <CardHeader>
                  <CardTitle>Características</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {profile.height && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Altura:</span>
                      <span className="font-medium">{profile.height} cm</span>
                    </div>
                  )}
                  {profile.weight && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Peso:</span>
                      <span className="font-medium">{profile.weight} kg</span>
                    </div>
                  )}
                  {profile.preferred_foot && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Pé preferido:</span>
                      <span className="font-medium">{profile.preferred_foot}</span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-gray-600">Experiência:</span>
                    <span className="font-medium">{profile.years_experience} anos</span>
                  </div>
                </CardContent>
              </Card>

              {/* Disponibilidade */}
              <Card>
                <CardHeader>
                  <CardTitle>Disponibilidade</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">Viagens:</span>
                    <Badge variant={profile.available_for_travel ? "default" : "secondary"}>
                      {profile.available_for_travel ? "Sim" : "Não"}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">Mudança:</span>
                    <Badge variant={profile.available_for_relocation ? "default" : "secondary"}>
                      {profile.available_for_relocation ? "Sim" : "Não"}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">Passaporte UE:</span>
                    <Badge variant={profile.has_eu_passport ? "default" : "secondary"}>
                      {profile.has_eu_passport ? "Sim" : "Não"}
                    </Badge>
                  </div>
                  {profile.contract_end_date && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Fim do contrato:</span>
                      <span className="font-medium">{formatDate(profile.contract_end_date)}</span>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Idiomas */}
              {profile.languages_spoken && profile.languages_spoken.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Languages className="w-5 h-5" />
                      Idiomas
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      {profile.languages_spoken.map((language) => (
                        <Badge key={language} variant="outline">
                          {language}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </TabsContent>

        {/* Aba: Profissional */}
        <TabsContent value="professional" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Avaliações Técnicas (apenas para jogadores) */}
            {profile.profile_type === 'player' && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Star className="w-5 h-5" />
                    Avaliações Técnicas
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {profile.speed_rating && (
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">Velocidade:</span>
                        <div className="flex items-center gap-2">
                          <div className="flex">
                            {Array.from({ length: 10 }).map((_, i) => (
                              <Star
                                key={i}
                                className={`w-4 h-4 ${
                                  i < profile.speed_rating! ? 'text-yellow-500 fill-yellow-500' : 'text-gray-300'
                                }`}
                              />
                            ))}
                          </div>
                          <span className="font-medium">{profile.speed_rating}/10</span>
                        </div>
                      </div>
                    )}
                    {profile.finishing_rating && (
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">Finalização:</span>
                        <div className="flex items-center gap-2">
                          <div className="flex">
                            {Array.from({ length: 10 }).map((_, i) => (
                              <Star
                                key={i}
                                className={`w-4 h-4 ${
                                  i < profile.finishing_rating! ? 'text-yellow-500 fill-yellow-500' : 'text-gray-300'
                                }`}
                              />
                            ))}
                          </div>
                          <span className="font-medium">{profile.finishing_rating}/10</span>
                        </div>
                      </div>
                    )}
                    {profile.passing_rating && (
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">Passe:</span>
                        <div className="flex items-center gap-2">
                          <div className="flex">
                            {Array.from({ length: 10 }).map((_, i) => (
                              <Star
                                key={i}
                                className={`w-4 h-4 ${
                                  i < profile.passing_rating! ? 'text-yellow-500 fill-yellow-500' : 'text-gray-300'
                                }`}
                              />
                            ))}
                          </div>
                          <span className="font-medium">{profile.passing_rating}/10</span>
                        </div>
                      </div>
                    )}
                    {profile.defending_rating && (
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">Defesa:</span>
                        <div className="flex items-center gap-2">
                          <div className="flex">
                            {Array.from({ length: 10 }).map((_, i) => (
                              <Star
                                key={i}
                                className={`w-4 h-4 ${
                                  i < profile.defending_rating! ? 'text-yellow-500 fill-yellow-500' : 'text-gray-300'
                                }`}
                              />
                            ))}
                          </div>
                          <span className="font-medium">{profile.defending_rating}/10</span>
                        </div>
                      </div>
                    )}
                    {profile.physical_rating && (
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">Físico:</span>
                        <div className="flex items-center gap-2">
                          <div className="flex">
                            {Array.from({ length: 10 }).map((_, i) => (
                              <Star
                                key={i}
                                className={`w-4 h-4 ${
                                  i < profile.physical_rating! ? 'text-yellow-500 fill-yellow-500' : 'text-gray-300'
                                }`}
                              />
                            ))}
                          </div>
                          <span className="font-medium">{profile.physical_rating}/10</span>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Informações Financeiras (apenas para jogadores) */}
            {profile.profile_type === 'player' && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="w-5 h-5" />
                    Informações Financeiras
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {profile.market_value_estimate && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Valor de Mercado:</span>
                      <span className="font-semibold text-green-600">
                        {formatCurrency(profile.market_value_estimate)}
                      </span>
                    </div>
                  )}
                  {profile.salary_expectation_min && profile.salary_expectation_max && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Expectativa Salarial:</span>
                      <span className="font-medium">
                        {formatCurrency(profile.salary_expectation_min)} - {formatCurrency(profile.salary_expectation_max)}
                      </span>
                    </div>
                  )}
                  {profile.contract_end_date && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Fim do Contrato:</span>
                      <span className="font-medium">{formatDate(profile.contract_end_date)}</span>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Certificações (para staff) */}
            {profile.profile_type !== 'player' && profile.certifications && profile.certifications.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Award className="w-5 h-5" />
                    Certificações
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {profile.certifications.map((cert) => (
                      <Badge key={cert} variant="outline" className="text-sm">
                        {cert}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Metodologia de Trabalho (para staff) */}
            {profile.profile_type !== 'player' && profile.work_methodology && (
              <Card>
                <CardHeader>
                  <CardTitle>Metodologia de Trabalho</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">
                    {profile.work_methodology}
                  </p>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Histórico Profissional */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Trophy className="w-5 h-5" />
                Histórico Profissional
              </CardTitle>
            </CardHeader>
            <CardContent>
              {careerHistory.length > 0 ? (
                <div className="space-y-4">
                  {careerHistory.map((history) => (
                    <div key={history.id} className="border-l-2 border-blue-200 pl-4 pb-4">
                      <div className="flex items-start justify-between">
                        <div>
                          <h4 className="font-semibold text-gray-900">{history.club_name}</h4>
                          {history.position_role && (
                            <p className="text-sm text-gray-600">{history.position_role}</p>
                          )}
                          <p className="text-xs text-gray-500">
                            {history.start_date && formatDate(history.start_date)} - {' '}
                            {history.end_date ? formatDate(history.end_date) : 'Atual'}
                          </p>
                        </div>
                      </div>
                      {history.achievements && (
                        <p className="text-sm text-gray-700 mt-2">{history.achievements}</p>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-4">
                  Nenhum histórico profissional cadastrado
                </p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Aba: Mídia */}
        <TabsContent value="media" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Fotos Adicionais */}
            {profile.additional_photos && profile.additional_photos.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Galeria de Fotos</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    {profile.additional_photos.map((photo, index) => (
                      <div key={index} className="aspect-square rounded-lg overflow-hidden">
                        <img
                          src={photo}
                          alt={`Foto ${index + 1}`}
                          className="w-full h-full object-cover hover:scale-105 transition-transform cursor-pointer"
                          onClick={() => window.open(photo, '_blank')}
                        />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Vídeos (apenas para jogadores) */}
            {profile.profile_type === 'player' && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Play className="w-5 h-5" />
                    Vídeos
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {profile.highlight_video_url && (
                    <div>
                      <h4 className="font-medium mb-2">Melhores Momentos</h4>
                      <Button
                        variant="outline"
                        onClick={() => window.open(profile.highlight_video_url!, '_blank')}
                        className="flex items-center gap-2"
                      >
                        <Play className="w-4 h-4" />
                        Assistir Vídeo
                      </Button>
                    </div>
                  )}
                  {profile.full_game_video_url && (
                    <div>
                      <h4 className="font-medium mb-2">Jogo Completo</h4>
                      <Button
                        variant="outline"
                        onClick={() => window.open(profile.full_game_video_url!, '_blank')}
                        className="flex items-center gap-2"
                      >
                        <Play className="w-4 h-4" />
                        Assistir Vídeo
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Documentos */}
            {profile.resume_document_url && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Download className="w-5 h-5" />
                    Documentos
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Button
                    variant="outline"
                    onClick={() => window.open(profile.resume_document_url!, '_blank')}
                    className="flex items-center gap-2"
                  >
                    <Download className="w-4 h-4" />
                    Baixar Currículo
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        {/* Aba: Contato */}
        <TabsContent value="contact" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Informações de Contato</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {profile.email && (
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Mail className="w-4 h-4 text-gray-500" />
                      <span className="text-gray-600">Email:</span>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleContactClick('email')}
                    >
                      {profile.email}
                    </Button>
                  </div>
                )}
                {profile.phone && (
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Phone className="w-4 h-4 text-gray-500" />
                      <span className="text-gray-600">Telefone:</span>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleContactClick('phone')}
                    >
                      {profile.phone}
                    </Button>
                  </div>
                )}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <MapPin className="w-4 h-4 text-gray-500" />
                    <span className="text-gray-600">Localização:</span>
                  </div>
                  <span className="font-medium">{profile.nationality}</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Estatísticas do Perfil</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Eye className="w-4 h-4 text-gray-500" />
                    <span className="text-gray-600">Perfil criado em:</span>
                  </div>
                  <span className="font-medium">{formatDate(profile.created_at)}</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4 text-gray-500" />
                    <span className="text-gray-600">Última atualização:</span>
                  </div>
                  <span className="font-medium">{formatDate(profile.updated_at)}</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <TrendingUp className="w-4 h-4 text-gray-500" />
                    <span className="text-gray-600">Status da assinatura:</span>
                  </div>
                  <Badge variant={profile.subscription_status === 'active' ? 'default' : 'secondary'}>
                    {profile.subscription_status === 'active' ? 'Premium' : 'Trial'}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
