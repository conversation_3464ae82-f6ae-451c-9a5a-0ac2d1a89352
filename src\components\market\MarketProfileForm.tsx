import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/hooks/use-toast';
import { Upload, X, Plus, Star } from 'lucide-react';
import { createMarketProfile, updateMarketProfile, getMyMarketProfile } from '@/api/marketProfiles';
import { uploadMarketFile, uploadMultiplePhotos } from '@/api/marketStorage';
import type { MarketProfile } from '@/api/marketProfiles';

// =====================================================
// SCHEMAS DE VALIDAÇÃO
// =====================================================

const baseSchema = z.object({
  full_name: z.string().min(2, 'Nome deve ter pelo menos 2 caracteres'),
  nickname: z.string().optional(),
  email: z.string().email('Email inválido'),
  phone: z.string().optional(),
  birth_date: z.string().min(1, 'Data de nascimento é obrigatória'),
  nationality: z.string().default('Brasil'),
  has_eu_passport: z.boolean().default(false),
  height: z.number().min(100).max(250).optional(),
  weight: z.number().min(30).max(200).optional(),
  profile_type: z.enum(['player', 'technical_staff', 'support_staff']),
  available_for_travel: z.boolean().default(true),
  available_for_relocation: z.boolean().default(true),
  personal_description: z.string().optional(),
});

const playerSchema = baseSchema.extend({
  position: z.string().min(1, 'Posição é obrigatória para jogadores'),
  preferred_foot: z.enum(['direito', 'esquerdo', 'ambos']).optional(),
  years_experience: z.number().min(0).max(50),
  last_club: z.string().optional(),
  career_highlights: z.string().optional(),
  speed_rating: z.number().min(1).max(10).optional(),
  finishing_rating: z.number().min(1).max(10).optional(),
  passing_rating: z.number().min(1).max(10).optional(),
  defending_rating: z.number().min(1).max(10).optional(),
  physical_rating: z.number().min(1).max(10).optional(),
  market_value_estimate: z.number().min(0).optional(),
  salary_expectation_min: z.number().min(0).optional(),
  salary_expectation_max: z.number().min(0).optional(),
  availability_status: z.enum(['available', 'contracted', 'on_loan', 'retired', 'injured']).default('available'),
  contract_end_date: z.string().optional(),
});

const staffSchema = baseSchema.extend({
  role: z.string().min(1, 'Função é obrigatória para staff'),
  years_experience: z.number().min(0).max(50),
  last_club: z.string().optional(),
  career_highlights: z.string().optional(),
  work_methodology: z.string().optional(),
  availability_status: z.enum(['available', 'contracted', 'on_loan', 'retired']).default('available'),
});

// =====================================================
// CONSTANTES
// =====================================================

const PLAYER_POSITIONS = [
  'Goleiro',
  'Zagueiro',
  'Lateral Direito',
  'Lateral Esquerdo',
  'Volante',
  'Meia Central',
  'Meia Atacante',
  'Ponta Direita',
  'Ponta Esquerda',
  'Atacante',
  'Centroavante'
];

const TECHNICAL_STAFF_ROLES = [
  'Técnico',
  'Auxiliar Técnico',
  'Preparador de Goleiro',
  'Preparador Físico',
  'Massagista'
];

const SUPPORT_STAFF_ROLES = [
  'Supervisor',
  'Roupeiro',
  'Analista de Desempenho',
  'Fisiologista',
  'Médico',
  'Fisioterapeuta',
  'Gerente de Futebol',
  'Gerente Administrativo',
  'Secretário(a)',
  'Cozinheiro(a)',
  'Psicólogo(a)',
  'Nutricionista',
  'Serviços Gerais',
  'CEO',
  'Motorista',
  'Jardineiro',
  'Porteiro',
  'Segurança'
];

const LANGUAGES = [
  'Português',
  'Inglês',
  'Espanhol',
  'Francês',
  'Italiano',
  'Alemão',
  'Holandês',
  'Russo',
  'Japonês',
  'Árabe'
];

const CERTIFICATIONS = [
  'Licença CBF A',
  'Licença CBF B',
  'Licença CBF C',
  'UEFA A',
  'UEFA B',
  'UEFA Pro',
  'CONMEBOL A',
  'CONMEBOL B',
  'Preparação Física CREF',
  'Fisioterapia CREFITO',
  'Medicina Esportiva CRM'
];

// =====================================================
// COMPONENTE PRINCIPAL
// =====================================================

interface MarketProfileFormProps {
  onSuccess?: (profile: MarketProfile) => void;
  onCancel?: () => void;
  editMode?: boolean;
  initialData?: MarketProfile;
}

export function MarketProfileForm({ onSuccess, onCancel, editMode = false, initialData }: MarketProfileFormProps) {
  const [loading, setLoading] = useState(false);
  const [profileType, setProfileType] = useState<'player' | 'technical_staff' | 'support_staff'>('player');
  const [selectedLanguages, setSelectedLanguages] = useState<string[]>([]);
  const [selectedCertifications, setSelectedCertifications] = useState<string[]>([]);
  const [profilePhoto, setProfilePhoto] = useState<File | null>(null);
  const [profilePhotoUrl, setProfilePhotoUrl] = useState<string>('');
  const [additionalPhotos, setAdditionalPhotos] = useState<File[]>([]);
  const [highlightVideo, setHighlightVideo] = useState<File | null>(null);
  const [fullGameVideo, setFullGameVideo] = useState<File | null>(null);
  const [resumeDocument, setResumeDocument] = useState<File | null>(null);

  // Determinar schema baseado no tipo de perfil
  const getSchema = () => {
    switch (profileType) {
      case 'player':
        return playerSchema;
      case 'technical_staff':
      case 'support_staff':
        return staffSchema;
      default:
        return baseSchema;
    }
  };

  const form = useForm({
    resolver: zodResolver(getSchema()),
    defaultValues: {
      profile_type: 'player',
      nationality: 'Brasil',
      has_eu_passport: false,
      available_for_travel: true,
      available_for_relocation: true,
      availability_status: 'available',
      years_experience: 0,
      ...initialData
    }
  });

  // Carregar dados existentes se estiver em modo de edição
  useEffect(() => {
    if (editMode && initialData) {
      setProfileType(initialData.profile_type);
      setSelectedLanguages(initialData.languages_spoken || []);
      setSelectedCertifications(initialData.certifications || []);
      setProfilePhotoUrl(initialData.profile_photo_url || '');
      form.reset(initialData);
    }
  }, [editMode, initialData, form]);

  // Atualizar schema quando o tipo de perfil mudar
  useEffect(() => {
    form.setValue('profile_type', profileType);
  }, [profileType, form]);

  // =====================================================
  // FUNÇÕES DE UPLOAD
  // =====================================================

  const handleProfilePhotoUpload = async (file: File) => {
    try {
      setLoading(true);
      const result = await uploadMarketFile(file, 'profile-photo');
      setProfilePhotoUrl(result.url);
      toast({
        title: "Sucesso",
        description: "Foto de perfil enviada com sucesso"
      });
    } catch (error: any) {
      toast({
        title: "Erro",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleVideoUpload = async (file: File, type: 'highlight' | 'full-game') => {
    try {
      setLoading(true);
      const fileType = type === 'highlight' ? 'highlight-video' : 'full-game-video';
      const result = await uploadMarketFile(file, fileType);
      
      if (type === 'highlight') {
        form.setValue('highlight_video_url', result.url);
      } else {
        form.setValue('full_game_video_url', result.url);
      }
      
      toast({
        title: "Sucesso",
        description: `Vídeo ${type === 'highlight' ? 'de melhores momentos' : 'completo'} enviado com sucesso`
      });
    } catch (error: any) {
      toast({
        title: "Erro",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDocumentUpload = async (file: File) => {
    try {
      setLoading(true);
      const result = await uploadMarketFile(file, 'resume');
      form.setValue('resume_document_url', result.url);
      toast({
        title: "Sucesso",
        description: "Currículo enviado com sucesso"
      });
    } catch (error: any) {
      toast({
        title: "Erro",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  // =====================================================
  // FUNÇÃO DE SUBMIT
  // =====================================================

  const onSubmit = async (data: any) => {
    try {
      setLoading(true);

      // Preparar dados para envio
      const profileData = {
        ...data,
        languages_spoken: selectedLanguages,
        certifications: selectedCertifications,
        profile_photo_url: profilePhotoUrl,
      };

      let result: MarketProfile;
      
      if (editMode && initialData) {
        result = await updateMarketProfile(initialData.id, profileData);
        toast({
          title: "Sucesso",
          description: "Perfil atualizado com sucesso"
        });
      } else {
        result = await createMarketProfile(profileData);
        toast({
          title: "Sucesso",
          description: "Perfil criado com sucesso"
        });
      }

      if (onSuccess) {
        onSuccess(result);
      }
    } catch (error: any) {
      toast({
        title: "Erro",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <Card>
        <CardHeader>
          <CardTitle>
            {editMode ? 'Editar Perfil de Mercado' : 'Criar Perfil de Mercado'}
          </CardTitle>
          <CardDescription>
            {editMode 
              ? 'Atualize suas informações profissionais'
              : 'Crie seu perfil para ser encontrado por clubes'
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <Tabs defaultValue="basic" className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="basic">Dados Básicos</TabsTrigger>
                <TabsTrigger value="professional">Profissional</TabsTrigger>
                <TabsTrigger value="media">Mídia</TabsTrigger>
                <TabsTrigger value="additional">Adicional</TabsTrigger>
              </TabsList>

              {/* Aba: Dados Básicos */}
              <TabsContent value="basic" className="space-y-4">
                {/* Tipo de Perfil */}
                <div className="space-y-2">
                  <Label>Tipo de Perfil *</Label>
                  <Select 
                    value={profileType} 
                    onValueChange={(value: any) => setProfileType(value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="player">Jogador</SelectItem>
                      <SelectItem value="technical_staff">Comissão Técnica</SelectItem>
                      <SelectItem value="support_staff">Staff de Apoio</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Dados Pessoais */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="full_name">Nome Completo *</Label>
                    <Input
                      id="full_name"
                      {...form.register('full_name')}
                      placeholder="Seu nome completo"
                    />
                    {form.formState.errors.full_name && (
                      <p className="text-sm text-red-500">
                        {form.formState.errors.full_name.message}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="nickname">Apelido</Label>
                    <Input
                      id="nickname"
                      {...form.register('nickname')}
                      placeholder="Como é conhecido"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email">Email *</Label>
                    <Input
                      id="email"
                      type="email"
                      {...form.register('email')}
                      placeholder="<EMAIL>"
                    />
                    {form.formState.errors.email && (
                      <p className="text-sm text-red-500">
                        {form.formState.errors.email.message}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone">Telefone</Label>
                    <Input
                      id="phone"
                      {...form.register('phone')}
                      placeholder="(11) 99999-9999"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="birth_date">Data de Nascimento *</Label>
                    <Input
                      id="birth_date"
                      type="date"
                      {...form.register('birth_date')}
                    />
                    {form.formState.errors.birth_date && (
                      <p className="text-sm text-red-500">
                        {form.formState.errors.birth_date.message}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="nationality">Nacionalidade</Label>
                    <Input
                      id="nationality"
                      {...form.register('nationality')}
                      placeholder="Brasil"
                    />
                  </div>
                </div>

                {/* Dados Físicos */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="height">Altura (cm)</Label>
                    <Input
                      id="height"
                      type="number"
                      {...form.register('height', { valueAsNumber: true })}
                      placeholder="180"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="weight">Peso (kg)</Label>
                    <Input
                      id="weight"
                      type="number"
                      step="0.1"
                      {...form.register('weight', { valueAsNumber: true })}
                      placeholder="75.5"
                    />
                  </div>

                  <div className="flex items-center space-x-2 pt-6">
                    <Checkbox
                      id="has_eu_passport"
                      {...form.register('has_eu_passport')}
                    />
                    <Label htmlFor="has_eu_passport">Passaporte Europeu</Label>
                  </div>
                </div>
              </TabsContent>

              {/* Aba: Profissional */}
              <TabsContent value="professional" className="space-y-4">
                {/* Posição/Função */}
                {profileType === 'player' ? (
                  <div className="space-y-2">
                    <Label htmlFor="position">Posição *</Label>
                    <Select onValueChange={(value) => form.setValue('position', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione sua posição" />
                      </SelectTrigger>
                      <SelectContent>
                        {PLAYER_POSITIONS.map((position) => (
                          <SelectItem key={position} value={position}>
                            {position}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {form.formState.errors.position && (
                      <p className="text-sm text-red-500">
                        {form.formState.errors.position.message}
                      </p>
                    )}
                  </div>
                ) : (
                  <div className="space-y-2">
                    <Label htmlFor="role">Função *</Label>
                    <Select onValueChange={(value) => form.setValue('role', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione sua função" />
                      </SelectTrigger>
                      <SelectContent>
                        {(profileType === 'technical_staff' ? TECHNICAL_STAFF_ROLES : SUPPORT_STAFF_ROLES).map((role) => (
                          <SelectItem key={role} value={role}>
                            {role}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {form.formState.errors.role && (
                      <p className="text-sm text-red-500">
                        {form.formState.errors.role.message}
                      </p>
                    )}
                  </div>
                )}

                {/* Experiência e Clube */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="years_experience">Anos de Experiência</Label>
                    <Input
                      id="years_experience"
                      type="number"
                      {...form.register('years_experience', { valueAsNumber: true })}
                      placeholder="5"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="last_club">Último Clube</Label>
                    <Input
                      id="last_club"
                      {...form.register('last_club')}
                      placeholder="Nome do último clube"
                    />
                  </div>
                </div>

                {/* Características específicas para jogadores */}
                {profileType === 'player' && (
                  <>
                    <div className="space-y-2">
                      <Label htmlFor="preferred_foot">Pé Preferido</Label>
                      <Select onValueChange={(value: any) => form.setValue('preferred_foot', value)}>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione o pé preferido" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="direito">Direito</SelectItem>
                          <SelectItem value="esquerdo">Esquerdo</SelectItem>
                          <SelectItem value="ambos">Ambos</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Avaliações Técnicas */}
                    <div className="space-y-4">
                      <Label>Avaliações Técnicas (1-10)</Label>
                      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="speed_rating">Velocidade</Label>
                          <Input
                            id="speed_rating"
                            type="number"
                            min="1"
                            max="10"
                            {...form.register('speed_rating', { valueAsNumber: true })}
                            placeholder="8"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="finishing_rating">Finalização</Label>
                          <Input
                            id="finishing_rating"
                            type="number"
                            min="1"
                            max="10"
                            {...form.register('finishing_rating', { valueAsNumber: true })}
                            placeholder="7"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="passing_rating">Passe</Label>
                          <Input
                            id="passing_rating"
                            type="number"
                            min="1"
                            max="10"
                            {...form.register('passing_rating', { valueAsNumber: true })}
                            placeholder="9"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="defending_rating">Defesa</Label>
                          <Input
                            id="defending_rating"
                            type="number"
                            min="1"
                            max="10"
                            {...form.register('defending_rating', { valueAsNumber: true })}
                            placeholder="6"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="physical_rating">Físico</Label>
                          <Input
                            id="physical_rating"
                            type="number"
                            min="1"
                            max="10"
                            {...form.register('physical_rating', { valueAsNumber: true })}
                            placeholder="8"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Valores e Expectativas */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="market_value_estimate">Valor de Mercado Estimado (R$)</Label>
                        <Input
                          id="market_value_estimate"
                          type="number"
                          {...form.register('market_value_estimate', { valueAsNumber: true })}
                          placeholder="50000"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="salary_expectation_min">Salário Mínimo (R$)</Label>
                        <Input
                          id="salary_expectation_min"
                          type="number"
                          {...form.register('salary_expectation_min', { valueAsNumber: true })}
                          placeholder="3000"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="salary_expectation_max">Salário Máximo (R$)</Label>
                        <Input
                          id="salary_expectation_max"
                          type="number"
                          {...form.register('salary_expectation_max', { valueAsNumber: true })}
                          placeholder="8000"
                        />
                      </div>
                    </div>

                    {/* Status de Disponibilidade */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="availability_status">Status de Disponibilidade</Label>
                        <Select onValueChange={(value: any) => form.setValue('availability_status', value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o status" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="available">Disponível</SelectItem>
                            <SelectItem value="contracted">Contratado</SelectItem>
                            <SelectItem value="on_loan">Emprestado</SelectItem>
                            <SelectItem value="injured">Lesionado</SelectItem>
                            <SelectItem value="retired">Aposentado</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="contract_end_date">Fim do Contrato</Label>
                        <Input
                          id="contract_end_date"
                          type="date"
                          {...form.register('contract_end_date')}
                        />
                      </div>
                    </div>
                  </>
                )}

                {/* Metodologia de trabalho para staff */}
                {profileType !== 'player' && (
                  <div className="space-y-2">
                    <Label htmlFor="work_methodology">Metodologia de Trabalho</Label>
                    <Textarea
                      id="work_methodology"
                      {...form.register('work_methodology')}
                      placeholder="Descreva sua metodologia e filosofia de trabalho..."
                      rows={4}
                    />
                  </div>
                )}

                {/* Destaques da Carreira */}
                <div className="space-y-2">
                  <Label htmlFor="career_highlights">Destaques da Carreira</Label>
                  <Textarea
                    id="career_highlights"
                    {...form.register('career_highlights')}
                    placeholder="Títulos, conquistas, experiências relevantes..."
                    rows={4}
                  />
                </div>
              </TabsContent>

              {/* Aba: Mídia */}
              <TabsContent value="media" className="space-y-4">
                {/* Foto de Perfil */}
                <div className="space-y-4">
                  <Label>Foto de Perfil</Label>
                  <div className="flex items-center space-x-4">
                    {profilePhotoUrl ? (
                      <div className="relative">
                        <img
                          src={profilePhotoUrl}
                          alt="Foto de perfil"
                          className="w-24 h-24 rounded-full object-cover"
                        />
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          className="absolute -top-2 -right-2 rounded-full w-6 h-6 p-0"
                          onClick={() => setProfilePhotoUrl('')}
                        >
                          <X className="w-3 h-3" />
                        </Button>
                      </div>
                    ) : (
                      <div className="w-24 h-24 rounded-full bg-gray-200 flex items-center justify-center">
                        <Upload className="w-8 h-8 text-gray-400" />
                      </div>
                    )}
                    <div>
                      <Input
                        type="file"
                        accept="image/*"
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) {
                            setProfilePhoto(file);
                            handleProfilePhotoUpload(file);
                          }
                        }}
                        className="hidden"
                        id="profile-photo-upload"
                      />
                      <Label htmlFor="profile-photo-upload" className="cursor-pointer">
                        <Button type="button" variant="outline" asChild>
                          <span>Escolher Foto</span>
                        </Button>
                      </Label>
                      <p className="text-sm text-gray-500 mt-1">
                        JPG, PNG ou WebP. Máximo 5MB.
                      </p>
                    </div>
                  </div>
                </div>

                {/* Vídeos - apenas para jogadores */}
                {profileType === 'player' && (
                  <>
                    {/* Vídeo de Melhores Momentos */}
                    <div className="space-y-2">
                      <Label>Vídeo de Melhores Momentos</Label>
                      <div className="flex items-center space-x-4">
                        <Input
                          type="file"
                          accept="video/*"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) {
                              setHighlightVideo(file);
                              handleVideoUpload(file, 'highlight');
                            }
                          }}
                          className="hidden"
                          id="highlight-video-upload"
                        />
                        <Label htmlFor="highlight-video-upload" className="cursor-pointer">
                          <Button type="button" variant="outline" asChild>
                            <span>
                              <Upload className="w-4 h-4 mr-2" />
                              Escolher Vídeo
                            </span>
                          </Button>
                        </Label>
                        <p className="text-sm text-gray-500">
                          MP4, AVI, MOV. Máximo 50MB.
                        </p>
                      </div>
                      {highlightVideo && (
                        <p className="text-sm text-green-600">
                          ✓ {highlightVideo.name}
                        </p>
                      )}
                    </div>

                    {/* Vídeo de Jogo Completo */}
                    <div className="space-y-2">
                      <Label>Vídeo de Jogo Completo</Label>
                      <div className="flex items-center space-x-4">
                        <Input
                          type="file"
                          accept="video/*"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) {
                              setFullGameVideo(file);
                              handleVideoUpload(file, 'full-game');
                            }
                          }}
                          className="hidden"
                          id="full-game-video-upload"
                        />
                        <Label htmlFor="full-game-video-upload" className="cursor-pointer">
                          <Button type="button" variant="outline" asChild>
                            <span>
                              <Upload className="w-4 h-4 mr-2" />
                              Escolher Vídeo
                            </span>
                          </Button>
                        </Label>
                        <p className="text-sm text-gray-500">
                          MP4, AVI, MOV. Máximo 100MB.
                        </p>
                      </div>
                      {fullGameVideo && (
                        <p className="text-sm text-green-600">
                          ✓ {fullGameVideo.name}
                        </p>
                      )}
                    </div>

                    {/* Links de Vídeo Alternativos */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="highlight_video_url">Link do Vídeo de Melhores Momentos</Label>
                        <Input
                          id="highlight_video_url"
                          {...form.register('highlight_video_url')}
                          placeholder="https://youtube.com/watch?v=..."
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="full_game_video_url">Link do Vídeo de Jogo Completo</Label>
                        <Input
                          id="full_game_video_url"
                          {...form.register('full_game_video_url')}
                          placeholder="https://youtube.com/watch?v=..."
                        />
                      </div>
                    </div>
                  </>
                )}

                {/* Currículo/Documento */}
                <div className="space-y-2">
                  <Label>Currículo Esportivo</Label>
                  <div className="flex items-center space-x-4">
                    <Input
                      type="file"
                      accept=".pdf,.doc,.docx"
                      onChange={(e) => {
                        const file = e.target.files?.[0];
                        if (file) {
                          setResumeDocument(file);
                          handleDocumentUpload(file);
                        }
                      }}
                      className="hidden"
                      id="resume-upload"
                    />
                    <Label htmlFor="resume-upload" className="cursor-pointer">
                      <Button type="button" variant="outline" asChild>
                        <span>
                          <Upload className="w-4 h-4 mr-2" />
                          Escolher Arquivo
                        </span>
                      </Button>
                    </Label>
                    <p className="text-sm text-gray-500">
                      PDF, DOC ou DOCX. Máximo 10MB.
                    </p>
                  </div>
                  {resumeDocument && (
                    <p className="text-sm text-green-600">
                      ✓ {resumeDocument.name}
                    </p>
                  )}
                </div>

                {/* Fotos Adicionais */}
                <div className="space-y-2">
                  <Label>Fotos Adicionais (até 6)</Label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    {Array.from({ length: 6 }).map((_, index) => (
                      <div key={index} className="space-y-2">
                        <div className="aspect-square bg-gray-100 rounded-lg flex items-center justify-center border-2 border-dashed border-gray-300">
                          <Upload className="w-8 h-8 text-gray-400" />
                        </div>
                        <Input
                          type="file"
                          accept="image/*"
                          className="text-xs"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) {
                              // Adicionar lógica para upload de fotos adicionais
                            }
                          }}
                        />
                      </div>
                    ))}
                  </div>
                </div>
              </TabsContent>

              {/* Aba: Adicional */}
              <TabsContent value="additional" className="space-y-4">
                {/* Idiomas */}
                <div className="space-y-4">
                  <Label>Idiomas</Label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                    {LANGUAGES.map((language) => (
                      <div key={language} className="flex items-center space-x-2">
                        <Checkbox
                          id={`language-${language}`}
                          checked={selectedLanguages.includes(language)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedLanguages([...selectedLanguages, language]);
                            } else {
                              setSelectedLanguages(selectedLanguages.filter(l => l !== language));
                            }
                          }}
                        />
                        <Label htmlFor={`language-${language}`} className="text-sm">
                          {language}
                        </Label>
                      </div>
                    ))}
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {selectedLanguages.map((language) => (
                      <Badge key={language} variant="secondary">
                        {language}
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="ml-1 h-auto p-0"
                          onClick={() => setSelectedLanguages(selectedLanguages.filter(l => l !== language))}
                        >
                          <X className="w-3 h-3" />
                        </Button>
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Certificações */}
                {profileType !== 'player' && (
                  <div className="space-y-4">
                    <Label>Certificações e Licenças</Label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {CERTIFICATIONS.map((cert) => (
                        <div key={cert} className="flex items-center space-x-2">
                          <Checkbox
                            id={`cert-${cert}`}
                            checked={selectedCertifications.includes(cert)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setSelectedCertifications([...selectedCertifications, cert]);
                              } else {
                                setSelectedCertifications(selectedCertifications.filter(c => c !== cert));
                              }
                            }}
                          />
                          <Label htmlFor={`cert-${cert}`} className="text-sm">
                            {cert}
                          </Label>
                        </div>
                      ))}
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {selectedCertifications.map((cert) => (
                        <Badge key={cert} variant="secondary">
                          {cert}
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="ml-1 h-auto p-0"
                            onClick={() => setSelectedCertifications(selectedCertifications.filter(c => c !== cert))}
                          >
                            <X className="w-3 h-3" />
                          </Button>
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* Disponibilidade */}
                <div className="space-y-4">
                  <Label>Disponibilidade</Label>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="available_for_travel"
                        {...form.register('available_for_travel')}
                      />
                      <Label htmlFor="available_for_travel">
                        Disponível para viagens
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="available_for_relocation"
                        {...form.register('available_for_relocation')}
                      />
                      <Label htmlFor="available_for_relocation">
                        Disponível para mudança de cidade/estado
                      </Label>
                    </div>
                  </div>
                </div>

                {/* Descrição Pessoal */}
                <div className="space-y-2">
                  <Label htmlFor="personal_description">Descrição Pessoal</Label>
                  <Textarea
                    id="personal_description"
                    {...form.register('personal_description')}
                    placeholder="Conte um pouco sobre você, seus objetivos, características pessoais..."
                    rows={6}
                  />
                  <p className="text-sm text-gray-500">
                    Esta descrição será exibida no seu perfil público.
                  </p>
                </div>
              </TabsContent>
            </Tabs>

            {/* Botões de Ação */}
            <div className="flex justify-end space-x-4 pt-6">
              {onCancel && (
                <Button type="button" variant="outline" onClick={onCancel}>
                  Cancelar
                </Button>
              )}
              <Button type="submit" disabled={loading}>
                {loading ? 'Salvando...' : editMode ? 'Atualizar Perfil' : 'Criar Perfil'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
