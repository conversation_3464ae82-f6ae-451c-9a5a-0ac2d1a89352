import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { X, RotateCcw } from 'lucide-react';
import type { MarketProfileFilters } from '@/api/marketProfiles';

// =====================================================
// CONSTANTES
// =====================================================

const PLAYER_POSITIONS = [
  'Goleiro',
  'Zagueiro',
  'Lateral Direito',
  'Lateral Esquerdo',
  'Volante',
  'Meia Central',
  'Meia Atacante',
  'Ponta Direita',
  'Ponta Esquerda',
  'Atacante',
  'Centroavante'
];

const TECHNICAL_STAFF_ROLES = [
  'Técnico',
  'Auxiliar Técnico',
  'Preparador de Goleiro',
  'Preparador Físico',
  'Massagista'
];

const SUPPORT_STAFF_ROLES = [
  'Supervisor',
  'Roupeiro',
  'Analista de Desempenho',
  'Fisiologista',
  'Médico',
  'Fisioterapeuta',
  'Gerente de Futebol',
  'Gerente Administrativo',
  'Secretário(a)',
  'Cozinheiro(a)',
  'Psicólogo(a)',
  'Nutricionista',
  'Serviços Gerais',
  'CEO',
  'Motorista',
  'Jardineiro',
  'Porteiro',
  'Segurança'
];

const AVAILABILITY_STATUS = [
  { value: 'available', label: 'Disponível' },
  { value: 'contracted', label: 'Contratado' },
  { value: 'on_loan', label: 'Emprestado' },
  { value: 'injured', label: 'Lesionado' },
  { value: 'retired', label: 'Aposentado' }
];

const NATIONALITIES = [
  'Brasil',
  'Argentina',
  'Uruguai',
  'Paraguai',
  'Chile',
  'Colômbia',
  'Venezuela',
  'Peru',
  'Equador',
  'Bolívia',
  'Portugal',
  'Espanha',
  'França',
  'Itália',
  'Alemanha',
  'Inglaterra',
  'Holanda',
  'Bélgica'
];

// =====================================================
// INTERFACE
// =====================================================

interface MarketSearchFiltersProps {
  filters: MarketProfileFilters;
  onFiltersChange: (filters: MarketProfileFilters) => void;
}

// =====================================================
// COMPONENTE PRINCIPAL
// =====================================================

export function MarketSearchFilters({ filters, onFiltersChange }: MarketSearchFiltersProps) {
  const [localFilters, setLocalFilters] = useState<MarketProfileFilters>(filters);
  const [selectedPositions, setSelectedPositions] = useState<string[]>(filters.position || []);
  const [selectedAvailability, setSelectedAvailability] = useState<string[]>(filters.availability_status || []);

  // Sincronizar com props quando mudarem
  useEffect(() => {
    setLocalFilters(filters);
    setSelectedPositions(filters.position || []);
    setSelectedAvailability(filters.availability_status || []);
  }, [filters]);

  // =====================================================
  // FUNÇÕES AUXILIARES
  // =====================================================

  const updateFilters = (newFilters: Partial<MarketProfileFilters>) => {
    const updated = { ...localFilters, ...newFilters };
    setLocalFilters(updated);
    onFiltersChange(updated);
  };

  const clearAllFilters = () => {
    const emptyFilters: MarketProfileFilters = {};
    setLocalFilters(emptyFilters);
    setSelectedPositions([]);
    setSelectedAvailability([]);
    onFiltersChange(emptyFilters);
  };

  const handlePositionToggle = (position: string) => {
    const newPositions = selectedPositions.includes(position)
      ? selectedPositions.filter(p => p !== position)
      : [...selectedPositions, position];
    
    setSelectedPositions(newPositions);
    updateFilters({ position: newPositions.length > 0 ? newPositions : undefined });
  };

  const handleAvailabilityToggle = (status: string) => {
    const newAvailability = selectedAvailability.includes(status)
      ? selectedAvailability.filter(s => s !== status)
      : [...selectedAvailability, status];
    
    setSelectedAvailability(newAvailability);
    updateFilters({ availability_status: newAvailability.length > 0 ? newAvailability : undefined });
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (localFilters.profile_type) count++;
    if (localFilters.position && localFilters.position.length > 0) count++;
    if (localFilters.age_min || localFilters.age_max) count++;
    if (localFilters.height_min || localFilters.height_max) count++;
    if (localFilters.nationality) count++;
    if (localFilters.availability_status && localFilters.availability_status.length > 0) count++;
    if (localFilters.market_value_min || localFilters.market_value_max) count++;
    if (localFilters.experience_min || localFilters.experience_max) count++;
    if (localFilters.available_for_travel !== undefined) count++;
    if (localFilters.available_for_relocation !== undefined) count++;
    if (localFilters.has_eu_passport !== undefined) count++;
    return count;
  };

  const activeFiltersCount = getActiveFiltersCount();

  // =====================================================
  // RENDER
  // =====================================================

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Filtros de Busca</CardTitle>
          <div className="flex items-center gap-2">
            {activeFiltersCount > 0 && (
              <Badge variant="secondary">
                {activeFiltersCount} filtro{activeFiltersCount > 1 ? 's' : ''} ativo{activeFiltersCount > 1 ? 's' : ''}
              </Badge>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={clearAllFilters}
              className="flex items-center gap-1"
            >
              <RotateCcw className="w-3 h-3" />
              Limpar
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Tipo de Perfil */}
        <div className="space-y-2">
          <Label>Tipo de Perfil</Label>
          <Select
            value={localFilters.profile_type || ''}
            onValueChange={(value) => updateFilters({ 
              profile_type: value as any || undefined 
            })}
          >
            <SelectTrigger>
              <SelectValue placeholder="Todos os tipos" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">Todos os tipos</SelectItem>
              <SelectItem value="player">Jogadores</SelectItem>
              <SelectItem value="technical_staff">Comissão Técnica</SelectItem>
              <SelectItem value="support_staff">Staff de Apoio</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Posições/Funções */}
        {localFilters.profile_type && (
          <div className="space-y-3">
            <Label>
              {localFilters.profile_type === 'player' ? 'Posições' : 'Funções'}
            </Label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              {(localFilters.profile_type === 'player' 
                ? PLAYER_POSITIONS 
                : localFilters.profile_type === 'technical_staff'
                ? TECHNICAL_STAFF_ROLES
                : SUPPORT_STAFF_ROLES
              ).map((item) => (
                <div key={item} className="flex items-center space-x-2">
                  <Checkbox
                    id={`position-${item}`}
                    checked={selectedPositions.includes(item)}
                    onCheckedChange={() => handlePositionToggle(item)}
                  />
                  <Label htmlFor={`position-${item}`} className="text-sm">
                    {item}
                  </Label>
                </div>
              ))}
            </div>
            {selectedPositions.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {selectedPositions.map((position) => (
                  <Badge key={position} variant="secondary" className="text-xs">
                    {position}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="ml-1 h-auto p-0"
                      onClick={() => handlePositionToggle(position)}
                    >
                      <X className="w-3 h-3" />
                    </Button>
                  </Badge>
                ))}
              </div>
            )}
          </div>
        )}

        <Separator />

        {/* Idade */}
        <div className="space-y-3">
          <Label>Idade</Label>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-1">
              <Label htmlFor="age_min" className="text-sm text-gray-600">Mínima</Label>
              <Input
                id="age_min"
                type="number"
                placeholder="18"
                value={localFilters.age_min || ''}
                onChange={(e) => updateFilters({ 
                  age_min: e.target.value ? parseInt(e.target.value) : undefined 
                })}
              />
            </div>
            <div className="space-y-1">
              <Label htmlFor="age_max" className="text-sm text-gray-600">Máxima</Label>
              <Input
                id="age_max"
                type="number"
                placeholder="35"
                value={localFilters.age_max || ''}
                onChange={(e) => updateFilters({ 
                  age_max: e.target.value ? parseInt(e.target.value) : undefined 
                })}
              />
            </div>
          </div>
        </div>

        {/* Altura (apenas para jogadores) */}
        {localFilters.profile_type === 'player' && (
          <div className="space-y-3">
            <Label>Altura (cm)</Label>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-1">
                <Label htmlFor="height_min" className="text-sm text-gray-600">Mínima</Label>
                <Input
                  id="height_min"
                  type="number"
                  placeholder="160"
                  value={localFilters.height_min || ''}
                  onChange={(e) => updateFilters({ 
                    height_min: e.target.value ? parseInt(e.target.value) : undefined 
                  })}
                />
              </div>
              <div className="space-y-1">
                <Label htmlFor="height_max" className="text-sm text-gray-600">Máxima</Label>
                <Input
                  id="height_max"
                  type="number"
                  placeholder="200"
                  value={localFilters.height_max || ''}
                  onChange={(e) => updateFilters({ 
                    height_max: e.target.value ? parseInt(e.target.value) : undefined 
                  })}
                />
              </div>
            </div>
          </div>
        )}

        <Separator />

        {/* Nacionalidade */}
        <div className="space-y-2">
          <Label>Nacionalidade</Label>
          <Select
            value={localFilters.nationality || ''}
            onValueChange={(value) => updateFilters({ 
              nationality: value || undefined 
            })}
          >
            <SelectTrigger>
              <SelectValue placeholder="Todas as nacionalidades" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">Todas as nacionalidades</SelectItem>
              {NATIONALITIES.map((nationality) => (
                <SelectItem key={nationality} value={nationality}>
                  {nationality}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Status de Disponibilidade */}
        <div className="space-y-3">
          <Label>Status de Disponibilidade</Label>
          <div className="grid grid-cols-2 gap-2">
            {AVAILABILITY_STATUS.map((status) => (
              <div key={status.value} className="flex items-center space-x-2">
                <Checkbox
                  id={`status-${status.value}`}
                  checked={selectedAvailability.includes(status.value)}
                  onCheckedChange={() => handleAvailabilityToggle(status.value)}
                />
                <Label htmlFor={`status-${status.value}`} className="text-sm">
                  {status.label}
                </Label>
              </div>
            ))}
          </div>
          {selectedAvailability.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {selectedAvailability.map((status) => {
                const statusLabel = AVAILABILITY_STATUS.find(s => s.value === status)?.label || status;
                return (
                  <Badge key={status} variant="secondary" className="text-xs">
                    {statusLabel}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="ml-1 h-auto p-0"
                      onClick={() => handleAvailabilityToggle(status)}
                    >
                      <X className="w-3 h-3" />
                    </Button>
                  </Badge>
                );
              })}
            </div>
          )}
        </div>

        <Separator />

        {/* Experiência */}
        <div className="space-y-3">
          <Label>Anos de Experiência</Label>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-1">
              <Label htmlFor="experience_min" className="text-sm text-gray-600">Mínima</Label>
              <Input
                id="experience_min"
                type="number"
                placeholder="0"
                value={localFilters.experience_min || ''}
                onChange={(e) => updateFilters({ 
                  experience_min: e.target.value ? parseInt(e.target.value) : undefined 
                })}
              />
            </div>
            <div className="space-y-1">
              <Label htmlFor="experience_max" className="text-sm text-gray-600">Máxima</Label>
              <Input
                id="experience_max"
                type="number"
                placeholder="20"
                value={localFilters.experience_max || ''}
                onChange={(e) => updateFilters({ 
                  experience_max: e.target.value ? parseInt(e.target.value) : undefined 
                })}
              />
            </div>
          </div>
        </div>

        {/* Valor de Mercado (apenas para jogadores) */}
        {localFilters.profile_type === 'player' && (
          <div className="space-y-3">
            <Label>Valor de Mercado (R$)</Label>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-1">
                <Label htmlFor="market_value_min" className="text-sm text-gray-600">Mínimo</Label>
                <Input
                  id="market_value_min"
                  type="number"
                  placeholder="10000"
                  value={localFilters.market_value_min || ''}
                  onChange={(e) => updateFilters({ 
                    market_value_min: e.target.value ? parseInt(e.target.value) : undefined 
                  })}
                />
              </div>
              <div className="space-y-1">
                <Label htmlFor="market_value_max" className="text-sm text-gray-600">Máximo</Label>
                <Input
                  id="market_value_max"
                  type="number"
                  placeholder="500000"
                  value={localFilters.market_value_max || ''}
                  onChange={(e) => updateFilters({ 
                    market_value_max: e.target.value ? parseInt(e.target.value) : undefined 
                  })}
                />
              </div>
            </div>
          </div>
        )}

        <Separator />

        {/* Disponibilidade para Viagem/Mudança */}
        <div className="space-y-3">
          <Label>Disponibilidade</Label>
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="available_for_travel"
                checked={localFilters.available_for_travel === true}
                onCheckedChange={(checked) => updateFilters({ 
                  available_for_travel: checked ? true : undefined 
                })}
              />
              <Label htmlFor="available_for_travel" className="text-sm">
                Disponível para viagens
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="available_for_relocation"
                checked={localFilters.available_for_relocation === true}
                onCheckedChange={(checked) => updateFilters({ 
                  available_for_relocation: checked ? true : undefined 
                })}
              />
              <Label htmlFor="available_for_relocation" className="text-sm">
                Disponível para mudança
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="has_eu_passport"
                checked={localFilters.has_eu_passport === true}
                onCheckedChange={(checked) => updateFilters({ 
                  has_eu_passport: checked ? true : undefined 
                })}
              />
              <Label htmlFor="has_eu_passport" className="text-sm">
                Possui passaporte europeu
              </Label>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
