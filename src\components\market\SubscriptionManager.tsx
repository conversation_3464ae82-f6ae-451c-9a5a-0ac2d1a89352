import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { toast } from '@/hooks/use-toast';
import { 
  CreditCard, 
  Calendar, 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  DollarSign,
  Shield,
  Star
} from 'lucide-react';
import { 
  getSubscriptionInfo, 
  processSubscriptionPayment, 
  getPaymentHistory, 
  generatePaymentLink,
  checkSubscriptionExpiry,
  type SubscriptionInfo,
  type SubscriptionPayment 
} from '@/api/marketSubscriptions';

// =====================================================
// INTERFACE
// =====================================================

interface SubscriptionManagerProps {
  profileId: string;
  onSubscriptionUpdate?: (info: SubscriptionInfo) => void;
}

// =====================================================
// COMPONENTE PRINCIPAL
// =====================================================

export function SubscriptionManager({ profileId, onSubscriptionUpdate }: SubscriptionManagerProps) {
  const [subscriptionInfo, setSubscriptionInfo] = useState<SubscriptionInfo | null>(null);
  const [paymentHistory, setPaymentHistory] = useState<SubscriptionPayment[]>([]);
  const [loading, setLoading] = useState(true);
  const [processingPayment, setProcessingPayment] = useState(false);
  const [expiryCheck, setExpiryCheck] = useState<any>(null);

  useEffect(() => {
    loadSubscriptionData();
  }, [profileId]);

  const loadSubscriptionData = async () => {
    try {
      setLoading(true);
      
      const [info, history, expiry] = await Promise.all([
        getSubscriptionInfo(profileId),
        getPaymentHistory(profileId),
        checkSubscriptionExpiry(profileId)
      ]);
      
      setSubscriptionInfo(info);
      setPaymentHistory(history);
      setExpiryCheck(expiry);
      
      if (info && onSubscriptionUpdate) {
        onSubscriptionUpdate(info);
      }
    } catch (error: any) {
      toast({
        title: "Erro",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  // =====================================================
  // FUNÇÕES AUXILIARES
  // =====================================================

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'trial':
        return 'bg-blue-100 text-blue-800';
      case 'expired':
        return 'bg-red-100 text-red-800';
      case 'suspended':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'active':
        return 'Ativo';
      case 'trial':
        return 'Período de Teste';
      case 'expired':
        return 'Expirado';
      case 'suspended':
        return 'Suspenso';
      default:
        return status;
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  // =====================================================
  // HANDLERS
  // =====================================================

  const handlePayment = async () => {
    try {
      setProcessingPayment(true);
      
      // Gerar link de pagamento
      const paymentLink = await generatePaymentLink(profileId, 120.00);
      
      // Em um sistema real, redirecionaria para o gateway de pagamento
      // Por enquanto, vamos simular o pagamento
      const confirmed = window.confirm(
        `Confirma o pagamento de R$ 120,00 para renovar a assinatura?\n\nReferência: ${paymentLink.referenceCode}`
      );
      
      if (confirmed) {
        // Simular processamento
        await processSubscriptionPayment(profileId, {
          amount: 120.00,
          payment_method: 'credit_card',
          reference_code: paymentLink.referenceCode
        });
        
        toast({
          title: "Pagamento processado",
          description: "Sua assinatura foi renovada com sucesso!"
        });
        
        // Recarregar dados
        await loadSubscriptionData();
      }
    } catch (error: any) {
      toast({
        title: "Erro no pagamento",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setProcessingPayment(false);
    }
  };

  // =====================================================
  // RENDER
  // =====================================================

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!subscriptionInfo) {
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-center text-gray-500">
            Não foi possível carregar informações da assinatura
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Status da Assinatura */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            Status da Assinatura
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-gray-600">Status atual:</span>
            <Badge className={getStatusColor(subscriptionInfo.subscription_status)}>
              {getStatusLabel(subscriptionInfo.subscription_status)}
            </Badge>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-gray-600">Início:</span>
            <span className="font-medium">{formatDate(subscriptionInfo.subscription_start_date)}</span>
          </div>
          
          {subscriptionInfo.subscription_end_date && (
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Vencimento:</span>
              <span className="font-medium">{formatDate(subscriptionInfo.subscription_end_date)}</span>
            </div>
          )}
          
          {subscriptionInfo.last_payment_date && (
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Último pagamento:</span>
              <span className="font-medium">{formatDate(subscriptionInfo.last_payment_date)}</span>
            </div>
          )}

          {/* Barra de progresso para trial */}
          {subscriptionInfo.is_trial_period && subscriptionInfo.days_until_expiry !== undefined && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Período de teste:</span>
                <span className="font-medium">{subscriptionInfo.days_until_expiry} dias restantes</span>
              </div>
              <Progress 
                value={Math.max(0, Math.min(100, (365 - subscriptionInfo.days_until_expiry) / 365 * 100))} 
                className="h-2"
              />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Alertas */}
      {expiryCheck && (
        <>
          {expiryCheck.isExpired && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Sua assinatura expirou. Renove agora para manter seu perfil ativo no mercado.
              </AlertDescription>
            </Alert>
          )}
          
          {!expiryCheck.isExpired && expiryCheck.daysUntilExpiry <= 30 && (
            <Alert>
              <Clock className="h-4 w-4" />
              <AlertDescription>
                Sua assinatura vence em {expiryCheck.daysUntilExpiry} dias. 
                Renove antecipadamente para evitar interrupções.
              </AlertDescription>
            </Alert>
          )}
        </>
      )}

      {/* Informações do Plano */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Star className="w-5 h-5" />
            Plano Premium
          </CardTitle>
          <CardDescription>
            Acesso completo à plataforma de análise de mercado
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center gap-3">
              <CheckCircle className="w-5 h-5 text-green-500" />
              <span className="text-sm">Perfil sempre visível</span>
            </div>
            <div className="flex items-center gap-3">
              <CheckCircle className="w-5 h-5 text-green-500" />
              <span className="text-sm">Upload de vídeos e documentos</span>
            </div>
            <div className="flex items-center gap-3">
              <CheckCircle className="w-5 h-5 text-green-500" />
              <span className="text-sm">Estatísticas de visualização</span>
            </div>
            <div className="flex items-center gap-3">
              <CheckCircle className="w-5 h-5 text-green-500" />
              <span className="text-sm">Suporte prioritário</span>
            </div>
          </div>
          
          <div className="border-t pt-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-semibold">Valor anual:</p>
                <p className="text-sm text-gray-600">Renovação automática</p>
              </div>
              <div className="text-right">
                <p className="text-2xl font-bold text-green-600">R$ 120,00</p>
                <p className="text-sm text-gray-600">por ano</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Ações */}
      {(subscriptionInfo.subscription_status === 'trial' || 
        subscriptionInfo.subscription_status === 'expired' ||
        (expiryCheck && expiryCheck.daysUntilExpiry <= 30)) && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="w-5 h-5" />
              Renovar Assinatura
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {subscriptionInfo.is_trial_period && (
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Período de teste 2024:</strong> Você está no período gratuito. 
                    A partir de 2025, será necessário pagar a taxa anual para manter seu perfil ativo.
                  </AlertDescription>
                </Alert>
              )}
              
              <Button 
                onClick={handlePayment}
                disabled={processingPayment}
                className="w-full flex items-center gap-2"
                size="lg"
              >
                <DollarSign className="w-4 h-4" />
                {processingPayment ? 'Processando...' : 'Renovar por R$ 120,00'}
              </Button>
              
              <p className="text-xs text-gray-500 text-center">
                Pagamento seguro • Renovação por 12 meses • Cancele quando quiser
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Histórico de Pagamentos */}
      {paymentHistory.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="w-5 h-5" />
              Histórico de Pagamentos
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {paymentHistory.map((payment) => (
                <div key={payment.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="font-medium">{formatCurrency(payment.amount)}</p>
                    <p className="text-sm text-gray-600">
                      {payment.payment_date && formatDate(payment.payment_date)}
                    </p>
                    {payment.reference_code && (
                      <p className="text-xs text-gray-500">Ref: {payment.reference_code}</p>
                    )}
                  </div>
                  <Badge 
                    variant={payment.payment_status === 'completed' ? 'default' : 'secondary'}
                  >
                    {payment.payment_status === 'completed' ? 'Pago' : 
                     payment.payment_status === 'pending' ? 'Pendente' : 
                     payment.payment_status === 'failed' ? 'Falhou' : 'Reembolsado'}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
