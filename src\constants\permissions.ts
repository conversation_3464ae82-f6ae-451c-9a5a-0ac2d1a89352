/**
 * Constantes de permissões do sistema
 *
 * Este arquivo define todas as permissões disponíveis no sistema,
 * organizadas por grupos para facilitar a manutenção e consistência.
 */

// Permissões relacionadas a jogadores
export const PLAYER_PERMISSIONS = {
  VIEW: "players.view",
  VIEW_OWN: "players.view_own", // Permissão para jogador ver seu próprio perfil
  EDIT_OWN: "players.edit_own", // Permissão para jogador editar seu próprio perfil
  CREATE: "players.create",
  EDIT: "players.edit",
  DELETE: "players.delete",
  RESET_PASSWORD: "players.reset_password",
  DOCUMENTS: {
    VIEW: "players.documents.view",
    VERIFY: "players.documents.verify",
  },
  FINANCES: {
    VIEW: "players.finances.view",
    EDIT: "players.finances.edit",
    MONTHLY_FEES: "players.finances.monthly_fees", // Permissão específica para mensalidades
  },
  OCCURRENCES: {
    VIEW: "players.occurrences.view",
    CREATE: "players.occurrences.create",
    EDIT: "players.occurrences.edit",
    DELETE: "players.occurrences.delete",
    SIGN: "players.occurrences.sign",
  },
  PROFILE_TABS: {
    INFO: "players.profile.info",
    MEDICAL: "players.profile.medical",
    DOCUMENTS: "players.profile.documents",
    FINANCES: "players.profile.finances",
    OCCURRENCES: "players.profile.occurrences",
    EVALUATION: "players.profile.evaluation",
  },
  EVALUATION: {
    VIEW: "players.evaluation.view",
    EDIT: "players.evaluation.edit",
  },
  STATUS: {
    UPDATE: "players.status.update",
  },
  TABS: {
    PLAYERS: "players.tabs.players",
    INACTIVE: "players.tabs.inactive",
    CARDS: "players.tabs.cards",
    TACTICS: "players.tabs.tactics",
    MEMBERS: "players.tabs.members",
  },
};

// Permissões relacionadas a partidas
export const MATCH_PERMISSIONS = {
  VIEW: "matches.view",
  CREATE: "matches.create",
  EDIT: "matches.edit",
  DELETE: "matches.delete",
  LINEUP: "matches.lineup",
  EVENTS: "matches.events",
};

// Permissões relacionadas a adversários
export const OPPONENT_PERMISSIONS = {
  VIEW: "opponents.view",
  CREATE: "opponents.create",
  EDIT: "opponents.edit",
  DELETE: "opponents.delete",
};

// Permissões relacionadas a mapeamento de jogadores
export const MAPPING_PERMISSIONS = {
  VIEW: "mapping.view",
  CREATE: "mapping.create",
  EDIT: "mapping.edit",
  DELETE: "mapping.delete",
};

// Permissões relacionadas a treinamentos
export const TRAINING_PERMISSIONS = {
  VIEW: "trainings.view",
  CREATE: "trainings.create",
  EDIT: "trainings.edit",
  DELETE: "trainings.delete",
  LOCATIONS: "trainings.locations",
};

// Permissões relacionadas a departamentos
export const DEPARTMENT_PERMISSIONS = {
  VIEW: "departments.view",
  CREATE: "departments.create",
  EDIT: "departments.edit",
  DELETE: "departments.delete",
};

// Permissões relacionadas a usuários
export const USER_PERMISSIONS = {
  VIEW: "users.view",
  CREATE: "users.create",
  EDIT: "users.edit",
  DELETE: "users.delete",
  REMOVE: "users.remove",
  RESET_PASSWORD: "users.reset_password",
  PERMISSIONS: "users.permissions",
  INVITATIONS: {
    CREATE: "users.invitations.create",
    EDIT: "users.invitations.edit",
    DELETE: "users.invitations.delete",
  },
  TABS: {
    INVITATIONS: "users.tabs.invitations",
    DOCUMENTS: "users.tabs.documents",
  },
};

// Permissões relacionadas a configurações
export const SETTINGS_PERMISSIONS = {
  VIEW: "settings.view",
  EDIT: "settings.edit",
};

// Permissões relacionadas a finanças
export const FINANCE_PERMISSIONS = {
  VIEW: "finances.view",
  CREATE: "finances.create",
  EDIT: "finances.edit",
  DELETE: "finances.delete",
};

// Permissões relacionadas a área médica
export const MEDICAL_PERMISSIONS = {
  VIEW: "medical.view",
  CREATE: "medical.create",
  EDIT: "medical.edit",
  DELETE: "medical.delete",
  RECORDS: {
    VIEW: "medical.records.view",
    CREATE: "medical.records.create",
    EDIT: "medical.records.edit",
    DELETE: "medical.records.delete",
  },
  REHABILITATION: {
    VIEW: "medical.rehabilitation.view",
    CREATE: "medical.rehabilitation.create",
    EDIT: "medical.rehabilitation.edit",
    DELETE: "medical.rehabilitation.delete",
  },
  AGENDA: {
    VIEW: "medical.agenda.view",
  },
  APPOINTMENTS: {
    CREATE: "medical.appointments.create",
    EDIT: "medical.appointments.edit",
    START: "medical.appointments.start",
    DELETE: "medical.appointments.delete",
  },
  EXAM_REQUESTS: {
    VIEW: "medical.exam_requests.view",
    CREATE: "medical.exam_requests.create",
  },
};

// Permissões relacionadas a profissionais médicos
export const MEDICAL_PROFESSIONAL_PERMISSIONS = {
  VIEW: "medical_professionals.view",
  CREATE: "medical_professionals.create",
  EDIT: "medical_professionals.edit",
  DELETE: "medical_professionals.delete",
  EDIT_OWN: "medical_professionals.edit_own",
};

// Permissões relacionadas à disponibilidade médica
export const MEDICAL_AVAILABILITY_PERMISSIONS = {
  VIEW: "medical.availability.view",
  CREATE: "medical.availability.create",
  EDIT: "medical.availability.edit",
  DELETE: "medical.availability.delete",
};

// Permissões relacionadas à prevenção de lesões com IA
export const INJURY_PREVENTION_PERMISSIONS = {
  VIEW: "medical.injury_prevention.view",
  CREATE: "medical.injury_prevention.create",
  EDIT: "medical.injury_prevention.edit",
  DELETE: "medical.injury_prevention.delete",
  ANALYTICS: "medical.injury_prevention.analytics",
  WELLNESS: {
    VIEW: "medical.injury_prevention.wellness.view",
    CREATE: "medical.injury_prevention.wellness.create",
    EDIT: "medical.injury_prevention.wellness.edit",
  },
  WORKLOAD: {
    VIEW: "medical.injury_prevention.workload.view",
    CREATE: "medical.injury_prevention.workload.create",
    EDIT: "medical.injury_prevention.workload.edit",
  },
  ALERTS: {
    VIEW: "medical.injury_prevention.alerts.view",
    ACKNOWLEDGE: "medical.injury_prevention.alerts.acknowledge",
  }
};

// Permissões relacionadas a agenda
export const AGENDA_PERMISSIONS = {
  VIEW: "agenda.view",
  CREATE: "agenda.create",
  EDIT: "agenda.edit",
  DELETE: "agenda.delete",
};

// Permissões relacionadas a categorias
export const CATEGORY_PERMISSIONS = {
  VIEW: "categories.view",
  CREATE: "categories.create",
  EDIT: "categories.edit",
  DELETE: "categories.delete",
};

// Permissões relacionadas a alojamentos
export const ACCOMMODATION_PERMISSIONS = {
  VIEW: "accommodations.view",
  CREATE: "accommodations.create",
  EDIT: "accommodations.edit",
  DELETE: "accommodations.delete",
};

// Permissões relacionadas ao módulo administrativo
export const ADMINISTRATIVE_PERMISSIONS = {
  VIEW: "administrative.view",
  TABS: {
    DOCUMENTS: "administrative.tabs.documents",
    TASKS: "administrative.tabs.tasks",
    REMINDERS: "administrative.tabs.reminders",
    COLLABORATORS: "administrative.tabs.collaborators",
    SUPPLIERS: "administrative.tabs.suppliers",
  },
  DOCUMENTS: {
    VIEW: "administrative.documents.view",
    CREATE: "administrative.documents.create",
    EDIT: "administrative.documents.edit",
    DELETE: "administrative.documents.delete",
    SIGN: "administrative.documents.sign",
  },
  TASKS: {
    VIEW: "administrative.tasks.view",
    VIEW_OWN: "administrative.tasks.view_own",
    CREATE: "administrative.tasks.create",
    EDIT: "administrative.tasks.edit",
    DELETE: "administrative.tasks.delete",
  },
  REMINDERS: {
    VIEW: "administrative.reminders.view",
    CREATE: "administrative.reminders.create",
    EDIT: "administrative.reminders.edit",
    DELETE: "administrative.reminders.delete",
  },
};

// Permissões relacionadas a colaboradores
export const COLLABORATOR_PERMISSIONS = {
  VIEW: "collaborators.view",
  VIEW_OWN: "collaborators.view_own",
  CREATE: "collaborators.create",
  EDIT: "collaborators.edit",
  DELETE: "collaborators.delete",
  STATUS: {
    UPDATE: "collaborators.status.update",
  },
  FINANCE: {
    VIEW: "collaborators.finance.view",
  },
};

// Permissões relacionadas a fornecedores
export const SUPPLIER_PERMISSIONS = {
  VIEW: "suppliers.view",
  CREATE: "suppliers.create",
  EDIT: "suppliers.edit",
  DELETE: "suppliers.delete",
};

// Permissões relacionadas a convocações
export const CALLUP_PERMISSIONS = {
  VIEW: "callups.view",
  CREATE: "callups.create",
  EDIT: "callups.edit",
  DELETE: "callups.delete",
};

// Permissões para equipe de Operação de Jogo
export const GAME_OPERATION_PERMISSIONS = {
  VIEW: "game_operation.view",
  CREATE: "game_operation.create",
  EDIT: "game_operation.edit",
  DELETE: "game_operation.delete",
};

// Permissões relacionadas a competições
export const COMPETITION_PERMISSIONS = {
  VIEW: "competitions.view",
  CREATE: "competitions.create",
  EDIT: "competitions.edit",
  DELETE: "competitions.delete",
};

// Permissões relacionadas ao pré cadastro de atletas
export const EVALUATION_PERMISSIONS = {
  TABS: {
    PLAYERS: "evaluation.tabs.players",
    INVITATIONS: "evaluation.tabs.invitations",
    NEW_INVITATION: "evaluation.tabs.new",
    DASHBOARD: "evaluation.tabs.dashboard",
  },
  INVITATIONS: {
    CREATE: "evaluation.invitations.create",
    COPY_LINK: "evaluation.invitations.copy",
    RESEND: "evaluation.invitations.resend",
    DELETE: "evaluation.invitations.delete",
  },
  PLAYERS: {
    EDIT: "evaluation.players.edit",
    DELETE: "evaluation.players.delete",
    SCHEDULE: "evaluation.players.schedule",
    UPDATE_STATUS: "evaluation.players.update_status",
    VERIFY_DOCUMENTS: "evaluation.players.verify_documents",
    MANAGE_PAYMENTS: "evaluation.players.manage_payments",
  },
};

// Permissões relacionadas a relatórios
export const REPORT_PERMISSIONS = {
  VIEW: "reports.view",
  GENERATE: "reports.generate",
};

// Permissões relacionadas a estatísticas
export const STATISTICS_PERMISSIONS = {
  VIEW: "statistics.view",
};

// Permissões relacionadas a analytics
export const ANALYTICS_PERMISSIONS = {
  VIEW: "analytics.view",
};

// Permissões relacionadas a comunicação
export const COMMUNICATION_PERMISSIONS = {
  VIEW: "communication.view",
  SEND: "communication.send",
};

// Permissões relacionadas a logs de auditoria
export const AUDIT_PERMISSIONS = {
  VIEW: "audit_logs.view",
  EXPORT: "audit_logs.export",
};

// Permissões relacionadas a presidente (especiais)
export const PRESIDENT_PERMISSIONS = {
  CLUB_OWNERSHIP: "president.club_ownership",
  FINANCIAL_APPROVAL: "president.financial_approval",
};

// Permissões relacionadas ao dashboard
export const DASHBOARD_PERMISSIONS = {
  VIEW: "dashboard.view",
};

// Permissões relacionadas ao estoque
export const INVENTORY_PERMISSIONS = {
  VIEW: "inventory.view",
  CREATE: "inventory.create",
  EDIT: "inventory.edit",
  DELETE: "inventory.delete",
  REPORTS: "inventory.reports",
  DEPARTMENTS: {
    MATERIAL_ESPORTIVO: "inventory.departments.material_esportivo"
  },
  TABS: {
    CADASTRO: "inventory.tabs.cadastro",
    SOLICITACOES: "inventory.tabs.solicitacoes",
    RELATORIOS: "inventory.tabs.relatorios",
    BAIXO_ESTOQUE: "inventory.tabs.baixo_estoque",
    LISTA_COMPRAS: "inventory.tabs.lista_compras",
  },
  REQUESTS: {
    VIEW: "inventory.requests.view",
    CREATE: "inventory.requests.create",
    EDIT: "inventory.requests.edit",
    DELETE: "inventory.requests.delete",
    APPROVE: "inventory.requests.approve",
    PROCESS: "inventory.requests.process"
  }
};

// Todos os grupos de permissões organizados para UI
export const PERMISSION_GROUPS = {
  dashboard: {
    label: "Dashboard",
    permissions: {
      [DASHBOARD_PERMISSIONS.VIEW]: "Visualizar dashboard",
    },
  },
  playerProfile: {
    label: "Perfil do Jogador",
    permissions: {
      [PLAYER_PERMISSIONS.VIEW_OWN]: "Visualizar próprio perfil",
      [PLAYER_PERMISSIONS.EDIT_OWN]: "Editar próprio perfil",
      [PLAYER_PERMISSIONS.EVALUATION.VIEW]: "Visualizar próprio pré cadastro",
    },
  },
  players: {
    label: "Jogadores",
    permissions: {
      [PLAYER_PERMISSIONS.VIEW]: "Visualizar jogadores",
      [PLAYER_PERMISSIONS.CREATE]: "Criar jogadores",
      [PLAYER_PERMISSIONS.EDIT]: "Editar jogadores",
      [PLAYER_PERMISSIONS.DELETE]: "Excluir jogadores",
      [PLAYER_PERMISSIONS.RESET_PASSWORD]: "Alterar senha do jogador",
      [PLAYER_PERMISSIONS.DOCUMENTS.VIEW]: "Visualizar documentos",
      [PLAYER_PERMISSIONS.DOCUMENTS.VERIFY]: "Verificar documentos",
      [PLAYER_PERMISSIONS.FINANCES.VIEW]: "Visualizar finanças",
      [PLAYER_PERMISSIONS.FINANCES.EDIT]: "Editar finanças",
      [PLAYER_PERMISSIONS.FINANCES.MONTHLY_FEES]: "Visualizar mensalidades",
      [PLAYER_PERMISSIONS.OCCURRENCES.VIEW]: "Visualizar ocorrências",
      [PLAYER_PERMISSIONS.OCCURRENCES.CREATE]: "Criar ocorrências",
      [PLAYER_PERMISSIONS.OCCURRENCES.EDIT]: "Editar ocorrências",
      [PLAYER_PERMISSIONS.OCCURRENCES.DELETE]: "Excluir ocorrências",
      [PLAYER_PERMISSIONS.EVALUATION.VIEW]: "Visualizar avaliações",
      [PLAYER_PERMISSIONS.EVALUATION.EDIT]: "Editar avaliações",
      [PLAYER_PERMISSIONS.STATUS.UPDATE]: "Alterar status dos atletas",
      [PLAYER_PERMISSIONS.TABS.PLAYERS]: "Acessar aba Atletas",
      [PLAYER_PERMISSIONS.TABS.INACTIVE]: "Acessar aba Inativos",
      [PLAYER_PERMISSIONS.TABS.CARDS]: "Acessar aba Cards",
      [PLAYER_PERMISSIONS.TABS.TACTICS]: "Acessar aba Tática",
      [PLAYER_PERMISSIONS.TABS.MEMBERS]: "Acessar aba Membros do Clube",
      [PLAYER_PERMISSIONS.PROFILE_TABS.INFO]: "Acessar aba Informações",
      [PLAYER_PERMISSIONS.PROFILE_TABS.MEDICAL]: "Acessar aba Histórico Médico",
      [PLAYER_PERMISSIONS.PROFILE_TABS.DOCUMENTS]: "Acessar aba Documentos",
      [PLAYER_PERMISSIONS.PROFILE_TABS.FINANCES]: "Acessar aba Finanças",
      [PLAYER_PERMISSIONS.PROFILE_TABS.OCCURRENCES]: "Acessar aba Ocorrências",
      [PLAYER_PERMISSIONS.PROFILE_TABS.EVALUATION]: "Acessar aba Avaliação do Atleta",
    },
  },
  evaluation: {
    label: "Pré Cadastro",
    permissions: {
      [EVALUATION_PERMISSIONS.TABS.PLAYERS]: "Acessar aba Atletas em Pré Cadastro",
      [EVALUATION_PERMISSIONS.TABS.INVITATIONS]: "Acessar aba Convites",
      [EVALUATION_PERMISSIONS.TABS.NEW_INVITATION]: "Acessar aba Novo Convite",
      [EVALUATION_PERMISSIONS.TABS.DASHBOARD]: "Acessar aba Dashboard",
      [EVALUATION_PERMISSIONS.INVITATIONS.CREATE]: "Enviar convites",
      [EVALUATION_PERMISSIONS.INVITATIONS.COPY_LINK]: "Copiar link de convite",
      [EVALUATION_PERMISSIONS.INVITATIONS.RESEND]: "Reenviar convite",
      [EVALUATION_PERMISSIONS.INVITATIONS.DELETE]: "Excluir convite",
      [EVALUATION_PERMISSIONS.PLAYERS.EDIT]: "Editar atletas em pré cadastro",
      [EVALUATION_PERMISSIONS.PLAYERS.DELETE]: "Excluir atletas em pré cadastro",
      [EVALUATION_PERMISSIONS.PLAYERS.SCHEDULE]: "Agendar pré cadastro",
      [EVALUATION_PERMISSIONS.PLAYERS.UPDATE_STATUS]: "Atualizar status do pré cadastro",
      [EVALUATION_PERMISSIONS.PLAYERS.VERIFY_DOCUMENTS]: "Verificar documentos",
    },
  },
  matches: {
    label: "Partidas",
    permissions: {
      [MATCH_PERMISSIONS.VIEW]: "Visualizar partidas",
      [MATCH_PERMISSIONS.CREATE]: "Criar partidas",
      [MATCH_PERMISSIONS.EDIT]: "Editar partidas",
      [MATCH_PERMISSIONS.DELETE]: "Excluir partidas",
      [MATCH_PERMISSIONS.LINEUP]: "Gerenciar escalação",
      [MATCH_PERMISSIONS.EVENTS]: "Registrar eventos",
    },
  },
  competitions: {
    label: "Competições",
    permissions: {
      [COMPETITION_PERMISSIONS.VIEW]: "Visualizar competições",
      [COMPETITION_PERMISSIONS.CREATE]: "Criar competições",
      [COMPETITION_PERMISSIONS.EDIT]: "Editar competições",
      [COMPETITION_PERMISSIONS.DELETE]: "Excluir competições",
    },
  },
  opponents: {
    label: "Adversários",
    permissions: {
      [OPPONENT_PERMISSIONS.VIEW]: "Visualizar adversários",
      [OPPONENT_PERMISSIONS.CREATE]: "Criar adversários",
      [OPPONENT_PERMISSIONS.EDIT]: "Editar adversários",
      [OPPONENT_PERMISSIONS.DELETE]: "Excluir adversários",
    },
  },
  mapping: {
    label: "Mapeamento de Jogadores",
    permissions: {
      [MAPPING_PERMISSIONS.VIEW]: "Visualizar mapeamentos",
      [MAPPING_PERMISSIONS.CREATE]: "Criar mapeamentos",
      [MAPPING_PERMISSIONS.EDIT]: "Editar mapeamentos",
      [MAPPING_PERMISSIONS.DELETE]: "Excluir mapeamentos",
    },
  },
  trainings: {
    label: "Treinamentos",
    permissions: {
      [TRAINING_PERMISSIONS.VIEW]: "Visualizar treinamentos",
      [TRAINING_PERMISSIONS.CREATE]: "Criar treinamentos",
      [TRAINING_PERMISSIONS.EDIT]: "Editar treinamentos",
      [TRAINING_PERMISSIONS.DELETE]: "Excluir treinamentos",
      [TRAINING_PERMISSIONS.LOCATIONS]: "Gerenciar locais de treino",
    },
  },
  departments: {
    label: "Departamentos",
    permissions: {
      [DEPARTMENT_PERMISSIONS.VIEW]: "Visualizar departamentos",
      [DEPARTMENT_PERMISSIONS.CREATE]: "Criar departamentos",
      [DEPARTMENT_PERMISSIONS.EDIT]: "Editar departamentos",
      [DEPARTMENT_PERMISSIONS.DELETE]: "Excluir departamentos",
    },
  },
  users: {
    label: "Usuários",
    permissions: {
      [USER_PERMISSIONS.VIEW]: "Visualizar usuários",
      [USER_PERMISSIONS.CREATE]: "Criar usuários",
      [USER_PERMISSIONS.EDIT]: "Editar usuários",
      [USER_PERMISSIONS.DELETE]: "Excluir usuários",
      [USER_PERMISSIONS.REMOVE]: "Remover usuário do clube",
      [USER_PERMISSIONS.RESET_PASSWORD]: "Alterar senha do usuário",
      [USER_PERMISSIONS.PERMISSIONS]: "Gerenciar permissões",
      [USER_PERMISSIONS.INVITATIONS.CREATE]: "Criar convites",
      [USER_PERMISSIONS.INVITATIONS.EDIT]: "Editar convites",
      [USER_PERMISSIONS.INVITATIONS.DELETE]: "Cancelar convites",
      [USER_PERMISSIONS.TABS.INVITATIONS]: "Acessar aba Convites",
      [USER_PERMISSIONS.TABS.DOCUMENTS]: "Acessar aba Documentos Pendentes",
    },
  },
  finances: {
    label: "Finanças",
    permissions: {
      [FINANCE_PERMISSIONS.VIEW]: "Visualizar finanças",
      [FINANCE_PERMISSIONS.CREATE]: "Criar transações",
      [FINANCE_PERMISSIONS.EDIT]: "Editar transações",
      [FINANCE_PERMISSIONS.DELETE]: "Excluir transações",
    },
  },
  medical: {
    label: "Departamento Médico",
    permissions: {
      [MEDICAL_PERMISSIONS.VIEW]: "Visualizar registros médicos",
      [MEDICAL_PERMISSIONS.CREATE]: "Criar registros médicos",
      [MEDICAL_PERMISSIONS.EDIT]: "Editar registros médicos",
      [MEDICAL_PERMISSIONS.DELETE]: "Excluir registros médicos",
      [MEDICAL_PERMISSIONS.RECORDS.VIEW]: "Visualizar prontuários",
      [MEDICAL_PERMISSIONS.RECORDS.CREATE]: "Criar prontuários",
      [MEDICAL_PERMISSIONS.RECORDS.EDIT]: "Editar prontuários",
      [MEDICAL_PERMISSIONS.RECORDS.DELETE]: "Excluir prontuários",
      [MEDICAL_PERMISSIONS.REHABILITATION.VIEW]: "Visualizar reabilitação",
      [MEDICAL_PERMISSIONS.REHABILITATION.CREATE]: "Criar sessões de reabilitação",
      [MEDICAL_PERMISSIONS.REHABILITATION.EDIT]: "Editar sessões de reabilitação",
      [MEDICAL_PERMISSIONS.REHABILITATION.DELETE]: "Excluir sessões de reabilitação",
      [MEDICAL_PERMISSIONS.AGENDA.VIEW]: "Visualizar aba de agenda médica",
      [MEDICAL_PERMISSIONS.APPOINTMENTS.CREATE]: "Criar agendamentos médicos",
      [MEDICAL_PERMISSIONS.APPOINTMENTS.EDIT]: "Editar agendamentos médicos",
      [MEDICAL_PERMISSIONS.APPOINTMENTS.START]: "Iniciar agendamentos médicos",
      [MEDICAL_PERMISSIONS.APPOINTMENTS.DELETE]: "Excluir agendamentos médicos",
      [MEDICAL_PERMISSIONS.EXAM_REQUESTS.VIEW]: "Visualizar aba de solicitações de exame",
      [MEDICAL_PERMISSIONS.EXAM_REQUESTS.CREATE]: "Solicitar exames médicos",
    },
  },
  medical_professionals: {
    label: "Profissionais Médicos",
    permissions: {
      [MEDICAL_PROFESSIONAL_PERMISSIONS.VIEW]: "Visualizar profissionais médicos",
      [MEDICAL_PROFESSIONAL_PERMISSIONS.CREATE]: "Cadastrar profissionais médicos",
      [MEDICAL_PROFESSIONAL_PERMISSIONS.EDIT]: "Editar profissionais médicos",
      [MEDICAL_PROFESSIONAL_PERMISSIONS.DELETE]: "Excluir profissionais médicos",
      [MEDICAL_PROFESSIONAL_PERMISSIONS.EDIT_OWN]: "Editar próprio perfil médico",
    },
  },
  medicalAvailability: {
    label: "Agenda Médica",
    permissions: {
      [MEDICAL_AVAILABILITY_PERMISSIONS.VIEW]: "Visualizar disponibilidade",
      [MEDICAL_AVAILABILITY_PERMISSIONS.CREATE]: "Criar disponibilidade",
      [MEDICAL_AVAILABILITY_PERMISSIONS.EDIT]: "Editar disponibilidade",
      [MEDICAL_AVAILABILITY_PERMISSIONS.DELETE]: "Excluir disponibilidade",
    },
  },
  injuryPrevention: {
    label: "IA - Prevenção de Lesões",
    permissions: {
      [INJURY_PREVENTION_PERMISSIONS.VIEW]: "Visualizar dashboard de prevenção",
      [INJURY_PREVENTION_PERMISSIONS.CREATE]: "Registrar dados de prevenção",
      [INJURY_PREVENTION_PERMISSIONS.EDIT]: "Editar dados de prevenção",
      [INJURY_PREVENTION_PERMISSIONS.DELETE]: "Excluir dados de prevenção",
      [INJURY_PREVENTION_PERMISSIONS.ANALYTICS]: "Visualizar análises avançadas",
      [INJURY_PREVENTION_PERMISSIONS.WELLNESS.VIEW]: "Visualizar dados de wellness",
      [INJURY_PREVENTION_PERMISSIONS.WELLNESS.CREATE]: "Registrar dados de wellness",
      [INJURY_PREVENTION_PERMISSIONS.WELLNESS.EDIT]: "Editar dados de wellness",
      [INJURY_PREVENTION_PERMISSIONS.WORKLOAD.VIEW]: "Visualizar dados de carga",
      [INJURY_PREVENTION_PERMISSIONS.WORKLOAD.CREATE]: "Registrar dados de carga",
      [INJURY_PREVENTION_PERMISSIONS.WORKLOAD.EDIT]: "Editar dados de carga",
      [INJURY_PREVENTION_PERMISSIONS.ALERTS.VIEW]: "Visualizar alertas de lesão",
      [INJURY_PREVENTION_PERMISSIONS.ALERTS.ACKNOWLEDGE]: "Reconhecer alertas",
    },
  },
  agenda: {
    label: "Agenda",
    permissions: {
      [AGENDA_PERMISSIONS.VIEW]: "Visualizar agenda",
      [AGENDA_PERMISSIONS.CREATE]: "Criar eventos",
      [AGENDA_PERMISSIONS.EDIT]: "Editar eventos",
      [AGENDA_PERMISSIONS.DELETE]: "Excluir eventos",
    },
  },
  categories: {
    label: "Categorias",
    permissions: {
      [CATEGORY_PERMISSIONS.VIEW]: "Visualizar categorias",
      [CATEGORY_PERMISSIONS.CREATE]: "Criar categorias",
      [CATEGORY_PERMISSIONS.EDIT]: "Editar categorias",
      [CATEGORY_PERMISSIONS.DELETE]: "Excluir categorias",
    },
  },
  accommodations: {
    label: "Alojamentos",
    permissions: {
      [ACCOMMODATION_PERMISSIONS.VIEW]: "Visualizar alojamentos",
      [ACCOMMODATION_PERMISSIONS.CREATE]: "Criar alojamentos",
      [ACCOMMODATION_PERMISSIONS.EDIT]: "Editar alojamentos",
      [ACCOMMODATION_PERMISSIONS.DELETE]: "Excluir alojamentos",
    },
  },
  callups: {
    label: "Convocações",
    permissions: {
      [CALLUP_PERMISSIONS.VIEW]: "Visualizar convocações",
      [CALLUP_PERMISSIONS.CREATE]: "Criar convocações",
      [CALLUP_PERMISSIONS.EDIT]: "Editar convocações",
      [CALLUP_PERMISSIONS.DELETE]: "Excluir convocações",
    },
  },
  gameOperation: {
    label: "Operação de Jogo",
    permissions: {
      [GAME_OPERATION_PERMISSIONS.VIEW]: "Visualizar membros",
      [GAME_OPERATION_PERMISSIONS.CREATE]: "Cadastrar membros",
      [GAME_OPERATION_PERMISSIONS.EDIT]: "Editar membros",
      [GAME_OPERATION_PERMISSIONS.DELETE]: "Excluir membros",
    },
  },
  administrative: {
    label: "Administrativo",
    permissions: {
      [ADMINISTRATIVE_PERMISSIONS.VIEW]: "Visualizar módulo",
      [ADMINISTRATIVE_PERMISSIONS.TABS.DOCUMENTS]: "Acessar aba Ofícios e Memorandos",
      [ADMINISTRATIVE_PERMISSIONS.TABS.TASKS]: "Acessar aba Tarefas Diárias",
      [ADMINISTRATIVE_PERMISSIONS.TABS.REMINDERS]: "Acessar aba Agenda e Lembretes",
      [ADMINISTRATIVE_PERMISSIONS.TABS.COLLABORATORS]: "Acessar aba Colaboradores",
      [ADMINISTRATIVE_PERMISSIONS.TABS.SUPPLIERS]: "Acessar aba Fornecedores",
      [ADMINISTRATIVE_PERMISSIONS.DOCUMENTS.VIEW]: "Visualizar documentos",
      [ADMINISTRATIVE_PERMISSIONS.DOCUMENTS.CREATE]: "Criar documentos",
      [ADMINISTRATIVE_PERMISSIONS.DOCUMENTS.EDIT]: "Editar documentos",
      [ADMINISTRATIVE_PERMISSIONS.DOCUMENTS.DELETE]: "Excluir documentos",
      [ADMINISTRATIVE_PERMISSIONS.DOCUMENTS.SIGN]: "Assinar documentos",
      [ADMINISTRATIVE_PERMISSIONS.TASKS.VIEW]: "Visualizar tarefas",
      [ADMINISTRATIVE_PERMISSIONS.TASKS.VIEW_OWN]: "Visualizar próprias tarefas",
      [ADMINISTRATIVE_PERMISSIONS.TASKS.CREATE]: "Criar tarefas",
      [ADMINISTRATIVE_PERMISSIONS.TASKS.EDIT]: "Editar tarefas",
      [ADMINISTRATIVE_PERMISSIONS.TASKS.DELETE]: "Excluir tarefas",
      [ADMINISTRATIVE_PERMISSIONS.REMINDERS.VIEW]: "Visualizar lembretes",
      [ADMINISTRATIVE_PERMISSIONS.REMINDERS.CREATE]: "Criar lembretes",
      [ADMINISTRATIVE_PERMISSIONS.REMINDERS.EDIT]: "Editar lembretes",
      [ADMINISTRATIVE_PERMISSIONS.REMINDERS.DELETE]: "Excluir lembretes",
    },
  },
  collaborators: {
    label: "Colaboradores",
    permissions: {
      [COLLABORATOR_PERMISSIONS.VIEW]: "Visualizar colaboradores",
      [COLLABORATOR_PERMISSIONS.VIEW_OWN]: "Visualizar próprio cadastro",
      [COLLABORATOR_PERMISSIONS.CREATE]: "Criar colaboradores",
      [COLLABORATOR_PERMISSIONS.EDIT]: "Editar colaboradores",
      [COLLABORATOR_PERMISSIONS.DELETE]: "Excluir colaboradores",
      [COLLABORATOR_PERMISSIONS.STATUS.UPDATE]: "Alterar status",
      [COLLABORATOR_PERMISSIONS.FINANCE.VIEW]: "Visualizar financeiro",
    },
  },
  suppliers: {
    label: "Fornecedores",
    permissions: {
      [SUPPLIER_PERMISSIONS.VIEW]: "Visualizar fornecedores",
      [SUPPLIER_PERMISSIONS.CREATE]: "Criar fornecedores",
      [SUPPLIER_PERMISSIONS.EDIT]: "Editar fornecedores",
      [SUPPLIER_PERMISSIONS.DELETE]: "Excluir fornecedores",
    },
  },
  reports: {
    label: "Relatórios",
    permissions: {
      [REPORT_PERMISSIONS.VIEW]: "Visualizar relatórios",
      [REPORT_PERMISSIONS.GENERATE]: "Gerar relatórios",
    },
  },
  statistics: {
    label: "Estatísticas",
    permissions: {
      [STATISTICS_PERMISSIONS.VIEW]: "Visualizar estatísticas",
    },
  },
  analytics: {
    label: "Analytics",
    permissions: {
      [ANALYTICS_PERMISSIONS.VIEW]: "Visualizar analytics",
    },
  },
  communication: {
    label: "Comunicação",
    permissions: {
      [COMMUNICATION_PERMISSIONS.VIEW]: "Visualizar comunicações",
      [COMMUNICATION_PERMISSIONS.SEND]: "Enviar comunicações",
    },
  },
  settings: {
    label: "Configurações",
    permissions: {
      [SETTINGS_PERMISSIONS.VIEW]: "Visualizar configurações",
      [SETTINGS_PERMISSIONS.EDIT]: "Editar configurações",
    },
  },
  audit: {
    label: "Logs de Auditoria",
    permissions: {
      [AUDIT_PERMISSIONS.VIEW]: "Visualizar logs de auditoria",
      [AUDIT_PERMISSIONS.EXPORT]: "Exportar logs de auditoria",
    },
  },
  inventory: {
    label: "Estoque",
    permissions: {
      [INVENTORY_PERMISSIONS.VIEW]: "Visualizar estoque",
      [INVENTORY_PERMISSIONS.CREATE]: "Adicionar produtos",
      [INVENTORY_PERMISSIONS.EDIT]: "Editar produtos",
      [INVENTORY_PERMISSIONS.DELETE]: "Excluir produtos",
      [INVENTORY_PERMISSIONS.REPORTS]: "Gerar relatórios de estoque",
      [INVENTORY_PERMISSIONS.DEPARTMENTS.MATERIAL_ESPORTIVO]: "Visualizar departamento Material Esportivo",
      [INVENTORY_PERMISSIONS.TABS.CADASTRO]: "Acessar aba Cadastro de Produto",
      [INVENTORY_PERMISSIONS.TABS.SOLICITACOES]: "Acessar aba Solicitações",
      [INVENTORY_PERMISSIONS.TABS.RELATORIOS]: "Acessar aba Relatórios por Departamento",
      [INVENTORY_PERMISSIONS.TABS.BAIXO_ESTOQUE]: "Acessar aba Produtos com Baixo Estoque",
      [INVENTORY_PERMISSIONS.TABS.LISTA_COMPRAS]: "Acessar aba Lista de Compras",
      [INVENTORY_PERMISSIONS.REQUESTS.VIEW]: "Visualizar solicitações de estoque",
      [INVENTORY_PERMISSIONS.REQUESTS.CREATE]: "Criar solicitações de estoque",
      [INVENTORY_PERMISSIONS.REQUESTS.EDIT]: "Editar solicitações de estoque",
      [INVENTORY_PERMISSIONS.REQUESTS.DELETE]: "Excluir solicitações de estoque",
      [INVENTORY_PERMISSIONS.REQUESTS.APPROVE]: "Aprovar solicitações de estoque",
      [INVENTORY_PERMISSIONS.REQUESTS.PROCESS]: "Processar solicitações de estoque",
    },
  },
  president: {
    label: "Presidente",
    permissions: {
      [PRESIDENT_PERMISSIONS.CLUB_OWNERSHIP]: "Propriedade do clube",
      [PRESIDENT_PERMISSIONS.FINANCIAL_APPROVAL]: "Aprovação financeira",
    },
  },
};

// Definição de papéis predefinidos
export const ROLES = {
  president: {
    label: "Presidente",
    description: "Acesso completo a todas as funcionalidades, incluindo propriedade do clube",
  },
  admin: {
    label: "Administrador",
    description: "Acesso completo a todas as funcionalidades, exceto propriedade do clube",
  },
  manager: {
    label: "Gerente",
    description: "Acesso a gerenciamento de jogadores e partidas",
  },
  coach: {
    label: "Treinador",
    description: "Acesso a jogadores, partidas e treinamentos",
  },
  medical: {
    label: "Médico",
    description: "Acesso ao departamento médico e jogadores",
  },
  staff: {
    label: "Funcionário",
    description: "Acesso limitado a visualização",
  },
  player: {
    label: "Jogador",
    description: "Acesso apenas ao próprio perfil",
  },
};

// Permissões predefinidas para cada papel
export const ROLE_PERMISSIONS = {
  // Presidente tem todas as permissões
  president: Object.values(PERMISSION_GROUPS).reduce((acc, group) => {
    Object.keys(group.permissions).forEach((perm) => {
      acc[perm] = true;
    });
    return acc;
  }, {} as Record<string, boolean>),

  // Admin tem todas as permissões exceto as de presidente
  admin: Object.values(PERMISSION_GROUPS)
    .filter(group => group.label !== "Presidente")
    .reduce((acc, group) => {
      Object.keys(group.permissions).forEach((perm) => {
        acc[perm] = true;
      });
      return acc;
    }, {} as Record<string, boolean>),

  // Gerente tem acesso amplo mas não completo
  manager: {
    [PLAYER_PERMISSIONS.VIEW]: true,
    [PLAYER_PERMISSIONS.CREATE]: true,
    [PLAYER_PERMISSIONS.EDIT]: true,
    [PLAYER_PERMISSIONS.DOCUMENTS.VIEW]: true,
    [PLAYER_PERMISSIONS.DOCUMENTS.VERIFY]: true,
    [PLAYER_PERMISSIONS.FINANCES.VIEW]: true,
    [PLAYER_PERMISSIONS.FINANCES.MONTHLY_FEES]: true,
    [PLAYER_PERMISSIONS.OCCURRENCES.VIEW]: true,
    [PLAYER_PERMISSIONS.OCCURRENCES.CREATE]: true,
    [PLAYER_PERMISSIONS.OCCURRENCES.EDIT]: true,
    [PLAYER_PERMISSIONS.OCCURRENCES.DELETE]: true,
    [PLAYER_PERMISSIONS.PROFILE_TABS.INFO]: true,
    [PLAYER_PERMISSIONS.PROFILE_TABS.MEDICAL]: true,
    [PLAYER_PERMISSIONS.PROFILE_TABS.DOCUMENTS]: true,
    [PLAYER_PERMISSIONS.PROFILE_TABS.FINANCES]: true,
    [PLAYER_PERMISSIONS.PROFILE_TABS.OCCURRENCES]: true,
    [PLAYER_PERMISSIONS.PROFILE_TABS.EVALUATION]: true,
    [PLAYER_PERMISSIONS.STATUS.UPDATE]: true,
    [PLAYER_PERMISSIONS.TABS.PLAYERS]: true,
    [PLAYER_PERMISSIONS.TABS.INACTIVE]: true,
    [PLAYER_PERMISSIONS.TABS.CARDS]: true,
    [PLAYER_PERMISSIONS.TABS.TACTICS]: true,
    [PLAYER_PERMISSIONS.TABS.MEMBERS]: true,
    [MATCH_PERMISSIONS.VIEW]: true,
    [MATCH_PERMISSIONS.CREATE]: true,
    [MATCH_PERMISSIONS.EDIT]: true,
    [MATCH_PERMISSIONS.LINEUP]: true,
    [MATCH_PERMISSIONS.EVENTS]: true,
    [TRAINING_PERMISSIONS.VIEW]: true,
    [TRAINING_PERMISSIONS.CREATE]: true,
    [TRAINING_PERMISSIONS.EDIT]: true,
    [TRAINING_PERMISSIONS.LOCATIONS]: true,
    [DEPARTMENT_PERMISSIONS.VIEW]: true,
    [USER_PERMISSIONS.VIEW]: true,
    [SETTINGS_PERMISSIONS.VIEW]: true,
    [FINANCE_PERMISSIONS.VIEW]: true,
    [MEDICAL_PERMISSIONS.VIEW]: true,
    [INJURY_PREVENTION_PERMISSIONS.VIEW]: true,
    [INJURY_PREVENTION_PERMISSIONS.CREATE]: true,
    [INJURY_PREVENTION_PERMISSIONS.EDIT]: true,
    [INJURY_PREVENTION_PERMISSIONS.DELETE]: true,
    [INJURY_PREVENTION_PERMISSIONS.ANALYTICS]: true,
    [INJURY_PREVENTION_PERMISSIONS.WELLNESS.VIEW]: true,
    [INJURY_PREVENTION_PERMISSIONS.WELLNESS.CREATE]: true,
    [INJURY_PREVENTION_PERMISSIONS.WELLNESS.EDIT]: true,
    [INJURY_PREVENTION_PERMISSIONS.WORKLOAD.VIEW]: true,
    [INJURY_PREVENTION_PERMISSIONS.WORKLOAD.CREATE]: true,
    [INJURY_PREVENTION_PERMISSIONS.WORKLOAD.EDIT]: true,
    [INJURY_PREVENTION_PERMISSIONS.ALERTS.VIEW]: true,
    [INJURY_PREVENTION_PERMISSIONS.ALERTS.ACKNOWLEDGE]: true,
    [AGENDA_PERMISSIONS.VIEW]: true,
    [AGENDA_PERMISSIONS.CREATE]: true,
    [AGENDA_PERMISSIONS.EDIT]: true,
    [CATEGORY_PERMISSIONS.VIEW]: true,
    [COMPETITION_PERMISSIONS.VIEW]: true,
    [COMPETITION_PERMISSIONS.CREATE]: true,
    [COMPETITION_PERMISSIONS.EDIT]: true,
    [COMPETITION_PERMISSIONS.DELETE]: true,
    [ACCOMMODATION_PERMISSIONS.VIEW]: true,
    [CALLUP_PERMISSIONS.VIEW]: true,
    [CALLUP_PERMISSIONS.CREATE]: true,
    [CALLUP_PERMISSIONS.EDIT]: true,
    [REPORT_PERMISSIONS.VIEW]: true,
    [REPORT_PERMISSIONS.GENERATE]: true,
    [STATISTICS_PERMISSIONS.VIEW]: true,
    [AUDIT_PERMISSIONS.VIEW]: true,
    [INVENTORY_PERMISSIONS.VIEW]: true,
    [INVENTORY_PERMISSIONS.CREATE]: true,
    [INVENTORY_PERMISSIONS.EDIT]: true,
    [INVENTORY_PERMISSIONS.REPORTS]: true,
    [INVENTORY_PERMISSIONS.DEPARTMENTS.MATERIAL_ESPORTIVO]: true,
    [INVENTORY_PERMISSIONS.TABS.CADASTRO]: true,
    [INVENTORY_PERMISSIONS.TABS.SOLICITACOES]: true,
    [INVENTORY_PERMISSIONS.TABS.RELATORIOS]: true,
    [INVENTORY_PERMISSIONS.TABS.BAIXO_ESTOQUE]: true,
    [INVENTORY_PERMISSIONS.TABS.LISTA_COMPRAS]: true,
    [INVENTORY_PERMISSIONS.REQUESTS.VIEW]: true,
    [INVENTORY_PERMISSIONS.REQUESTS.CREATE]: true,
    [INVENTORY_PERMISSIONS.REQUESTS.EDIT]: true,
    [INVENTORY_PERMISSIONS.REQUESTS.DELETE]: true,
    [INVENTORY_PERMISSIONS.REQUESTS.APPROVE]: true,
    [INVENTORY_PERMISSIONS.REQUESTS.PROCESS]: true,
    [EVALUATION_PERMISSIONS.TABS.PLAYERS]: true,
    [EVALUATION_PERMISSIONS.TABS.INVITATIONS]: true,
    [EVALUATION_PERMISSIONS.TABS.NEW_INVITATION]: true,
    [EVALUATION_PERMISSIONS.TABS.DASHBOARD]: true,
    [EVALUATION_PERMISSIONS.INVITATIONS.CREATE]: true,
    [EVALUATION_PERMISSIONS.INVITATIONS.COPY_LINK]: true,
    [EVALUATION_PERMISSIONS.INVITATIONS.RESEND]: true,
    [EVALUATION_PERMISSIONS.INVITATIONS.DELETE]: true,
    [EVALUATION_PERMISSIONS.PLAYERS.EDIT]: true,
    [EVALUATION_PERMISSIONS.PLAYERS.DELETE]: true,
    [EVALUATION_PERMISSIONS.PLAYERS.SCHEDULE]: true,
    [EVALUATION_PERMISSIONS.PLAYERS.UPDATE_STATUS]: true,
    [EVALUATION_PERMISSIONS.PLAYERS.VERIFY_DOCUMENTS]: true,
    [MAPPING_PERMISSIONS.VIEW]: true,
    [MAPPING_PERMISSIONS.CREATE]: true,
    [MAPPING_PERMISSIONS.EDIT]: true,
    [MAPPING_PERMISSIONS.DELETE]: true,
    [OPPONENT_PERMISSIONS.VIEW]: true,
    [OPPONENT_PERMISSIONS.CREATE]: true,
    [OPPONENT_PERMISSIONS.EDIT]: true,
    [OPPONENT_PERMISSIONS.DELETE]: true,
  },

  // Treinador tem acesso a jogadores e partidas
  coach: {
    [PLAYER_PERMISSIONS.VIEW]: true,
    [PLAYER_PERMISSIONS.EDIT]: true,
    [PLAYER_PERMISSIONS.STATUS.UPDATE]: true,
    [PLAYER_PERMISSIONS.TABS.PLAYERS]: true,
    [PLAYER_PERMISSIONS.TABS.INACTIVE]: true,
    [PLAYER_PERMISSIONS.TABS.CARDS]: true,
    [PLAYER_PERMISSIONS.TABS.TACTICS]: true,
    [MATCH_PERMISSIONS.VIEW]: true,
    [MATCH_PERMISSIONS.LINEUP]: true,
    [MATCH_PERMISSIONS.EVENTS]: true,
    [TRAINING_PERMISSIONS.VIEW]: true,
    [TRAINING_PERMISSIONS.CREATE]: true,
    [TRAINING_PERMISSIONS.EDIT]: true,
    [TRAINING_PERMISSIONS.LOCATIONS]: true,
    [AGENDA_PERMISSIONS.VIEW]: true,
    [CATEGORY_PERMISSIONS.VIEW]: true,
    [CALLUP_PERMISSIONS.VIEW]: true,
    [CALLUP_PERMISSIONS.CREATE]: true,
    [CALLUP_PERMISSIONS.EDIT]: true,
    [PLAYER_PERMISSIONS.OCCURRENCES.VIEW]: true,
    [PLAYER_PERMISSIONS.OCCURRENCES.CREATE]: true,
    [PLAYER_PERMISSIONS.OCCURRENCES.EDIT]: true,
    [PLAYER_PERMISSIONS.OCCURRENCES.DELETE]: true,
    [PLAYER_PERMISSIONS.PROFILE_TABS.INFO]: true,
    [PLAYER_PERMISSIONS.PROFILE_TABS.MEDICAL]: true,
    [PLAYER_PERMISSIONS.PROFILE_TABS.DOCUMENTS]: true,
    [PLAYER_PERMISSIONS.PROFILE_TABS.FINANCES]: true,
    [PLAYER_PERMISSIONS.FINANCES.MONTHLY_FEES]: true,
    [PLAYER_PERMISSIONS.PROFILE_TABS.OCCURRENCES]: true,
    [PLAYER_PERMISSIONS.PROFILE_TABS.EVALUATION]: true,
    [STATISTICS_PERMISSIONS.VIEW]: true,
    [EVALUATION_PERMISSIONS.TABS.PLAYERS]: true,
    [EVALUATION_PERMISSIONS.TABS.DASHBOARD]: true,
    [EVALUATION_PERMISSIONS.PLAYERS.SCHEDULE]: true,
    [EVALUATION_PERMISSIONS.PLAYERS.UPDATE_STATUS]: true,
    [EVALUATION_PERMISSIONS.PLAYERS.VERIFY_DOCUMENTS]: true,
    [MAPPING_PERMISSIONS.VIEW]: true,
    [MAPPING_PERMISSIONS.CREATE]: true,
    [MAPPING_PERMISSIONS.EDIT]: true,
    [MAPPING_PERMISSIONS.DELETE]: true,
    [OPPONENT_PERMISSIONS.VIEW]: true,
    [OPPONENT_PERMISSIONS.CREATE]: true,
    [OPPONENT_PERMISSIONS.EDIT]: true,
    [OPPONENT_PERMISSIONS.DELETE]: true,
  },

  // Médico tem acesso a registros médicos
  medical: {
    [PLAYER_PERMISSIONS.VIEW]: true,
    [MEDICAL_PERMISSIONS.VIEW]: true,
    [MEDICAL_PERMISSIONS.CREATE]: true,
    [MEDICAL_PERMISSIONS.EDIT]: true,
    [MEDICAL_PERMISSIONS.RECORDS.VIEW]: true,
    [MEDICAL_PERMISSIONS.RECORDS.CREATE]: true,
    [MEDICAL_PERMISSIONS.RECORDS.EDIT]: true,
    [MEDICAL_PERMISSIONS.REHABILITATION.VIEW]: true,
    [MEDICAL_PERMISSIONS.REHABILITATION.CREATE]: true,
    [MEDICAL_PERMISSIONS.REHABILITATION.EDIT]: true,
    [MEDICAL_PERMISSIONS.AGENDA.VIEW]: true,
    [MEDICAL_PERMISSIONS.APPOINTMENTS.CREATE]: true,
    [MEDICAL_PERMISSIONS.APPOINTMENTS.EDIT]: true,
    [MEDICAL_PERMISSIONS.APPOINTMENTS.START]: true,
    [MEDICAL_PERMISSIONS.APPOINTMENTS.DELETE]: true,
    [MEDICAL_PERMISSIONS.EXAM_REQUESTS.VIEW]: true,
    [MEDICAL_PERMISSIONS.EXAM_REQUESTS.CREATE]: true,
    [MEDICAL_PROFESSIONAL_PERMISSIONS.VIEW]: true,
    [MEDICAL_PROFESSIONAL_PERMISSIONS.EDIT_OWN]: true,
    [MEDICAL_AVAILABILITY_PERMISSIONS.VIEW]: true,
    [MEDICAL_AVAILABILITY_PERMISSIONS.CREATE]: true,
    [MEDICAL_AVAILABILITY_PERMISSIONS.EDIT]: true,
    [MEDICAL_AVAILABILITY_PERMISSIONS.DELETE]: true,
    [INJURY_PREVENTION_PERMISSIONS.VIEW]: true,
    [INJURY_PREVENTION_PERMISSIONS.CREATE]: true,
    [INJURY_PREVENTION_PERMISSIONS.EDIT]: true,
    [INJURY_PREVENTION_PERMISSIONS.DELETE]: true,
    [INJURY_PREVENTION_PERMISSIONS.ANALYTICS]: true,
    [INJURY_PREVENTION_PERMISSIONS.WELLNESS.VIEW]: true,
    [INJURY_PREVENTION_PERMISSIONS.WELLNESS.CREATE]: true,
    [INJURY_PREVENTION_PERMISSIONS.WELLNESS.EDIT]: true,
    [INJURY_PREVENTION_PERMISSIONS.WORKLOAD.VIEW]: true,
    [INJURY_PREVENTION_PERMISSIONS.WORKLOAD.CREATE]: true,
    [INJURY_PREVENTION_PERMISSIONS.WORKLOAD.EDIT]: true,
    [INJURY_PREVENTION_PERMISSIONS.ALERTS.VIEW]: true,
    [INJURY_PREVENTION_PERMISSIONS.ALERTS.ACKNOWLEDGE]: true,
    [AGENDA_PERMISSIONS.VIEW]: true,
    [AGENDA_PERMISSIONS.CREATE]: true,
    [AGENDA_PERMISSIONS.EDIT]: true,
    [PLAYER_PERMISSIONS.PROFILE_TABS.INFO]: true,
    [PLAYER_PERMISSIONS.PROFILE_TABS.MEDICAL]: true,
    [PLAYER_PERMISSIONS.PROFILE_TABS.DOCUMENTS]: true,
    [PLAYER_PERMISSIONS.PROFILE_TABS.FINANCES]: true,
    [PLAYER_PERMISSIONS.FINANCES.MONTHLY_FEES]: true,
    [PLAYER_PERMISSIONS.PROFILE_TABS.OCCURRENCES]: true,
    [PLAYER_PERMISSIONS.PROFILE_TABS.EVALUATION]: true,
  },

  // Funcionário tem acesso limitado
  staff: {
    [PLAYER_PERMISSIONS.VIEW]: true,
    [PLAYER_PERMISSIONS.TABS.PLAYERS]: true,
    [MATCH_PERMISSIONS.VIEW]: true,
    [AGENDA_PERMISSIONS.VIEW]: true,
  },

  // Jogador tem acesso apenas ao próprio perfil
  player: {
    // Permissões mínimas para jogadores
    [DASHBOARD_PERMISSIONS.VIEW]: true,
    [PLAYER_PERMISSIONS.VIEW_OWN]: true,
    [PLAYER_PERMISSIONS.EDIT_OWN]: true,
    [PLAYER_PERMISSIONS.EVALUATION.VIEW]: true,
    [PLAYER_PERMISSIONS.PROFILE_TABS.FINANCES]: true, // Acesso à aba Finanças
    [PLAYER_PERMISSIONS.FINANCES.MONTHLY_FEES]: true, // Acesso específico às mensalidades
    [INVENTORY_PERMISSIONS.REQUESTS.CREATE]: true,
    [INVENTORY_PERMISSIONS.REQUESTS.EDIT]: true,
    // Permissões para wellness (jogadores podem registrar seus próprios dados)
    [INJURY_PREVENTION_PERMISSIONS.WELLNESS.CREATE]: true,
    [INJURY_PREVENTION_PERMISSIONS.WELLNESS.VIEW]: true,
  },
};