import { useEffect, useCallback } from 'react';
import { io, Socket } from 'socket.io-client';
import { supabase } from '@/integrations/supabase/client';
import { useChatStore } from '@/store/useChatStore';
import { useUser } from '@/context/UserContext';
import type { ChatMessage, ChatRoom, UserPresence } from '@/types/chat';

let socket: Socket | null = null;
let refCount = 0;

export function useSocketChat() {
  const { user } = useUser();
  const {
    addMessage,
    updateUserPresence,
    addRoom,
    setOnlineUsers,
    setError,
    setIsConnected,
    setCurrentRoom
  } = useChatStore();

  useEffect(() => {
    if (!user?.id) return;

    // TEMPORARIAMENTE DESABILITADO - Chat com requisições infinitas
    console.log('🚫 Chat temporariamente desabilitado para evitar requisições infinitas');
    setIsConnected(false);
    setError('Chat temporariamente desabilitado');
    return;

    refCount += 1;
    let active = true;

    const connectSocket = async () => {
      if (socket?.connected) {
        setIsConnected(true);
        return;
      }

      try {
        const { data: { session } } = await supabase.auth.getSession();
        const token = session?.access_token;
        const storedClubId = localStorage.getItem('clubId');
        const clubId = user?.club_id ?? (storedClubId ? Number(storedClubId) : undefined);

        if (!token || !clubId || !active) return;

        console.log('\uD83D\uDD0C Conectando Socket.IO...', user.email, 'club', clubId);
        const socketUrl =
          import.meta.env.VITE_SOCKET_URL ||
          (import.meta.env.PROD
            ? 'https://chat-server-iota-inky.vercel.app'
            : 'http://localhost:3001');

        const socketPath = '/api/socket';

        socket = io(socketUrl, {
          auth: { token, clubId },
          // Vercel não oferece suporte nativo a WebSockets persistentes.
          // Forçamos o uso de long polling para garantir a conexão.
          transports: ['polling'],
          timeout: 20000,
          path: socketPath
        });

        const s = socket;

        s.on('connect', () => {
          console.log('\u2705 Conectado!');
          if (active) {
            setIsConnected(true);
            setError(null);
          }
        });

        s.on('disconnect', () => {
          console.log('\u274C Desconectado');
          if (active) setIsConnected(false);
        });

        s.on('connect_error', (error) => {
          console.error('\u274C Erro:', error.message);
          if (active) {
            setError(error.message);
            setIsConnected(false);
          }
        });

        s.on('new-message', (message: ChatMessage) => {
          if (active) addMessage(message.room_id, message);
        });

        s.on('user-presence-update', (raw: Record<string, unknown>) => {
          if (!active) return;
          const userData = raw as {
            user_id: string;
            club_id?: string;
            status: UserPresence['status'];
            name: string;
            avatar_url?: string;
            last_seen?: string;
          };
          updateUserPresence({
            user_id: userData.user_id,
            club_id: userData.club_id || '',
            status: userData.status,
            last_seen: userData.last_seen || new Date().toISOString(),
            updated_at: new Date().toISOString(),
            user: {
              id: userData.user_id,
              name: userData.name,
              avatar_url: userData.avatar_url
            }
          });
        });

        s.on('online-users-list', (rawUsers: Array<Record<string, unknown>>) => {
          if (!active) return;
          const mapped = rawUsers.map(u => {
            const user = u as {
              id: string;
              clubId?: string;
              status: UserPresence['status'];
              name: string;
              avatar_url?: string;
            };
            return {
              user_id: user.id,
              club_id: user.clubId || '',
              status: user.status,
              last_seen: new Date().toISOString(),
              updated_at: new Date().toISOString(),
              user: {
                id: user.id,
                name: user.name,
                avatar_url: user.avatar_url
              }
            };
          });
          setOnlineUsers(mapped);
        });

        s.on('room-created', (room: ChatRoom) => {
          console.log('room-created recebido:', room);
          if (active) addRoom(room);
        });

        s.on(
          'direct-chat-created',
          ({ room, with_user }: { room: ChatRoom; with_user: { name: string } }) => {
            if (!active) return;
            const displayRoom = { ...room, display_name: with_user.name };
            addRoom(displayRoom);
            setCurrentRoom(displayRoom);
          }
        );

        s.on(
          'new-direct-chat',
          ({ room, with_user }: { room: ChatRoom; with_user: { name: string } }) => {
            if (!active) return;
            addRoom({ ...room, display_name: with_user.name });
          }
        );

        s.on('error', (error) => {
          console.error('\u274C Erro do socket:', error);
          const message = (error as { message?: string })?.message || 'Erro desconhecido do socket';
          const isDirectChatError =
            message.toLowerCase().includes('chat direto') ||
            message.toLowerCase().includes('usuário não encontrado');
          setError(
            isDirectChatError
              ? `N\u00E3o foi poss\u00EDvel criar o chat direto: ${message}`
              : message
          );
        });
      } catch (error) {
        console.error('Erro ao conectar:', error);
      }
    };

    connectSocket();

    return () => {
      active = false;
      refCount -= 1;
      if (refCount === 0) {
        socket?.disconnect();
        socket = null;
        setIsConnected(false);
      }
    };
  }, [
    user?.id,
    user?.club_id,
    user?.email,
    addMessage,
    updateUserPresence,
    addRoom,
    setOnlineUsers,
    setError,
    setIsConnected,
    setCurrentRoom
  ]);

  const sendMessage = useCallback(
    (roomId: string, content: string, replyTo?: string) => {
      if (!socket?.connected) {
        setError('N\u00E3o conectado ao chat');
        return;
      }

      console.log('\uD83D\uDCE4 Enviando mensagem:', { roomId, content });
      socket.emit('send-message', {
        room_id: roomId,
        content,
        reply_to: replyTo,
        message_type: 'text'
      });
    },
    [setError]
  );

  const createRoom = useCallback(
    (name: string, description?: string) => {
      if (!socket?.connected) {
        setError('N\u00E3o conectado ao chat');
        return;
      }

      console.log('\uD83C\uDFE0 Criando sala:', { name, description });
      socket.emit('create-room', { name, description }, (response: { error?: string }) => {
        if (response?.error) {
          console.error('Erro ao criar sala:', response.error);
        }
      });
    },
    [setError]
  );

  const joinRoom = useCallback(
    (roomId: string) => {
      if (!socket?.connected) {
        setError('N\u00E3o conectado ao chat');
        return;
      }

      console.log('\uD83D\uDEAA Entrando na sala:', roomId);
      socket.emit('join-room', roomId);
    },
    [setError]
  );

  const createDirectChat = useCallback(
    (targetUserId: string) => {
      if (!socket?.connected) {
        setError('N\u00E3o conectado ao chat');
        return;
      }

      console.log('\uD83D\uDCAC Criando chat direto com:', targetUserId);
      socket.emit('create-direct-chat', targetUserId);
    },
    [setError]
  );

  const markAsRead = useCallback((roomId: string) => {
    if (!socket?.connected) return;

    socket.emit('mark-as-read', roomId);
  }, []);

  return {
    sendMessage,
    createRoom,
    joinRoom,
    createDirectChat,
    markAsRead,
    isConnected: socket?.connected || false
  };
}