import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/hooks/use-toast';
import { 
  Search, 
  Filter, 
  Plus, 
  User, 
  Users, 
  Briefcase, 
  Heart,
  Star,
  Eye,
  MapPin,
  Calendar,
  TrendingUp
} from 'lucide-react';
import { MarketProfileForm } from '@/components/market/MarketProfileForm';
import { MarketSearchFilters } from '@/components/market/MarketSearchFilters';
import { MarketProfileCard } from '@/components/market/MarketProfileCard';
import { MarketProfileDetails } from '@/components/market/MarketProfileDetails';
import { 
  searchMarketProfiles, 
  getMyMarketProfile,
  type MarketProfile,
  type MarketProfileFilters 
} from '@/api/marketProfiles';
import { getFavorites, getSavedSearches } from '@/api/marketFavorites';

// =====================================================
// COMPONENTE PRINCIPAL
// =====================================================

export default function AnalyseMercado() {
  const [activeTab, setActiveTab] = useState('search');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [selectedProfile, setSelectedProfile] = useState<MarketProfile | null>(null);
  const [myProfile, setMyProfile] = useState<MarketProfile | null>(null);
  const [searchResults, setSearchResults] = useState<MarketProfile[]>([]);
  const [favorites, setFavorites] = useState<any[]>([]);
  const [savedSearches, setSavedSearches] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchFilters, setSearchFilters] = useState<MarketProfileFilters>({});
  const [searchText, setSearchText] = useState('');
  const [totalResults, setTotalResults] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [showFilters, setShowFilters] = useState(false);

  // =====================================================
  // EFEITOS E CARREGAMENTO INICIAL
  // =====================================================

  useEffect(() => {
    loadInitialData();
  }, []);

  useEffect(() => {
    if (activeTab === 'search') {
      performSearch();
    } else if (activeTab === 'favorites') {
      loadFavorites();
    } else if (activeTab === 'saved-searches') {
      loadSavedSearches();
    }
  }, [activeTab, searchFilters, currentPage]);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      
      // Carregar perfil do usuário
      const profile = await getMyMarketProfile();
      setMyProfile(profile);
      
      // Fazer busca inicial
      await performSearch();
    } catch (error: any) {
      console.error('Erro ao carregar dados iniciais:', error);
    } finally {
      setLoading(false);
    }
  };

  // =====================================================
  // FUNÇÕES DE BUSCA
  // =====================================================

  const performSearch = async () => {
    try {
      setLoading(true);
      
      const filters = {
        ...searchFilters,
        search_text: searchText || undefined,
        limit: 20,
        offset: (currentPage - 1) * 20
      };

      const result = await searchMarketProfiles(filters);
      setSearchResults(result.profiles);
      setTotalResults(result.total);
    } catch (error: any) {
      toast({
        title: "Erro",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    setCurrentPage(1);
    performSearch();
  };

  const handleFilterChange = (newFilters: MarketProfileFilters) => {
    setSearchFilters(newFilters);
    setCurrentPage(1);
  };

  // =====================================================
  // FUNÇÕES DE CARREGAMENTO
  // =====================================================

  const loadFavorites = async () => {
    try {
      setLoading(true);
      const favs = await getFavorites();
      setFavorites(favs);
    } catch (error: any) {
      toast({
        title: "Erro",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const loadSavedSearches = async () => {
    try {
      setLoading(true);
      const searches = await getSavedSearches();
      setSavedSearches(searches);
    } catch (error: any) {
      toast({
        title: "Erro",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  // =====================================================
  // HANDLERS DE EVENTOS
  // =====================================================

  const handleProfileCreated = (profile: MarketProfile) => {
    setMyProfile(profile);
    setShowCreateForm(false);
    setActiveTab('my-profile');
    toast({
      title: "Sucesso",
      description: "Perfil criado com sucesso!"
    });
  };

  const handleProfileUpdated = (profile: MarketProfile) => {
    setMyProfile(profile);
    setShowEditForm(false);
    toast({
      title: "Sucesso",
      description: "Perfil atualizado com sucesso!"
    });
  };

  const handleProfileClick = (profile: MarketProfile) => {
    setSelectedProfile(profile);
  };

  // =====================================================
  // ESTATÍSTICAS RÁPIDAS
  // =====================================================

  const getQuickStats = () => {
    const playerCount = searchResults.filter(p => p.profile_type === 'player').length;
    const staffCount = searchResults.filter(p => p.profile_type !== 'player').length;
    const availableCount = searchResults.filter(p => p.availability_status === 'available').length;
    
    return { playerCount, staffCount, availableCount };
  };

  const stats = getQuickStats();

  // =====================================================
  // RENDERIZAÇÃO CONDICIONAL
  // =====================================================

  if (showCreateForm) {
    return (
      <MarketProfileForm
        onSuccess={handleProfileCreated}
        onCancel={() => setShowCreateForm(false)}
      />
    );
  }

  if (showEditForm && myProfile) {
    return (
      <MarketProfileForm
        editMode
        initialData={myProfile}
        onSuccess={handleProfileUpdated}
        onCancel={() => setShowEditForm(false)}
      />
    );
  }

  if (selectedProfile) {
    return (
      <MarketProfileDetails
        profile={selectedProfile}
        onBack={() => setSelectedProfile(null)}
      />
    );
  }

  // =====================================================
  // RENDER PRINCIPAL
  // =====================================================

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Análise de Mercado</h1>
          <p className="text-gray-600 mt-1">
            Encontre jogadores, comissão técnica e staff para seu clube
          </p>
        </div>
        
        <div className="flex gap-2">
          {!myProfile ? (
            <Button onClick={() => setShowCreateForm(true)} className="flex items-center gap-2">
              <Plus className="w-4 h-4" />
              Criar Meu Perfil
            </Button>
          ) : (
            <Button 
              variant="outline" 
              onClick={() => setShowEditForm(true)}
              className="flex items-center gap-2"
            >
              <User className="w-4 h-4" />
              Editar Meu Perfil
            </Button>
          )}
        </div>
      </div>

      {/* Estatísticas Rápidas */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total de Perfis</p>
                <p className="text-2xl font-bold">{totalResults}</p>
              </div>
              <Users className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Jogadores</p>
                <p className="text-2xl font-bold">{stats.playerCount}</p>
              </div>
              <User className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Staff</p>
                <p className="text-2xl font-bold">{stats.staffCount}</p>
              </div>
              <Briefcase className="w-8 h-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Disponíveis</p>
                <p className="text-2xl font-bold">{stats.availableCount}</p>
              </div>
              <TrendingUp className="w-8 h-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs Principais */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="search">Buscar</TabsTrigger>
          <TabsTrigger value="favorites">Favoritos</TabsTrigger>
          <TabsTrigger value="saved-searches">Buscas Salvas</TabsTrigger>
          <TabsTrigger value="my-profile">Meu Perfil</TabsTrigger>
        </TabsList>

        {/* Aba: Buscar */}
        <TabsContent value="search" className="space-y-4">
          {/* Barra de Busca */}
          <Card>
            <CardContent className="p-4">
              <div className="flex gap-2">
                <div className="flex-1">
                  <Input
                    placeholder="Buscar por nome, posição, clube..."
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  />
                </div>
                <Button onClick={handleSearch} disabled={loading}>
                  <Search className="w-4 h-4" />
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => setShowFilters(!showFilters)}
                >
                  <Filter className="w-4 h-4" />
                </Button>
              </div>
              
              {showFilters && (
                <div className="mt-4">
                  <MarketSearchFilters
                    filters={searchFilters}
                    onFiltersChange={handleFilterChange}
                  />
                </div>
              )}
            </CardContent>
          </Card>

          {/* Resultados da Busca */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {searchResults.map((profile) => (
              <MarketProfileCard
                key={profile.id}
                profile={profile}
                onClick={() => handleProfileClick(profile)}
              />
            ))}
          </div>

          {searchResults.length === 0 && !loading && (
            <Card>
              <CardContent className="p-8 text-center">
                <Search className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Nenhum perfil encontrado
                </h3>
                <p className="text-gray-600">
                  Tente ajustar os filtros ou termos de busca
                </p>
              </CardContent>
            </Card>
          )}

          {/* Paginação */}
          {totalResults > 20 && (
            <div className="flex justify-center gap-2">
              <Button
                variant="outline"
                disabled={currentPage === 1}
                onClick={() => setCurrentPage(currentPage - 1)}
              >
                Anterior
              </Button>
              <span className="flex items-center px-4">
                Página {currentPage} de {Math.ceil(totalResults / 20)}
              </span>
              <Button
                variant="outline"
                disabled={currentPage >= Math.ceil(totalResults / 20)}
                onClick={() => setCurrentPage(currentPage + 1)}
              >
                Próxima
              </Button>
            </div>
          )}
        </TabsContent>

        {/* Aba: Favoritos */}
        <TabsContent value="favorites" className="space-y-4">
          {favorites.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {favorites.map((favorite) => (
                <MarketProfileCard
                  key={favorite.id}
                  profile={favorite.profile}
                  onClick={() => handleProfileClick(favorite.profile)}
                  showFavoriteButton={false}
                />
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <Heart className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Nenhum favorito ainda
                </h3>
                <p className="text-gray-600">
                  Adicione perfis aos favoritos para acessá-los rapidamente
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Aba: Buscas Salvas */}
        <TabsContent value="saved-searches" className="space-y-4">
          {savedSearches.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {savedSearches.map((search) => (
                <Card key={search.id} className="cursor-pointer hover:shadow-md transition-shadow">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">{search.search_name}</CardTitle>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setSearchFilters(search.search_criteria);
                          setActiveTab('search');
                        }}
                      >
                        Aplicar Busca
                      </Button>
                    </div>
                    <CardDescription>
                      Criada em {new Date(search.created_at).toLocaleDateString('pt-BR')}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {search.search_criteria.profile_type && (
                        <Badge variant="outline">
                          {search.search_criteria.profile_type === 'player' ? 'Jogadores' :
                           search.search_criteria.profile_type === 'technical_staff' ? 'Comissão Técnica' : 'Staff'}
                        </Badge>
                      )}
                      {search.search_criteria.position && search.search_criteria.position.length > 0 && (
                        <div className="flex flex-wrap gap-1">
                          {search.search_criteria.position.slice(0, 3).map((pos) => (
                            <Badge key={pos} variant="secondary" className="text-xs">
                              {pos}
                            </Badge>
                          ))}
                          {search.search_criteria.position.length > 3 && (
                            <Badge variant="secondary" className="text-xs">
                              +{search.search_criteria.position.length - 3}
                            </Badge>
                          )}
                        </div>
                      )}
                      {(search.search_criteria.age_min || search.search_criteria.age_max) && (
                        <Badge variant="outline" className="text-xs">
                          Idade: {search.search_criteria.age_min || 0} - {search.search_criteria.age_max || '∞'}
                        </Badge>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <Search className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Nenhuma busca salva
                </h3>
                <p className="text-gray-600">
                  Salve suas buscas favoritas para acessá-las rapidamente
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Aba: Meu Perfil */}
        <TabsContent value="my-profile" className="space-y-4">
          {myProfile ? (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Cartão do Perfil */}
              <div className="lg:col-span-1">
                <MarketProfileCard
                  profile={myProfile}
                  onClick={() => setSelectedProfile(myProfile)}
                  showFavoriteButton={false}
                />
              </div>

              {/* Informações e Estatísticas */}
              <div className="lg:col-span-2 space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Status da Assinatura</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">Status atual:</span>
                        <Badge variant={myProfile.subscription_status === 'active' ? 'default' : 'secondary'}>
                          {myProfile.subscription_status === 'active' ? 'Premium' : 'Trial'}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">Início da assinatura:</span>
                        <span className="font-medium">
                          {new Date(myProfile.subscription_start_date).toLocaleDateString('pt-BR')}
                        </span>
                      </div>
                      {myProfile.subscription_end_date && (
                        <div className="flex items-center justify-between">
                          <span className="text-gray-600">Vencimento:</span>
                          <span className="font-medium">
                            {new Date(myProfile.subscription_end_date).toLocaleDateString('pt-BR')}
                          </span>
                        </div>
                      )}

                      {myProfile.subscription_status === 'trial' && (
                        <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                          <h4 className="font-medium text-blue-900 mb-2">Período de Teste 2024</h4>
                          <p className="text-sm text-blue-700 mb-3">
                            Você está no período de teste gratuito. A partir de 2025, será necessário
                            pagar a taxa anual de R$ 120,00 para manter seu perfil ativo.
                          </p>
                          <Button variant="outline" size="sm">
                            Saiba Mais
                          </Button>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Ações Rápidas</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Button
                        variant="outline"
                        onClick={() => setShowEditForm(true)}
                        className="flex items-center gap-2"
                      >
                        <User className="w-4 h-4" />
                        Editar Perfil
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => setSelectedProfile(myProfile)}
                        className="flex items-center gap-2"
                      >
                        <Eye className="w-4 h-4" />
                        Visualizar Perfil
                      </Button>
                      <Button
                        variant="outline"
                        onClick={handleShare}
                        className="flex items-center gap-2"
                      >
                        <Share2 className="w-4 h-4" />
                        Compartilhar Perfil
                      </Button>
                      <Button
                        variant="outline"
                        className="flex items-center gap-2"
                      >
                        <TrendingUp className="w-4 h-4" />
                        Ver Estatísticas
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Dicas para Melhorar seu Perfil</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-start gap-3">
                        <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                        <div>
                          <p className="text-sm font-medium">Adicione uma foto profissional</p>
                          <p className="text-xs text-gray-600">Perfis com foto recebem 3x mais visualizações</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                        <div>
                          <p className="text-sm font-medium">Complete sua descrição pessoal</p>
                          <p className="text-xs text-gray-600">Conte sua história e objetivos profissionais</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                        <div>
                          <p className="text-sm font-medium">Adicione vídeos e documentos</p>
                          <p className="text-xs text-gray-600">Mostre suas habilidades em ação</p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <User className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Você ainda não tem um perfil
                </h3>
                <p className="text-gray-600 mb-4">
                  Crie seu perfil para ser encontrado por clubes e oportunidades
                </p>
                <Button onClick={() => setShowCreateForm(true)} className="flex items-center gap-2">
                  <Plus className="w-4 h-4" />
                  Criar Meu Perfil
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
