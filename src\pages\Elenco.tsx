import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useClubNavigation } from "@/hooks/useClubNavigation";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { MoreHorizontal, Plus, Search, Key, Trash2 } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { DataTable } from "@/components/ui/data-table";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { PlayerDialog } from "@/components/modals/PlayerDialog";
import { PlayerCard } from "@/components/player/PlayerCard";
import { ChangePasswordDialog } from "@/components/users/ChangePasswordDialog";
import { PermissionControl } from "@/components/PermissionControl";
import { PendingEvaluationsTable } from "@/components/evaluation/PendingEvaluationsTable";
import { EvaluationApprovalStats } from "@/components/evaluation/EvaluationApprovalStats";
import { usePlayersStore } from "@/store/usePlayersStore";
import { useClubMembersStore } from "@/store/useClubMembersStore";
import { useCategoriesStore } from "@/store/useCategoriesStore";
import { useUser } from "@/context/UserContext";
import type { Player, ClubMember } from "@/api/api";
import { initializePlayerStats, syncPlayerAggregatedStats } from "@/api/api";
import { LimitGuard } from "@/components/guards/LimitGuard";
import { useCurrentClubId } from "@/context/ClubContext";
import { checkAndUpdateLoanedPlayers } from "@/utils/loanManager";
import { useToast } from "@/components/ui/use-toast";
import { usePermission } from "@/hooks/usePermission";
import { PLAYER_PERMISSIONS } from "@/constants/permissions";
import { POSITIONS } from "@/constants/positions"
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { ConfirmDialog } from "@/components/ui/confirm-dialog";

export default function Elenco() {
  const [activeTab, setActiveTab] = useState("lista");
  const navigate = useNavigate();
  const { navigateToClubPage } = useClubNavigation();
  const [playerDialogOpen, setPlayerDialogOpen] = useState(false);
  const [selectedPlayer, setSelectedPlayer] = useState<Player | null>(null);
  const [passwordPlayer, setPasswordPlayer] = useState<Player | null>(null);
  const [editMode, setEditMode] = useState(false);
  const { toast } = useToast();
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | null>(null);
  const { role, can } = usePermission();
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [playerToDelete, setPlayerToDelete] = useState<Player | null>(null);
  const [inactiveConfirmOpen, setInactiveConfirmOpen] = useState(false);
  const [playerToInactivate, setPlayerToInactivate] = useState<Player | null>(null);

  const canApproveEvaluations = role === "manager" || role === "president" || role === "admin";

  // Zustand store
  const { players, loading, error, fetchPlayers, updatePlayer, deletePlayer } = usePlayersStore();
  const { members, loading: loadingMembers, error: errorMembers, fetchMembers, addMember, removeMember } = useClubMembersStore();
  const { getCategoryPlayers } = useCategoriesStore();

  const clubId = useCurrentClubId();
  const { user } = useUser();
  const [categoryPlayers, setCategoryPlayers] = useState<Player[]>([]);
  const [loadingCategoryPlayers, setLoadingCategoryPlayers] = useState(false);

  // Estados para filtro de busca de jogadores nos cards
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedPosition, setSelectedPosition] = useState<string>("");
  const [currentPage, setCurrentPage] = useState(1);
  const playersPerPage = 20;

  useEffect(() => {
    fetchPlayers(clubId);
    fetchMembers(clubId);

    // Verificar jogadores emprestados cujo empréstimo terminou
    checkAndUpdateLoanedPlayers(clubId).then(count => {
      if (count > 0) {
        toast({
          title: "Jogadores retornaram de empréstimo",
          description: `${count} jogador(es) retornaram automaticamente de empréstimo.`,
          variant: "default",
        });

        // Recarregar a lista de jogadores para refletir as mudanças
        fetchPlayers(clubId);
      }
    });
  }, [fetchPlayers, fetchMembers, clubId, toast]);

  // Recarregar jogadores ao mudar para a aba de inativos
  useEffect(() => {
    if (activeTab === "inativos") {
      fetchPlayers(clubId);
    }
  }, [activeTab, clubId, fetchPlayers]);

  // Listen for category changes from localStorage and custom events
  useEffect(() => {
    const storedCategoryId = localStorage.getItem("selectedCategoryId");
    if (storedCategoryId) {
      setSelectedCategoryId(Number(storedCategoryId));
    } else {
      setSelectedCategoryId(null);
    }

    // Add event listener for storage changes
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === "selectedCategoryId") {
        if (e.newValue) {
          setSelectedCategoryId(Number(e.newValue));
        } else {
          setSelectedCategoryId(null);
        }
      }
    };

    // Escutar o evento personalizado de mudança de categoria
    const handleCategoryChanged = (e: CustomEvent) => {
      const { categoryId } = e.detail;
      setSelectedCategoryId(categoryId);
    };

    // Criar um observador para monitorar mudanças no localStorage em tempo real
    const checkForChanges = setInterval(() => {
      const currentCategoryId = localStorage.getItem("selectedCategoryId");
      const currentSelectedId = selectedCategoryId ? selectedCategoryId.toString() : null;

      if (currentCategoryId !== currentSelectedId) {
        if (currentCategoryId) {
          setSelectedCategoryId(Number(currentCategoryId));
        } else {
          setSelectedCategoryId(null);
        }
      }
    }, 500);

    window.addEventListener("storage", handleStorageChange);
    window.addEventListener("categoryChanged", handleCategoryChanged as EventListener);

    return () => {
      window.removeEventListener("storage", handleStorageChange);
      window.removeEventListener("categoryChanged", handleCategoryChanged as EventListener);
      clearInterval(checkForChanges);
    };
  }, [selectedCategoryId]);

  // Fetch players by category when selectedCategoryId changes
  useEffect(() => {
    if (selectedCategoryId && clubId) {
      setLoadingCategoryPlayers(true);
      getCategoryPlayers(clubId, selectedCategoryId)
        .then(players => {
          setCategoryPlayers(players);
        })
        .catch(error => {
          console.error("Error fetching category players:", error);
          setCategoryPlayers([]);
        })
        .finally(() => {
          setLoadingCategoryPlayers(false);
        });
    } else {
      setCategoryPlayers([]);
    }
  }, [selectedCategoryId, clubId, getCategoryPlayers]);

  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery, selectedPosition, selectedCategoryId, activeTab]);

  // Determine which player list to use based on category selection
  const playerList = selectedCategoryId ? categoryPlayers : players;
  const isLoading = selectedCategoryId ? loadingCategoryPlayers : loading;

  // Filtrar jogadores com base na busca, posição e no status (ativo/inativo)
  const filteredPlayers = playerList.filter(player => {
    // Remover jogadores que estão em processo de avaliação/pré-cadastro
    if (player.status === "aguardando agendamento" ||
      player.status === "aguardando documentação" ||
      player.status === "jogador agendado") {
      return false;
    }

    // Primeiro, aplicar o filtro de busca
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      const matchesSearch =
        player.name.toLowerCase().includes(query) ||
        player.position.toLowerCase().includes(query) ||
        (player.nationality && player.nationality.toLowerCase().includes(query));

      if (!matchesSearch) return false;
    }

    // Aplicar o filtro de posição
    if (selectedPosition && player.position !== selectedPosition) {
      return false;
    }

    // Depois, aplicar o filtro de status baseado na tab ativa
    if (activeTab === "inativos") {
      return player.status === "inativo";
    } else if (activeTab === "lista" || activeTab === "cards" || activeTab === "tatica") {
      // Para as tabs de jogadores ativos (lista, cards, tatica), excluir inativos
      return player.status !== "inativo";
    }

    // Para outras tabs (membros), mostrar todos os jogadores
    return true;
  }).sort((a, b) => a.name.localeCompare(b.name));

  const totalPages = Math.ceil(filteredPlayers.length / playersPerPage);
  const paginatedPlayers = filteredPlayers.slice(
    (currentPage - 1) * playersPerPage,
    currentPage * playersPerPage
  );

  // Função para navegar para o perfil do jogador
  const handleViewProfile = (playerId: string) => {
    navigateToClubPage(`/jogador/${playerId}`);
  };

  const handleEditPlayer = async (player: Player) => {
    const freshPlayer = players.find(p => p.id === player.id) || player;
    setSelectedPlayer(freshPlayer);
    setEditMode(true);
    setPlayerDialogOpen(true);
  };

  const handleInitializePlayerStats = async () => {
    try {
      await initializePlayerStats(clubId);
      toast({
        title: "Estatísticas inicializadas",
        description: "Estatísticas padrão foram criadas para jogadores sem estatísticas.",
      });
    } catch (error) {
      console.error("Erro ao inicializar estatísticas:", error);
      toast({
        title: "Erro",
        description: "Não foi possível inicializar as estatísticas dos jogadores.",
        variant: "destructive",
      });
    }
  };

  const handleSyncPlayerStats = async () => {
    try {
      await syncPlayerAggregatedStats(clubId);
      toast({
        title: "Estatísticas sincronizadas",
        description: "Todas as estatísticas dos jogadores foram recalculadas e atualizadas.",
      });
    } catch (error) {
      console.error("Erro ao sincronizar estatísticas:", error);
      toast({
        title: "Erro",
        description: "Não foi possível sincronizar as estatísticas dos jogadores.",
        variant: "destructive",
      });
    }
  };

  const handleDeleteClick = (player: Player) => {
    setPlayerToDelete(player);
    setDeleteConfirmOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!playerToDelete || !user?.id) return;
    try {
      await deletePlayer(playerToDelete.club_id, playerToDelete.id, user.id);
      toast({
        title: "Jogador excluído",
        description: `${playerToDelete.name} foi removido com sucesso.`,
      });
      fetchPlayers(playerToDelete.club_id);
    } catch (error: unknown) {
      console.error("Erro ao excluir jogador:", error);
      const message = error instanceof Error ? error.message : "Erro ao excluir jogador";
      toast({
        title: "Erro",
        description: message,
        variant: "destructive",
      });
    } finally {
      setDeleteConfirmOpen(false);
      setPlayerToDelete(null);
    }
  };

  const handleStatusChange = async (player: Player, newStatus: string) => {
    if (newStatus === "inativo" && player.status !== "inativo") {
      setPlayerToInactivate(player);
      setInactiveConfirmOpen(true);
      return;
    }
    if (user?.id) {
      try {
        await updatePlayer(player.club_id, player.id, { status: newStatus }, user.id);
        fetchPlayers(player.club_id);
        if (selectedCategoryId) {
          const updatedCategoryPlayers = await getCategoryPlayers(player.club_id, selectedCategoryId);
          setCategoryPlayers(updatedCategoryPlayers);
        }
      } catch (error) {
        console.error("Erro ao atualizar status do jogador:", error);
        toast({
          title: "Erro",
          description: "Não foi possível atualizar o status do jogador.",
          variant: "destructive",
        });
      }
    }
  };

  const handleInactivateConfirm = async () => {
    if (!playerToInactivate || !user?.id) return;
    try {
      await updatePlayer(playerToInactivate.club_id, playerToInactivate.id, { status: "inativo" }, user.id);
      fetchPlayers(playerToInactivate.club_id);
      if (selectedCategoryId) {
        const updatedCategoryPlayers = await getCategoryPlayers(playerToInactivate.club_id, selectedCategoryId);
        setCategoryPlayers(updatedCategoryPlayers);
      }
      // Permanecer na aba atual (lista) após inativar
    } catch (error) {
      console.error("Erro ao marcar jogador como inativo:", error);
      toast({
        title: "Erro",
        description: "Não foi possível marcar o jogador como inativo.",
        variant: "destructive",
      });
    } finally {
      setInactiveConfirmOpen(false);
      setPlayerToInactivate(null);
    }
  };

  // Opções de status
  const statusOptions = [
    { label: "Disponível", value: "disponivel" },
    { label: "Lesionado", value: "lesionado" },
    { label: "Suspenso", value: "suspenso" },
    { label: "Em recuperação", value: "em recuperacao" },
    { label: "Inativo", value: "inativo" },
    { label: "Emprestado", value: "emprestado" },
    { label: "Em Avaliação", value: "em avaliacao" },
  ];

  // Colunas para a tabela de membros do clube
  const memberColumns = [
    {
      key: "userName",
      header: "Nome",
      searchable: true,
      render: (member: ClubMember) => (
        <div className="flex items-center gap-3">
          <Avatar className="h-8 w-8">
            <AvatarFallback className="bg-team-blue text-white">
              {member.userName ? member.userName.slice(0, 2).toUpperCase() : 'UN'}
            </AvatarFallback>
          </Avatar>
          <span>{member.userName || 'Usuário sem nome'}</span>
        </div>
      ),
    },
    { key: "userEmail", header: "Email", searchable: true },
    { key: "role", header: "Cargo", searchable: true },
    { key: "status", header: "Status" },
  ];

  // Ações para membros do clube
  const memberActions = (member: ClubMember) => (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem 
          onClick={() => {
            if (window.confirm(`Tem certeza que deseja remover ${member.userName} do clube?`)) {
              removeMember(member.userId, member.clubId);
            }
          }}
          className="text-red-600"
        >
          Remover do clube
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );

  // Filtrar membros com base na busca externa
  const filteredMembers = members.filter(member => {
    if (!searchQuery) return true;
    const query = searchQuery.toLowerCase();
    return (
      member.userName?.toLowerCase().includes(query) ||
      member.userEmail?.toLowerCase().includes(query) ||
      member.role?.toLowerCase().includes(query)
    );
  });

  return (
    <div className="space-y-4 sm:space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">Gestão de Elenco</h1>
          <p className="text-sm sm:text-base text-muted-foreground">
            Gerencie os jogadores do seu time, contratos e estatísticas.
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-2">
          <LimitGuard limitType="players" fallback={
            <Button disabled size="sm" className="text-xs sm:text-sm">
              <Plus className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
              Limite Atingido
            </Button>
          }>
            <Button
              size="sm"
              className="hover:opacity-90 transition-opacity text-xs sm:text-sm"
              style={{
                backgroundColor: 'var(--color-primary)',
                color: 'white'
              }}
              onClick={() => {
                setSelectedPlayer(null);
                setEditMode(false);
                setPlayerDialogOpen(true);
              }}>
              <Plus className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
              Novo Jogador
            </Button>
          </LimitGuard>
        </div>
      </div>

      <Tabs defaultValue="lista" onValueChange={setActiveTab}>
        <div className="flex flex-col gap-4 mb-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <TabsList className="w-full sm:w-auto overflow-x-auto">
              <PermissionControl permission="players.tabs.players">
                <TabsTrigger value="lista" className="text-xs sm:text-sm">Atletas</TabsTrigger>
              </PermissionControl>
              <PermissionControl permission="players.tabs.inactive">
                <TabsTrigger value="inativos" className="text-xs sm:text-sm">Inativos</TabsTrigger>
              </PermissionControl>
              <PermissionControl permission="players.tabs.cards">
                <TabsTrigger value="cards" className="text-xs sm:text-sm">Cards</TabsTrigger>
              </PermissionControl>
              <PermissionControl permission="players.tabs.tactics">
                <TabsTrigger value="tatica" className="text-xs sm:text-sm">Tática</TabsTrigger>
              </PermissionControl>
              {canApproveEvaluations && (
                <TabsTrigger value="aprovacoes" className="text-xs sm:text-sm">Aprovações</TabsTrigger>
              )}
              <PermissionControl permission="players.tabs.members">
                <TabsTrigger value="membros" className="text-xs sm:text-sm">Membros</TabsTrigger>
              </PermissionControl>
            </TabsList>

            <div className="flex flex-col sm:flex-row sm:items-center gap-2 w-full sm:w-auto">
              <div className="relative w-full sm:w-64">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder={activeTab === "membros" ? "Buscar membro..." : "Buscar jogador..."}
                  className="pl-8 pr-4 text-sm"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              {activeTab !== "membros" && (
                <Select
                  value={selectedPosition}
                  onValueChange={(value) => setSelectedPosition(value === "all" ? "" : value)}
                >
                  <SelectTrigger className="w-full sm:w-40 text-sm">
                    <SelectValue placeholder="Posição" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Todas</SelectItem>
                    {POSITIONS.map((pos) => (
                      <SelectItem key={pos} value={pos}>
                        {pos}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}            </div>
          </div>
        </div>

        <PermissionControl permission="players.tabs.players">
          <TabsContent value="lista">
            {isLoading ? (
              <div className="text-center py-8">
                <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-team-blue border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"></div>
                <p className="mt-2 text-muted-foreground text-sm">Carregando jogadores...</p>
              </div>
            ) : filteredPlayers.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground text-sm">
                {selectedCategoryId
                  ? "Nenhum jogador encontrado nesta categoria."
                  : "Nenhum jogador encontrado."}
              </div>
            ) : (
              <>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse min-w-[600px]">
                    <thead>
                      <tr className="bg-gray-50 border-b">
                        <th className="py-2 sm:py-3 px-2 sm:px-4 text-left text-xs sm:text-sm font-medium text-gray-500">Nº</th>
                        <th className="py-2 sm:py-3 px-2 sm:px-4 text-left text-xs sm:text-sm font-medium text-gray-500">Jogador</th>
                        <th className="py-2 sm:py-3 px-2 sm:px-4 text-left text-xs sm:text-sm font-medium text-gray-500">Posição</th>
                        <th className="py-2 sm:py-3 px-2 sm:px-4 text-left text-xs sm:text-sm font-medium text-gray-500 hidden sm:table-cell">Idade</th>
                        <th className="py-2 sm:py-3 px-2 sm:px-4 text-left text-xs sm:text-sm font-medium text-gray-500 hidden md:table-cell">Nacionalidade</th>
                        <th className="py-2 sm:py-3 px-2 sm:px-4 text-left text-xs sm:text-sm font-medium text-gray-500">Status</th>
                        <th className="py-2 sm:py-3 px-2 sm:px-4 text-left text-xs sm:text-sm font-medium text-gray-500">Ações</th>
                      </tr>
                    </thead>
                    <tbody>
                      {paginatedPlayers.map((player) => {
                        // Formatar a data de fim de contrato para exibição
                        let contractEnd = "Não informado";
                        if (player.contract_end_date) {
                          const date = new Date(player.contract_end_date);
                          const month = date.toLocaleString('pt-BR', { month: 'short' });
                          const year = date.getFullYear();
                          contractEnd = `${month} ${year}`;
                        }

                        return (
                          <tr
                            key={player.id}
                            className="border-b hover:bg-gray-50 cursor-pointer"
                            onClick={() => handleViewProfile(player.id)}
                          >
                            <td className="py-2 sm:py-3 px-2 sm:px-4 text-xs sm:text-sm">{player.number}</td>
                            <td className="py-2 sm:py-3 px-2 sm:px-4">
                              <div className="flex items-center gap-2 sm:gap-3">
                                <Avatar className="h-6 w-6 sm:h-8 sm:w-8">
                                  <AvatarImage src={player.image || ""} />
                                  <AvatarFallback className="bg-team-blue text-white text-xs">
                                    {player.name.slice(0, 2).toUpperCase()}
                                  </AvatarFallback>
                                </Avatar>
                                <span className="font-medium text-xs sm:text-sm truncate max-w-[100px] sm:max-w-none">{player.name}</span>
                              </div>
                            </td>
                            <td className="py-2 sm:py-3 px-2 sm:px-4 text-xs sm:text-sm">{player.position}</td>
                            <td className="py-2 sm:py-3 px-2 sm:px-4 text-xs sm:text-sm hidden sm:table-cell">{player.age}</td>
                            <td className="py-2 sm:py-3 px-2 sm:px-4 text-xs sm:text-sm hidden md:table-cell">{player.nationality || "—"}</td>
                            <td className="py-2 sm:py-3 px-2 sm:px-4 text-xs sm:text-sm">
                              {can("players.status.update") ? (
                                <select
                                  value={player.status || "disponivel"}
                                  onChange={(e) => handleStatusChange(player, e.target.value)}
                                  className={`border rounded px-1 sm:px-2 py-1 text-xs relative z-50 w-full sm:w-auto
                                    ${player.status === "disponivel" ? "bg-green-50 text-green-800 border-green-200" :
                                      player.status === "lesionado" ? "bg-rose-50 text-rose-800 border-rose-200" :
                                        player.status === "suspenso" ? "bg-amber-50 text-amber-800 border-amber-200" :
                                          player.status === "em recuperacao" ? "bg-orange-50 text-orange-800 border-orange-200" :
                                            player.status === "inativo" ? "bg-gray-50 text-gray-800 border-gray-200" :
                                              player.status === "em avaliacao" ? "bg-orange-50 text-orange-800 border-orange-200" :
                                                player.status === "emprestado" ? "bg-primary/10 text-primary border-primary/20" : ""}
                                  `}
                                  onClick={e => e.stopPropagation()}
                                >
                                  {statusOptions.map(opt => (
                                    <option key={opt.value} value={opt.value}>{opt.label}</option>
                                  ))}
                                </select>
                              ) : (
                                <span className="capitalize text-xs sm:text-sm">{player.status}</span>
                              )}
                            </td>
                            <td className="py-2 sm:py-3 px-2 sm:px-4 text-xs sm:text-sm">
                              <div className="flex items-center justify-end">
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" size="sm" className="h-6 w-6 sm:h-8 sm:w-8 p-0">
                                      <MoreHorizontal className="h-3 w-3 sm:h-4 sm:w-4" />
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="end">
                                    <DropdownMenuItem onClick={(e) => {
                                      e.stopPropagation();
                                      handleViewProfile(player.id);
                                    }}>Ver perfil</DropdownMenuItem>
                                    <DropdownMenuItem onClick={(e) => {
                                      e.stopPropagation();
                                      handleEditPlayer(player);
                                    }}>Editar</DropdownMenuItem>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem onClick={(e) => {
                                      e.stopPropagation();
                                      navigateToClubPage(`/jogador/${player.id}/historico-medico`);
                                    }}>Histórico médico</DropdownMenuItem>
                                    <DropdownMenuItem onClick={(e) => {
                                      e.stopPropagation();
                                      navigateToClubPage(`/jogador/${player.id}/estatisticas`);
                                    }}>Estatísticas</DropdownMenuItem>
                                    <PermissionControl permission={PLAYER_PERMISSIONS.RESET_PASSWORD}>
                                      <DropdownMenuItem
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          if (player.user_id) {
                                            setPasswordPlayer(player);
                                          } else {
                                            toast({
                                              title: "Jogador sem conta",
                                              description: "Este jogador não possui conta vinculada",
                                              variant: "destructive",
                                            });
                                          }
                                        }}
                                      >
                                        <Key className="mr-2 h-4 w-4" />
                                        Alterar senha
                                      </DropdownMenuItem>
                                    </PermissionControl>
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </div>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>

                {/* Paginação */}
                {totalPages > 1 && (
                  <div className="flex justify-center mt-4">
                    <Pagination>
                      <PaginationContent>
                        <PaginationItem>
                          <PaginationPrevious
                            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                            className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                          />
                        </PaginationItem>
                        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                          const page = i + 1;
                          return (
                            <PaginationItem key={page}>
                              <PaginationLink
                                onClick={() => setCurrentPage(page)}
                                isActive={currentPage === page}
                                className="cursor-pointer"
                              >
                                {page}
                              </PaginationLink>
                            </PaginationItem>
                          );
                        })}
                        <PaginationItem>
                          <PaginationNext
                            onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                            className={currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                          />
                        </PaginationItem>
                      </PaginationContent>
                    </Pagination>
                  </div>
                )}
              </>
            )}
          </TabsContent>
        </PermissionControl>

        <PermissionControl permission="players.tabs.inactive">
          <TabsContent value="inativos">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg sm:text-xl">Jogadores Inativos</CardTitle>
                <CardDescription className="text-sm">
                  Lista de jogadores que foram marcados como inativos
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  {filteredPlayers.length} jogador(es) inativo(s) encontrado(s)
                </p>
                
                {isLoading ? (
                  <div className="text-center py-8">
                    <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-team-blue border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"></div>
                    <p className="mt-2 text-muted-foreground text-sm">Carregando jogadores inativos...</p>
                  </div>
                ) : filteredPlayers.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground text-sm">
                    Nenhum jogador inativo encontrado.
                  </div>
                ) : (
                  <>
                    <div className="overflow-x-auto">
                      <table className="w-full border-collapse min-w-[600px]">
                        <thead>
                          <tr className="bg-gray-50 border-b">
                            <th className="py-2 sm:py-3 px-2 sm:px-4 text-left text-xs sm:text-sm font-medium text-gray-500">Nº</th>
                            <th className="py-2 sm:py-3 px-2 sm:px-4 text-left text-xs sm:text-sm font-medium text-gray-500">Jogador</th>
                            <th className="py-2 sm:py-3 px-2 sm:px-4 text-left text-xs sm:text-sm font-medium text-gray-500">Posição</th>
                            <th className="py-2 sm:py-3 px-2 sm:px-4 text-left text-xs sm:text-sm font-medium text-gray-500 hidden sm:table-cell">Idade</th>
                            <th className="py-2 sm:py-3 px-2 sm:px-4 text-left text-xs sm:text-sm font-medium text-gray-500 hidden md:table-cell">Nacionalidade</th>
                            <th className="py-2 sm:py-3 px-2 sm:px-4 text-left text-xs sm:text-sm font-medium text-gray-500">Status</th>
                            <th className="py-2 sm:py-3 px-2 sm:px-4 text-left text-xs sm:text-sm font-medium text-gray-500">Ações</th>
                          </tr>
                        </thead>
                        <tbody>
                          {paginatedPlayers.map((player) => {
                            return (
                              <tr
                                key={player.id}
                                className="border-b hover:bg-gray-50 cursor-pointer"
                                onClick={() => handleViewProfile(player.id)}
                              >
                                <td className="py-2 sm:py-3 px-2 sm:px-4 text-xs sm:text-sm">{player.number}</td>
                                <td className="py-2 sm:py-3 px-2 sm:px-4">
                                  <div className="flex items-center gap-2 sm:gap-3">
                                    <Avatar className="h-6 w-6 sm:h-8 sm:w-8">
                                      <AvatarImage src={player.image || ""} />
                                      <AvatarFallback className="bg-gray-400 text-white text-xs">
                                        {player.name.slice(0, 2).toUpperCase()}
                                      </AvatarFallback>
                                    </Avatar>
                                    <span className="font-medium text-xs sm:text-sm truncate max-w-[100px] sm:max-w-none">{player.name}</span>
                                  </div>
                                </td>
                                <td className="py-2 sm:py-3 px-2 sm:px-4 text-xs sm:text-sm">{player.position}</td>
                                <td className="py-2 sm:py-3 px-2 sm:px-4 text-xs sm:text-sm hidden sm:table-cell">{player.age}</td>
                                <td className="py-2 sm:py-3 px-2 sm:px-4 text-xs sm:text-sm hidden md:table-cell">{player.nationality || "—"}</td>
                                <td className="py-2 sm:py-3 px-2 sm:px-4 text-xs sm:text-sm">
                                  {can("players.status.update") ? (
                                    <select
                                      value={player.status || "inativo"}
                                      onChange={(e) => handleStatusChange(player, e.target.value)}
                                      className="bg-gray-50 text-gray-800 border-gray-200 border rounded px-1 sm:px-2 py-1 text-xs relative z-50 w-full sm:w-auto"
                                      onClick={e => e.stopPropagation()}
                                    >
                                      {statusOptions.map(opt => (
                                        <option key={opt.value} value={opt.value}>{opt.label}</option>
                                      ))}
                                    </select>
                                  ) : (
                                    <Badge variant="secondary" className="text-xs">
                                      Inativo
                                    </Badge>
                                  )}
                                </td>
                                <td className="py-2 sm:py-3 px-2 sm:px-4 text-xs sm:text-sm">
                                  <div className="flex items-center justify-end">
                                    <DropdownMenu>
                                      <DropdownMenuTrigger asChild>
                                        <Button variant="ghost" size="sm" className="h-6 w-6 sm:h-8 sm:w-8 p-0">
                                          <MoreHorizontal className="h-3 w-3 sm:h-4 sm:w-4" />
                                        </Button>
                                      </DropdownMenuTrigger>
                                      <DropdownMenuContent align="end">
                                        <DropdownMenuItem onClick={(e) => {
                                          e.stopPropagation();
                                          handleViewProfile(player.id);
                                        }}>Ver perfil</DropdownMenuItem>
                                        <DropdownMenuItem onClick={(e) => {
                                          e.stopPropagation();
                                          handleEditPlayer(player);
                                        }}>Editar</DropdownMenuItem>
                                        <DropdownMenuSeparator />
                                        <DropdownMenuItem onClick={(e) => {
                                          e.stopPropagation();
                                          navigateToClubPage(`/jogador/${player.id}/historico-medico`);
                                        }}>Histórico médico</DropdownMenuItem>
                                        <DropdownMenuItem onClick={(e) => {
                                          e.stopPropagation();
                                          navigateToClubPage(`/jogador/${player.id}/estatisticas`);
                                        }}>Estatísticas</DropdownMenuItem>
                                        <PermissionControl permission={PLAYER_PERMISSIONS.RESET_PASSWORD}>
                                          <DropdownMenuItem
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              if (player.user_id) {
                                                setPasswordPlayer(player);
                                              } else {
                                                toast({
                                                  title: "Jogador sem conta",
                                                  description: "Este jogador não possui conta vinculada",
                                                  variant: "destructive",
                                                });
                                              }
                                            }}
                                          >
                                            <Key className="mr-2 h-4 w-4" />
                                            Alterar senha
                                          </DropdownMenuItem>
                                        </PermissionControl>
                                        <PermissionControl permission={PLAYER_PERMISSIONS.DELETE}>
                                          <>
                                            <DropdownMenuSeparator />
                                            <DropdownMenuItem
                                              onClick={(e) => {
                                                e.stopPropagation();
                                                handleDeleteClick(player);
                                              }}
                                              className="text-red-600"
                                            >
                                              <Trash2 className="mr-2 h-4 w-4" />
                                              Excluir
                                            </DropdownMenuItem>
                                          </>
                                        </PermissionControl>
                                      </DropdownMenuContent>
                                    </DropdownMenu>
                                  </div>
                                </td>
                              </tr>
                            );
                          })}
                        </tbody>
                      </table>
                    </div>

                    {/* Paginação para jogadores inativos */}
                    {totalPages > 1 && (
                      <div className="flex justify-center mt-4">
                        <Pagination>
                          <PaginationContent>
                            <PaginationItem>
                              <PaginationPrevious
                                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                                className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                              />
                            </PaginationItem>
                            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                              const page = i + 1;
                              return (
                                <PaginationItem key={page}>
                                  <PaginationLink
                                    onClick={() => setCurrentPage(page)}
                                    isActive={currentPage === page}
                                    className="cursor-pointer"
                                  >
                                    {page}
                                  </PaginationLink>
                                </PaginationItem>
                              );
                            })}
                            <PaginationItem>
                              <PaginationNext
                                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                                className={currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                              />
                            </PaginationItem>
                          </PaginationContent>
                        </Pagination>
                      </div>
                    )}
                  </>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </PermissionControl>

        <PermissionControl permission="players.tabs.cards">
          <TabsContent value="cards">
            <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
              {paginatedPlayers.map((player) => (
                <PlayerCard
                  key={player.id}
                  player={player}
                  onClick={() => handleViewProfile(player.id)}
                />
              ))}
            </div>

            {/* Paginação para cards */}
            {totalPages > 1 && (
              <div className="flex justify-center mt-6">
                <Pagination>
                  <PaginationContent>
                    <PaginationItem>
                      <PaginationPrevious
                        onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                        className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                      />
                    </PaginationItem>
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      const page = i + 1;
                      return (
                        <PaginationItem key={page}>
                          <PaginationLink
                            onClick={() => setCurrentPage(page)}
                            isActive={currentPage === page}
                            className="cursor-pointer"
                          >
                            {page}
                          </PaginationLink>
                        </PaginationItem>
                      );
                    })}
                    <PaginationItem>
                      <PaginationNext
                        onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                        className={currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                      />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              </div>
            )}
          </TabsContent>
        </PermissionControl>

        <PermissionControl permission="players.tabs.tactics">
          <TabsContent value="tatica">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg sm:text-xl">Visão Tática</CardTitle>
                <CardDescription className="text-sm">
                  Visualização tática dos jogadores por posição
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Funcionalidade em desenvolvimento
                </p>
              </CardContent>
            </Card>
          </TabsContent>
        </PermissionControl>

        {canApproveEvaluations && (
          <TabsContent value="aprovacoes">
            <div className="space-y-6">
              <EvaluationApprovalStats />
              <PendingEvaluationsTable />
            </div>
          </TabsContent>
        )}

        <PermissionControl permission="players.tabs.members">
          <TabsContent value="membros">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg sm:text-xl">Membros do Clube</CardTitle>
                <CardDescription className="text-sm">
                  Gerencie os membros e suas funções no clube
                </CardDescription>
              </CardHeader>
              <CardContent>
                <DataTable
                  data={filteredMembers}
                  columns={memberColumns}
                  isLoading={loadingMembers}
                  actions={memberActions}
                  enableSearch={false}
                  />
              </CardContent>
            </Card>
          </TabsContent>
        </PermissionControl>
      </Tabs>

      {/* Modais */}
      <PlayerDialog
        open={playerDialogOpen}
        onOpenChange={setPlayerDialogOpen}
        player={selectedPlayer}
        editMode={editMode}
        clubId={clubId}
      />

      {passwordPlayer && (
        <ChangePasswordDialog
          open={!!passwordPlayer}
          onOpenChange={() => setPasswordPlayer(null)}
          userId={passwordPlayer.user_id!}
          userName={passwordPlayer.name}
        />
      )}
      
      <ConfirmDialog
        open={inactiveConfirmOpen}
        onOpenChange={(open) => {
          setInactiveConfirmOpen(open);
          if (!open) setPlayerToInactivate(null);
        }}
        title="Marcar jogador como inativo"
        description={`Tem certeza que deseja marcar o jogador ${playerToInactivate?.name} como inativo?`}
        confirmText="Confirmar"
        cancelText="Cancelar"
        onConfirm={handleInactivateConfirm}
      />
      
      <ConfirmDialog
        open={deleteConfirmOpen}
        onOpenChange={setDeleteConfirmOpen}
        title="Excluir Jogador"
        description={`Tem certeza que deseja excluir o jogador ${playerToDelete?.name}? Esta ação não pode ser desfeita.`}
        confirmText="Excluir"
        cancelText="Cancelar"
        onConfirm={handleDeleteConfirm}
      />
    </div>
  );
}