import React from 'react';
import { InjuryPreventionDashboard } from '@/components/injury-prevention/InjuryPreventionDashboard';
import { WellnessDataForm } from '@/components/injury-prevention/WellnessDataForm';
import { WorkloadDataForm } from '@/components/injury-prevention/WorkloadDataForm';
import { InjuryHistoryForm } from '@/components/injury-prevention/InjuryHistoryForm';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useCurrentClubId } from '@/context/ClubContext';
import { usePermission } from '@/hooks/usePermission';
import { PermissionControl } from '@/components/PermissionControl';
import { INJURY_PREVENTION_PERMISSIONS } from '@/constants/permissions';
import { 
  Brain, 
  Heart, 
  Activity, 
  TrendingUp, 
  Shield,
  AlertTriangle,
  Target,
  BarChart3,
  ClipboardList,
  History,
  Users
} from 'lucide-react';

export default function InjuryPrevention() {
  const clubId = useCurrentClubId();
  const canView = usePermission(INJURY_PREVENTION_PERMISSIONS.VIEW);
  const canCreate = usePermission(INJURY_PREVENTION_PERMISSIONS.CREATE);

  if (!canView) {
    return (
      <div className="container mx-auto py-6">
        <Card className="p-8 text-center">
          <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-yellow-700">
            Acesso Restrito
          </h3>
          <p className="text-muted-foreground">
            Você não tem permissão para acessar o sistema de prevenção de lesões.
          </p>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="space-y-2">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
            <Brain className="h-6 w-6 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold">IA - Prevenção de Lesões</h1>
            <p className="text-muted-foreground">
              Sistema inteligente de monitoramento e prevenção de lesões
            </p>
          </div>
        </div>
      </div>

      {/* Cards de Benefícios */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="border-l-4 border-l-green-500">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Shield className="h-4 w-4 text-green-600" />
              Prevenção
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">35%</div>
            <p className="text-xs text-muted-foreground">
              Redução nas lesões
            </p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-blue-500">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Activity className="h-4 w-4 text-blue-600" />
              Monitoramento
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">24/7</div>
            <p className="text-xs text-muted-foreground">
              Análise contínua
            </p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-purple-500">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <TrendingUp className="h-4 w-4 text-purple-600" />
              Performance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">40%</div>
            <p className="text-xs text-muted-foreground">
              Melhoria na disponibilidade
            </p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-orange-500">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Target className="h-4 w-4 text-orange-600" />
              Economia
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">R$ 50K+</div>
            <p className="text-xs text-muted-foreground">
              Economia anual
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Tabs Principais */}
      <Tabs defaultValue="dashboard" className="space-y-4">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="dashboard" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Dashboard
          </TabsTrigger>
          <PermissionControl permission={INJURY_PREVENTION_PERMISSIONS.WELLNESS.CREATE}>
            <TabsTrigger value="wellness" className="flex items-center gap-2">
              <Heart className="h-4 w-4" />
              Wellness
            </TabsTrigger>
          </PermissionControl>
          <PermissionControl permission={INJURY_PREVENTION_PERMISSIONS.CREATE}>
            <TabsTrigger value="workload" className="flex items-center gap-2">
              <Activity className="h-4 w-4" />
              Carga de Treino
            </TabsTrigger>
          </PermissionControl>
          <PermissionControl permission={INJURY_PREVENTION_PERMISSIONS.CREATE}>
            <TabsTrigger value="injury-history" className="flex items-center gap-2">
              <History className="h-4 w-4" />
              Histórico de Lesões
            </TabsTrigger>
          </PermissionControl>
          <PermissionControl permission={INJURY_PREVENTION_PERMISSIONS.VIEW}>
            <TabsTrigger value="players" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Por Jogador
            </TabsTrigger>
          </PermissionControl>
          <TabsTrigger value="about" className="flex items-center gap-2">
            <Brain className="h-4 w-4" />
            Como Funciona
          </TabsTrigger>
        </TabsList>

        <TabsContent value="dashboard" className="space-y-4">
          <InjuryPreventionDashboard clubId={clubId} />
        </TabsContent>

        <PermissionControl permission={INJURY_PREVENTION_PERMISSIONS.WELLNESS.CREATE}>
          <TabsContent value="wellness" className="space-y-4">
            <div className="max-w-4xl mx-auto">
              <WellnessDataForm 
                clubId={clubId} 
                onSuccess={() => {
                  // Opcional: mostrar toast de sucesso ou recarregar dados
                }}
              />
            </div>
          </TabsContent>
        </PermissionControl>

        <PermissionControl permission={INJURY_PREVENTION_PERMISSIONS.CREATE}>
          <TabsContent value="workload" className="space-y-4">
            <div className="max-w-4xl mx-auto">
              <WorkloadDataForm 
                clubId={clubId} 
                onSuccess={() => {
                  // Opcional: mostrar toast de sucesso ou recarregar dados
                }}
              />
            </div>
          </TabsContent>
        </PermissionControl>

        <PermissionControl permission={INJURY_PREVENTION_PERMISSIONS.CREATE}>
          <TabsContent value="injury-history" className="space-y-4">
            <div className="max-w-4xl mx-auto">
              <InjuryHistoryForm 
                clubId={clubId} 
                onSuccess={() => {
                  // Opcional: mostrar toast de sucesso ou recarregar dados
                }}
              />
            </div>
          </TabsContent>
        </PermissionControl>

        <PermissionControl permission={INJURY_PREVENTION_PERMISSIONS.VIEW}>
          <TabsContent value="players" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5 text-blue-600" />
                  Análise Individual por Jogador
                </CardTitle>
                <CardDescription>
                  Visualize dados específicos de cada jogador
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Funcionalidade em desenvolvimento</p>
                  <p className="text-sm">
                    Em breve você poderá visualizar dados individuais de cada jogador
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </PermissionControl>

        <TabsContent value="about" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Brain className="h-5 w-5 text-blue-600" />
                  Como a IA Funciona
                </CardTitle>
                <CardDescription>
                  Algoritmos científicos para prevenção
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                    <div>
                      <h4 className="font-medium">Acute:Chronic Workload Ratio</h4>
                      <p className="text-sm text-muted-foreground">
                        Compara carga da última semana vs últimas 4 semanas
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                    <div>
                      <h4 className="font-medium">Wellness Score</h4>
                      <p className="text-sm text-muted-foreground">
                        Analisa sono, fadiga, dor muscular e stress
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-purple-500 rounded-full mt-2"></div>
                    <div>
                      <h4 className="font-medium">Histórico de Lesões</h4>
                      <p className="text-sm text-muted-foreground">
                        Considera lesões anteriores e padrões
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-orange-500 rounded-full mt-2"></div>
                    <div>
                      <h4 className="font-medium">Training Monotony</h4>
                      <p className="text-sm text-muted-foreground">
                        Avalia variabilidade da carga de treino
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5 text-green-600" />
                  Benefícios Comprovados
                </CardTitle>
                <CardDescription>
                  Resultados baseados em pesquisas científicas
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Redução de lesões musculares</span>
                    <span className="font-semibold text-green-600">25-35%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Economia em tratamentos</span>
                    <span className="font-semibold text-blue-600">R$ 30-50K/ano</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Melhoria na disponibilidade</span>
                    <span className="font-semibold text-purple-600">40%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Detecção precoce de riscos</span>
                    <span className="font-semibold text-orange-600">7-14 dias</span>
                  </div>
                </div>

                <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border">
                  <h4 className="font-medium text-blue-900 mb-2">💡 Dica Importante</h4>
                  <p className="text-sm text-blue-800">
                    Para melhores resultados, incentive os jogadores a preencherem 
                    o questionário de wellness diariamente. A consistência dos dados 
                    melhora significativamente a precisão das predições.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>🚀 Próximos Passos</CardTitle>
              <CardDescription>
                Como começar a usar o sistema de prevenção
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 border rounded-lg">
                  <div className="w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center mx-auto mb-2 font-bold">
                    1
                  </div>
                  <h4 className="font-medium mb-1">Configurar Dados</h4>
                  <p className="text-sm text-muted-foreground">
                    Registre dados históricos de lesões e comece a coletar dados de wellness
                  </p>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <div className="w-8 h-8 bg-green-100 text-green-600 rounded-full flex items-center justify-center mx-auto mb-2 font-bold">
                    2
                  </div>
                  <h4 className="font-medium mb-1">Treinar Equipe</h4>
                  <p className="text-sm text-muted-foreground">
                    Ensine jogadores e staff a usar o sistema diariamente
                  </p>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <div className="w-8 h-8 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center mx-auto mb-2 font-bold">
                    3
                  </div>
                  <h4 className="font-medium mb-1">Monitorar Resultados</h4>
                  <p className="text-sm text-muted-foreground">
                    Acompanhe alertas e ajuste cargas conforme recomendações
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
