import React from 'react';
import { PlayerWellnessQuickForm } from '@/components/injury-prevention/PlayerWellnessQuickForm';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useCurrentClubId } from '@/context/ClubContext';
import { 
  Heart, 
  Shield, 
  TrendingUp, 
  Clock,
  CheckCircle,
  AlertTriangle
} from 'lucide-react';

export default function PlayerWellness() {
  const clubId = useCurrentClubId();

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <div className="flex items-center justify-center gap-3">
          <div className="p-2 bg-gradient-to-r from-red-500 to-pink-600 rounded-lg">
            <Heart className="h-6 w-6 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold">Wellness Diário</h1>
            <p className="text-muted-foreground">
              2 minutos que podem prevenir semanas de lesão
            </p>
          </div>
        </div>
      </div>

      {/* Cards de Benefícios */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-4xl mx-auto">
        <Card className="border-l-4 border-l-green-500">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Shield className="h-4 w-4 text-green-600" />
              Prevenção
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">35%</div>
            <p className="text-xs text-muted-foreground">
              Menos lesões
            </p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-blue-500">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <TrendingUp className="h-4 w-4 text-blue-600" />
              Performance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">40%</div>
            <p className="text-xs text-muted-foreground">
              Mais disponibilidade
            </p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-purple-500">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Clock className="h-4 w-4 text-purple-600" />
              Rapidez
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">2min</div>
            <p className="text-xs text-muted-foreground">
              Por dia apenas
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Formulário Principal */}
      <div className="max-w-2xl mx-auto">
        <PlayerWellnessQuickForm 
          clubId={clubId} 
          onSuccess={() => {
            // Opcional: mostrar mensagem de sucesso adicional
          }}
        />
      </div>

      {/* Informações Importantes */}
      <div className="max-w-4xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-yellow-600" />
              Por que é importante?
            </CardTitle>
            <CardDescription>
              Entenda como seus dados ajudam na prevenção de lesões
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <h4 className="font-semibold text-green-700">✅ O que fazemos com seus dados:</h4>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                    Analisamos padrões para detectar riscos de lesão
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                    Alertamos a comissão técnica quando necessário
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                    Ajudamos a ajustar sua carga de treino
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                    Mantemos histórico para análises futuras
                  </li>
                </ul>
              </div>

              <div className="space-y-3">
                <h4 className="font-semibold text-blue-700">🔒 Sua privacidade:</h4>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                    Dados acessíveis apenas pela comissão técnica
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                    Informações não são compartilhadas externamente
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                    Usado exclusivamente para sua saúde e performance
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                    Você pode solicitar seus dados a qualquer momento
                  </li>
                </ul>
              </div>
            </div>

            <div className="mt-6 p-4 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg border border-yellow-200">
              <h4 className="font-medium text-yellow-800 mb-2">💡 Dica Importante</h4>
              <p className="text-sm text-yellow-700">
                Seja honesto nas suas respostas! O sistema só funciona bem com dados reais. 
                Não há problema em ter dias ruins - isso é normal e esperado. O importante 
                é identificar padrões que possam indicar risco de lesão.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* FAQ Rápido */}
      <div className="max-w-4xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle>❓ Perguntas Frequentes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <h4 className="font-medium">Preciso preencher todos os dias?</h4>
                <p className="text-sm text-muted-foreground">
                  Sim, a consistência é fundamental para a precisão do sistema. 
                  Quanto mais dados, melhor a prevenção.
                </p>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium">E se eu esquecer um dia?</h4>
                <p className="text-sm text-muted-foreground">
                  Não tem problema! Você pode preencher dados de dias anteriores 
                  se lembrar como se sentia.
                </p>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium">Os técnicos vão me julgar?</h4>
                <p className="text-sm text-muted-foreground">
                  Não! O objetivo é te ajudar. Ter dias ruins é normal e esperado. 
                  O importante é identificar padrões.
                </p>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium">Quanto tempo leva?</h4>
                <p className="text-sm text-muted-foreground">
                  Apenas 2 minutos por dia. É um pequeno investimento para 
                  uma grande proteção contra lesões.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}