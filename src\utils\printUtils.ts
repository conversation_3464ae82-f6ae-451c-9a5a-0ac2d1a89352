/**
 * Utilitários para impressão de documentos
 */

/**
 * Abre a janela de impressão para um arquivo
 * @param fileUrl URL do arquivo
 * @param fileName Nome do arquivo (opcional)
 */
export function printFile(fileUrl: string, fileName?: string): void {
    try {
        // Criar uma nova janela/aba para o arquivo
        const printWindow = window.open(fileUrl, '_blank');

        if (!printWindow) {
            // Se o popup foi bloqueado, tentar download direto
            downloadFile(fileUrl, fileName);
            return;
        }

        // Aguardar o carregamento do documento e abrir impressão
        printWindow.onload = () => {
            setTimeout(() => {
                try {
                    printWindow.print();
                } catch (error) {
                    console.warn('Não foi possível abrir a impressão automaticamente:', error);
                    // Fallback: manter a janela aberta para impressão manual
                }
            }, 1000); // Aguardar 1 segundo para garantir que o documento carregou
        };

        // Fallback caso onload não funcione
        setTimeout(() => {
            try {
                if (printWindow && !printWindow.closed) {
                    printWindow.print();
                }
            } catch (error) {
                console.warn('Fallback de impressão falhou:', error);
            }
        }, 2000);

    } catch (error) {
        console.error('Erro ao abrir arquivo para impressão:', error);
        // Fallback: fazer download do arquivo
        downloadFile(fileUrl, fileName);
    }
}

/**
 * Faz download de um arquivo
 * @param fileUrl URL do arquivo
 * @param fileName Nome do arquivo (opcional)
 */
export function downloadFile(fileUrl: string, fileName?: string): void {
    try {
        const link = document.createElement('a');
        link.href = fileUrl;
        link.target = '_blank';

        if (fileName) {
            link.download = fileName;
        }

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    } catch (error) {
        console.error('Erro ao fazer download do arquivo:', error);
        // Último recurso: abrir em nova aba
        window.open(fileUrl, '_blank');
    }
}

/**
 * Determina se um arquivo deve ser impresso automaticamente
 * @param fileUrl URL do arquivo
 * @param mimeType Tipo MIME do arquivo (opcional)
 * @returns true se deve imprimir, false se deve apenas baixar
 */
export function shouldPrintFile(fileUrl: string, mimeType?: string): boolean {
    // Extrair extensão do arquivo
    const extension = fileUrl.split('.').pop()?.toLowerCase();

    // Tipos que devem ser impressos (geralmente visualizáveis no navegador)
    const printableTypes = ['pdf'];
    const printableMimeTypes = [
        'application/pdf',
        'text/plain',
        'text/html'
    ];

    // Verificar por extensão
    if (extension && printableTypes.includes(extension)) {
        return true;
    }

    // Verificar por MIME type
    if (mimeType && printableMimeTypes.includes(mimeType)) {
        return true;
    }

    // Para Word, Excel, etc., fazer download para impressão local
    return false;
}

/**
 * Processa o download/impressão de um arquivo baseado no tipo
 * @param fileUrl URL do arquivo
 * @param fileName Nome do arquivo
 * @param mimeType Tipo MIME do arquivo (opcional)
 */
export function handleFileAction(fileUrl: string, fileName: string, mimeType?: string): void {
    if (shouldPrintFile(fileUrl, mimeType)) {
        // Arquivos que podem ser impressos diretamente no navegador
        printFile(fileUrl, fileName);
    } else {
        // Arquivos que precisam ser baixados para impressão local
        downloadFile(fileUrl, fileName);
    }
}